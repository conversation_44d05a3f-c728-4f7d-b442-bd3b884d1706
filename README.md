# DPN Peer App

- [DP<PERSON> Peer App](#dpn-peer-app)
  - [Getting Started](#getting-started)
  - [Build and run Flutter app](#build-and-run-flutter-app)
- [Flutter Rust bridge codegen](#flutter-rust-bridge-codegen)


## Getting Started

To begin, ensure that you have a working installation of the following items:
- [Flutter SDK](https://docs.flutter.dev/get-started/install)
- [Rust language](https://rustup.rs/)
- `flutter_rust_bridge_codegen` [cargo package](https://cjycode.com/flutter_rust_bridge/integrate/deps.html#build-time-dependencies)
- Appropriate [Rust targets](https://rust-lang.github.io/rustup/cross-compilation.html) for cross-compiling to your device
- For Android targets:
    - Install [cargo-ndk](https://github.com/bbqsrc/cargo-ndk#installing)
    - Install [Android NDK 22](https://github.com/android/ndk/wiki/Unsupported-Downloads#r22b), then put its path in one of the `gradle.properties`, e.g.:
        ```
        echo "ANDROID_NDK=.." >> ~/.gradle/gradle.properties
        ```

## Build and run Flutter app

Open up your terminal.

To list emulators:
```
flutter emulators
```
```
2 available emulators:
Pixel_3a_API_34_extension_level_7_arm64-v8a • Pixel_3a_API_34_extension_level_7_arm64-v8a • Google • android
Pixel_6a_API_33                             • Pixel 6a API 33                             • Google • android
```

To launch emulator: 
```
flutter emulators --launch Pixel_6a_API_33
```

To list devices: 
```
flutter devices
```

```
Found 2 connected devices:
  sdk gphone64 arm64 (mobile) • emulator-5554 • android-arm64 • Android 14 (API 34) (emulator)
  macOS (desktop)             • macos         • darwin-arm64  • macOS 14.1.1 23B81 darwin-arm64
```

Then run app with device:
```
flutter run -d emulator-5554
```
When running above command, a task to build Rust code at `rust` folder is run. To build them successfully, dependencies must be installed. Please checkout for more information [README.md](./rust/README.md).

# Flutter Rust bridge codegen
Unless you want to generate code again due to change of `subnet-dpn-peernode`, you don't need it to build `subnet-dpn-peer-app`.Building code Rust for Flutter requires [subnet-dpn-peernode](https://github.com/unicornultralabs/subnet-dpn-peernode) repository, it is private repo, contact admin for access. 

Built Dart interfaces are at `subnet-dpn-peer-app/lib`.
Built libs are at `subnet-dpn-peer-app/android/app/src/main/jniLibs`.

Guide to build Rust code is at: [https://github.com/unicornultralabs/subnet-dpn-peernode/blob/develop/bridge/README.md](https://github.com/unicornultralabs/subnet-dpn-peernode/blob/develop/bridge/README.md)