name: u2u_dpn
version: 1.3.5+91
publish_to: none
description: A new Flutter project.
environment:
  sdk: ">=3.1.5 <4.0.0"

dependencies:
  cupertino_icons: ^1.0.2
  get: 4.6.6
  flutter:
    sdk: flutter
  flutter_svg: ^2.0.9
  # flutter_screenutil: ^5.9.0
  url_launcher: ^6.2.1
  simple_gradient_text: ^1.3.0
  overlay_dialog: ^0.2.2
  ffi: ^2.1.0
  flutter_rust_bridge: ^1.82.4
  freezed_annotation: ^2.4.1
  web_socket_channel: ^2.4.0
  auto_size_text_field: ^2.2.2
  flutter_launcher_icons: ^0.13.1
  dio: ^5.4.0
  pretty_dio_logger: ^1.3.1
  nb_utils: ^7.1.3
  intl: ^0.19.0
  otp_text_field: ^1.1.3
  pinput: ^3.0.1
  firebase_core: ^3.0.8
  firebase_auth: ^5.3.4
  firebase_storage: ^12.3.7
  firebase_dynamic_links: ^6.0.0
  google_sign_in: ^6.1.6
  the_apple_sign_in: ^1.1.1
  hex: ^0.2.0
  pull_to_refresh: ^2.0.0
  wakelock_plus: ^1.3.2
  showcaseview: ^3.0.0
  get_storage:
  info_popup: ^4.3.1
  # audioplayers: ^5.2.1
  # firebase_core_desktop: ^1.0.2
  http: ^1.2.0
  window_manager: ^0.3.8
  flutter_switch: ^0.3.2
  # uni_links2: ^0.6.0+2
  toastification: ^1.2.1
  uni_links_desktop: ^0.1.6
  internet_connection_checker: ^1.0.0+1
  flutter_background: ^1.2.0
  flutter_local_notifications: ^17.0.0
  permission_handler: ^11.3.0
  # workmanager: ^0.5.2
  upgrader: ^10.0.1
  # flutter_app_info: ^3.0.3
  # singular_flutter_sdk: ^1.3.3
  desktop_window: ^0.4.0
  desktop_webview_auth: ^0.0.15
  flutter_telegram_web_app: ^0.0.1
  web_ffi: ^0.7.2
  inject_js: ^2.0.0
  device_info_plus: ^9.1.2
  firebase_analytics: ^11.3.6
  app_links: ^6.0.1

  qr_flutter: ^4.1.0
  firebase_messaging: ^15.1.6
  # country_codes: ^3.3.0
  uuid: ^4.5.1
  connectivity_plus: ^6.1.3
  logger: ^2.5.0
  sentry_flutter: ^8.14.2
  flutter_speed_test_plus: ^1.0.10
  flutter_background_service: ^5.1.0
  # workmanager:
  #   git:
  #     url: https://github.com/fluttercommunity/flutter_workmanager.git
  #     path: workmanager
  #     ref: main
  # background_fetch: ^1.3.7
  # wireguard_2govpn: ^0.1.0

dev_dependencies:
  build_runner: ^2.4.7
  ffigen: ^9.0.1
  flutter_lints: ^2.0.0
  flutter_test:
    sdk: flutter
  freezed: ^2.4.5

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/config/
    - assets/sound/
  fonts:
    - family: Manrope
      fonts:
        - asset: assets/fonts/Manrope-Bold.ttf
        - asset: assets/fonts/Manrope-ExtraBold.ttf
        - asset: assets/fonts/Manrope-ExtraLight.ttf
        - asset: assets/fonts/Manrope-Light.ttf
        - asset: assets/fonts/Manrope-Medium.ttf
        - asset: assets/fonts/Manrope-Regular.ttf
        - asset: assets/fonts/Manrope-SemiBold.ttf
    - family: MonaSansExpanded
      fonts:
        - asset: assets/fonts/MonaSansExpanded-Regular.ttf
        - asset: assets/fonts/MonaSansExpanded-Bold.ttf
flutter_launcher_icons:
  android: true
  ios: false
  remove_alpha_ios: true
  image_path: "assets/images/logo_ circle.png"
  min_sdk_android: 21
  color: "#1D2334C7"
