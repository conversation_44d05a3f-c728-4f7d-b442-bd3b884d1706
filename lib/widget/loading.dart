import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:u2u_dpn/utils/app_color.dart';

class LoadingScreen extends StatefulWidget {
  final double? height;
  const LoadingScreen({Key? key, this.height}) : super(key: key);

  @override
  State<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends State<LoadingScreen> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      height: widget.height ?? Get.height,
      child: const Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppColor.neutral300, strokeWidth: 2.0),
            SizedBox(
              height: 8,
            ),
            Text('Loading...', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: AppColor.neutral200)),
          ],
        ),
      ),
    );
  }
}
