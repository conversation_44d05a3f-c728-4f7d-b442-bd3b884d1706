import 'package:flutter/widgets.dart';
//
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
import 'package:url_launcher/url_launcher.dart';

class InfoComunity extends StatelessWidget {
  const InfoComunity({super.key});

  @override
  Widget build(BuildContext context) {
    return inforTwTele();
  }

  Widget inforTwTele() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        CustomText(
          text: LocaleKeys.join_U2DPN.tr,
          fontSize: 18,
        ).paddingOnly(bottom: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            itemCommunity(
                // text: 'Twitter',
                icon: AppImages.icon_twitter,
                onTap: () {
                  launchUrl(
                    Uri.parse("https://x.com/u2dpn_network"),
                    mode: LaunchMode.externalApplication,
                  );
                }),
            const SizedBox(
              width: 8,
            ),
            itemCommunity(
                // text: 'Telegram',
                icon: AppImages.icon_telegram,
                onTap: () {
                  launchUrl(
                    Uri.parse("https://t.me/u2u_xyzchat"),
                    mode: LaunchMode.externalApplication,
                  );
                }),
            const SizedBox(
              width: 8,
            ),
            itemCommunity(
                // text: 'Youtube',
                icon: AppImages.icon_youtube,
                onTap: () {
                  launchUrl(
                    Uri.parse("https://www.youtube.com/playlist?list=PLiZq-QQcRRnffubgxPGJCj0UA_D0Y5p0R"),
                    mode: LaunchMode.externalApplication,
                  );
                })
          ],
        )
      ],
    );
  }

  itemCommunity({String text = '', required Function onTap, required String icon}) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        decoration: BoxDecoration(
            // border: Border.all(
            //   color: AppColor.neutral500,
            //   width: 1,
            // ),
            borderRadius: BorderRadius.circular(30),
            color: AppColor.neutral600),
        child: Image.asset(
          icon,
          width: 12,
          height: 12,
        ).paddingOnly(right: 6),
      ).onTap(onTap),
    );
  }
}
