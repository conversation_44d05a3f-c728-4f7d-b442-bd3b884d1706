// ignore_for_file: overridden_fields, library_private_types_in_public_api

import 'package:flutter/material.dart';

import 'package:u2u_dpn/utils/app_color.dart';

class PopupMenuCustomer<T> extends StatelessWidget {
  const PopupMenuCustomer(
      {super.key, required this.appBarHeight,
      this.child,
      this.onSelected,
      this.valueSelected,
      required this.getTitle,
      required this.data,
      this.titleTextStyle,
      required this.checkValueChoosed,
      this.onOpened});

  final List<dynamic> data;
  final PopupMenuItemSelected<T>? onSelected;
  final Function(int) getTitle;

  final TextStyle? titleTextStyle;
  final String? valueSelected;

  final double appBarHeight;

  final Widget? child;
  final String checkValueChoosed;
  final Function()? onOpened;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<T>(
      color: AppColor.neutral600,
      onOpened: onOpened,
      onSelected: onSelected,
      itemBuilder: (context) => [
        for (var i = 0; i < data.length; i++) menuItem(i, getTitle(i), checkValueChoosed),
      ],
      icon: child != null ? null : const Icon(Icons.expand_less),
      offset: Offset(0.0, appBarHeight),
      shape: const RoundedRectangleBorder(
        side: BorderSide(color: AppColor.neutral500),
        borderRadius: BorderRadius.all(
          Radius.circular(8),
          // bottomLeft: const Radius.circular(8.0).r,
          // bottomRight: const Radius.circular(8.0).r,
          // topLeft: const Radius.circular(8.0).r,
          // topRight: const Radius.circular(8.0).r,
        ),
      ),
      child: child,
    );
  }

  PopupMenuItem<T> menuItem(dynamic value, String title, String checkValueChoosed) {
    bool enable = valueSelected == null ? false : title == valueSelected!;
    return CustomPopupMenuItem<T>(
      value: value,
      bgColorEnable: Colors.red,
      enabled: enable,
      child: Text(
        title,
        style: titleTextStyle ??
            TextStyle(fontWeight: FontWeight.w500, fontSize: 14, color: (title == checkValueChoosed) ? AppColor.neutral400 : AppColor.neutral200),
      ),
    );
  }
}

class CustomPopupMenuItem<T> extends PopupMenuItem<T> {
  const CustomPopupMenuItem({super.key, 
    this.enabled = false,
    required this.child,
    this.bgColorEnable,
    this.value,
  }) : super(enabled: enabled, child: child, value: value);

  @override
  final bool enabled;
  @override
  final Widget child;
  final Color? bgColorEnable;
  @override
  final T? value;

  @override
  _CustomPopupMenuItemState<T> createState() => _CustomPopupMenuItemState<T>();
}

class _CustomPopupMenuItemState<T> extends PopupMenuItemState<T, CustomPopupMenuItem<T>> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(8),
      onTap: handleTap,
      child: Container(
        color: widget.enabled ? widget.bgColorEnable : null,
        child: super.build(context),
      ),
    );
  }
}
