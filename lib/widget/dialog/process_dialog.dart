// ignore_for_file: use_build_context_synchronously, deprecated_member_use

import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:overlay_dialog/overlay_dialog.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/widget/custom_bottom_sheet.dart';

import 'package:u2u_dpn/widget/custom_raisebutton.dart';
import 'package:u2u_dpn/widget/ultil.dart';

class ProgressDialog {
  static DialogWidget? _dialog;

  static _initIfNeeded() {
    _dialog = DialogWidget.custom(child: const CustomerProgressDialog());
  }

  static show(BuildContext context) {
    _initIfNeeded();
    if (_dialog != null) {
      DialogHelper().show(context, _dialog!);
    }
  }

  static showTransactions(BuildContext context) {
    _dialog =
        DialogWidget.custom(child: const CustomerProgressDialogTransactions());
    if (_dialog != null) {
      DialogHelper().show(context, _dialog!);
    }
  }

  static hide(BuildContext context) {
    DialogHelper().hide(context);
  }

  static Future<void> hideIfNeeded(BuildContext context) async {
    DialogHelper().hide(context);
    return Future.value();
  }

  static void showDialogQRcode(
      BuildContext context, String code, String linkCode) {
    CustomBottomSheet.showModalNotFullScreenWithHeight(
      context,
      SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                DefaultTextStyle(
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w400,
                    color: AppColor.neutral200,
                  ),
                  child: Text(
                    '${LocaleKeys.share.tr} ${LocaleKeys.referral_code.tr.toLowerCase()}',
                    textAlign: TextAlign.left,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    Get.back();
                  },
                  icon: const Icon(
                    Icons.close,
                    color: AppColor.neutral400,
                  ),
                )
              ],
            ),
            const SizedBox(height: 24),
            if (linkCode != '')
              QrImageView(
                data: linkCode,
                version: QrVersions.auto,
                backgroundColor: Colors.white,
                size: 195.0,
              ),
            const SizedBox(height: 24),
            Row(
              children: [
                DefaultTextStyle(
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: AppColor.neutral400,
                  ),
                  child: Text(
                    LocaleKeys.referral_code.tr,
                    textAlign: TextAlign.left,
                  ),
                ),
                const SizedBox()
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: const BoxDecoration(
                color: AppColor.neutral900,
                borderRadius: BorderRadius.all(
                  Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  DefaultTextStyle(
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFFFFFFFF),
                    ),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: Get.width * 0.35),
                      child: Text(
                        code,
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: code.toString()))
                          .then((value) async {
                        Utils.showSnackbar(
                            context, LocaleKeys.copied_successfully.tr);
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24),
                        color: AppColor.neutral600,
                      ),
                      child: DefaultTextStyle(
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColor.neutral200,
                        ),
                        child: Text(
                          LocaleKeys.copy.tr,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                DefaultTextStyle(
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: AppColor.neutral400,
                  ),
                  child: Text(
                    LocaleKeys.link_referral_code.tr,
                    textAlign: TextAlign.left,
                  ),
                ),
                const SizedBox()
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: const BoxDecoration(
                color: AppColor.neutral900,
                borderRadius: BorderRadius.all(
                  Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  DefaultTextStyle(
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFFFFFFFF),
                    ),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: Get.width * 0.55),
                      child: Text(
                        linkCode,
                        textAlign: TextAlign.right,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Clipboard.setData(
                              ClipboardData(text: linkCode.toString()))
                          .then((value) async {
                        Utils.showSnackbar(
                            context, LocaleKeys.copied_successfully.tr);
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24),
                        color: AppColor.neutral600,
                      ),
                      child: DefaultTextStyle(
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColor.neutral200,
                        ),
                        child: Text(
                          LocaleKeys.copy.tr,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],
        ).paddingAll(16),
      ),
      height: Get.height * 0.9,
    );
  }

  static void showDialogNotification(
      {required Widget content,
      String? title,
      Function()? onPressed,
      Function()? onPressedCancell,
      bool barrierDismissible = true,
      bool isShowCancel = true,
      bool isRow = true,
      bool isSwitch = false,
      String? canTitle,
      String? saveTitle,
      double margin = 40,
      RxBool? loadbutton,
      double paddingHoz = 16,
      double paddingVer = 16}) {
    RxBool? loadbuttonvalue = loadbutton ?? false.obs;
    Get.dialog(
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: margin),
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xff242332),
                  borderRadius: BorderRadius.all(
                    Radius.circular(20),
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: paddingHoz, vertical: paddingVer),
                  child: Material(
                    color: const Color(0xff242332),
                    child: Column(
                      children: [
                        if (title != null) ...{
                          const SizedBox(height: 10),
                          Text(
                            title,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFFFFFFFF),
                            ),
                          ),
                          const SizedBox(height: 15),
                        },
                        content,
                        const SizedBox(height: 24),
                        //Buttons
                        isRow
                            ? Row(
                                children: [
                                  if (isShowCancel)
                                    Expanded(
                                        child: CostomRaisedButtom(
                                      name: canTitle ?? LocaleKeys.cancel.tr,
                                      function: onPressedCancell ??
                                          () {
                                            Get.back();
                                          },
                                      colorText: Colors.white,
                                      colorsGradient: const [
                                        Color(0xFF2D2C3B),
                                        Color(0xFF2D2C3B)
                                      ],
                                    )),
                                  const SizedBox(width: 10),
                                  Expanded(
                                      child: Obx(() => loadbuttonvalue.value
                                          ? const CustomRaisedButtonLoading()
                                          : CostomRaisedButtom(
                                              name: saveTitle ??
                                                  LocaleKeys.confirm.tr,
                                              function: onPressed ??
                                                  () {
                                                    Get.back();
                                                  },
                                            ))),
                                ],
                              ).paddingSymmetric(horizontal: 12)
                            : isSwitch
                                ? Column(
                                    children: [
                                      Obx(() => loadbuttonvalue.value
                                          ? const CustomRaisedButtonLoading()
                                          : CostomRaisedButtom(
                                              name: saveTitle ??
                                                  LocaleKeys.confirm.tr,
                                              function: onPressed ??
                                                  () {
                                                    Get.back();
                                                  },
                                            )),
                                      if (isShowCancel)
                                        const SizedBox(height: 12),
                                      if (isShowCancel)
                                        CostomRaisedButtom(
                                          name:
                                              canTitle ?? LocaleKeys.cancel.tr,
                                          function: onPressedCancell ??
                                              () {
                                                Get.back();
                                              },
                                          colorText: Colors.white,
                                          colorsGradient: const [
                                            Color(0xFF2D2C3B),
                                            Color(0xFF2D2C3B)
                                          ],
                                        ),
                                    ],
                                  ).paddingSymmetric(horizontal: 16)
                                : Column(
                                    children: [
                                      if (isShowCancel)
                                        CostomRaisedButtom(
                                          name:
                                              canTitle ?? LocaleKeys.cancel.tr,
                                          function: onPressedCancell ??
                                              () {
                                                Get.back();
                                              },
                                          colorText: Colors.white,
                                          colorsGradient: const [
                                            Color(0xFF2D2C3B),
                                            Color(0xFF2D2C3B)
                                          ],
                                        ),
                                      if (isShowCancel)
                                        const SizedBox(height: 12),
                                      Obx(() => loadbuttonvalue.value
                                          ? const CustomRaisedButtonLoading()
                                          : CostomRaisedButtom(
                                              name: saveTitle ??
                                                  LocaleKeys.confirm.tr,
                                              function: onPressed ??
                                                  () {
                                                    Get.back();
                                                  },
                                            )),
                                    ],
                                  ).paddingSymmetric(horizontal: 16),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        barrierDismissible: barrierDismissible);
  }
}

class CustomerProgressDialog extends StatelessWidget {
  const CustomerProgressDialog({
    Key? key,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: true,
      child: Stack(
        children: [
          Container(
            color: const Color(0xFF0A0A0B).withOpacity(0.5),
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
          ),
          Center(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 0.5, sigmaY: 0.5),
              child: Container(
                color: const Color(0x01000000),
                child: FractionallySizedBox(
                  child: Material(
                    color: Colors.transparent,
                    child: Container(
                      padding: const EdgeInsets.all(24.0),
                      decoration: const BoxDecoration(
                          color: Color(0xFF242332),
                          borderRadius:
                              BorderRadius.all(Radius.circular(10.0))),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(
                            backgroundColor:
                                const Color(0xFFA7A4A4).withOpacity(0.5),
                            color: const Color(0xFFFFFFFF),
                            strokeWidth: 3,
                          ),
                          const SizedBox(height: 8),
                          Text('loading...',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(color: const Color(0xFFFFFFFF))),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CustomerProgressDialogTransactions extends StatelessWidget {
  const CustomerProgressDialogTransactions({super.key});

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: true,
      child: Stack(
        children: [
          Container(
            color: const Color(0xFF0A0A0B).withOpacity(0.5),
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
          ),
          Center(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 0.5, sigmaY: 0.5),
              child: Container(
                width: Get.width - 32,
                color: const Color(0x01000000),
                child: FractionallySizedBox(
                  child: Material(
                    color: Colors.transparent,
                    child: Container(
                      padding: const EdgeInsets.all(24.0),
                      decoration: const BoxDecoration(
                          color: Color(0xFF242332),
                          borderRadius:
                              BorderRadius.all(Radius.circular(10.0))),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(height: 20),
                          CircularProgressIndicator(
                            backgroundColor:
                                const Color(0xFFA7A4A4).withOpacity(0.5),
                            color: const Color(0xFFFFFFFF),
                            strokeWidth: 5,
                            strokeCap: StrokeCap.round,
                          ),
                          const SizedBox(height: 20),
                          Column(
                            children: [
                              Text(
                                LocaleKeys.transactions_is_progress.tr,
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFFFFFFFF),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                LocaleKeys.it_should_take.tr,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF65636F),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
