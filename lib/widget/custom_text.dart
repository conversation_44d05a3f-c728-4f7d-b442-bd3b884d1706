// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';

import '../utils/app_utils.dart';

class CustomText extends StatelessWidget {
  final double paddingVertical;
  final double paddingHorizontal;
  final dynamic text;
  final Color? color;
  final FontWeight? fontWeight;
  final double? fontSize;
  final TextAlign? textAlign;
  final double? textHeight;
  final int? maxLine;
  final TextDecoration? textDecoration;
  final TextOverflow? overflow;
  final double? letterSpacing;
  final String? fontFamily;
  final FontStyle? fontStyle;
  const CustomText(
      {super.key,
      this.paddingHorizontal = 0,
      this.paddingVertical = 0,
      this.color,
      this.fontSize,
      this.fontWeight,
      required this.text,
      this.textAlign,
      this.maxLine,
      this.textHeight,
      this.textDecoration,
      this.overflow,
      this.letterSpacing,
      this.fontFamily,
      this.fontStyle});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: paddingHorizontal, vertical: paddingVertical),
      child: text is Widget
          ? text
          : Text(
              text.toString(),
              textAlign: textAlign,
              maxLines: maxLine,
              overflow: overflow,

              style: TextStyle(
                  wordSpacing: 1.3,
                  fontFamily: fontFamily,
                  letterSpacing: letterSpacing ?? 0.5,
                  height: textHeight ?? 1.3,
                  color: color ?? Colors.white,
                  fontSize: fontSize ?? 14,
                  fontWeight: fontWeight ?? FontWeight.w500,
                  decoration: textDecoration,
                  fontStyle: fontStyle,
                  overflow: overflow),
              
            ),
    );
  }
}

ShaderMask CustomTextLineGradient({
  required String text,
  double? fontSize,
  FontWeight? fontWeight,
}) {
  return ShaderMask(
      shaderCallback: (Rect rect) {
        return AppColor.customLineGradientGreen.createShader(rect);
      },
      child: CustomText(
        text: text,
        fontSize: fontSize ?? 12,
        fontWeight: fontWeight ?? FontWeight.w500,
      ));
}
