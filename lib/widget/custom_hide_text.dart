class CustomHideTex {
  static String? hideMiddleText(String? text, {int toIndexText = 6}) {
    if (text != null && text.length <= 20) {
      return text;
    } else if (text != null && text != '') {
      return '${text.substring(0, toIndexText)}...${text.substring(text.length - toIndexText)}';
    } else {
      return text;
    }
  }

  static String? hideStartText(String? input, int maxLength) {
    if (input != null && input != '') {
      if (input.length <= maxLength || input.length < 20) {
        return input;
      } else {
        return "...${input.substring(input.length - maxLength)}";
      }
    } else {
      return input;
    }
  }
}
