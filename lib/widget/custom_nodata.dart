// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';

import 'package:get/get_utils/get_utils.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/widget.dart';

// class CustomNodata extends StatelessWidget {
//   const CustomNodata({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Center(
//       child: Column(
//         children: [
//           Image.asset(
//             AppImages.message,
//             width: 135.w,
//           ),
//           CustomText(
//             text: 'There is no data available.',
//             color: AppColor.neutral200,
//             fontSize: 16,
//             textAlign: TextAlign.center,
//           ),
//         ],
//       ).paddingOnly(top: 48 ),
//     );
//   }
// }

Center CustomNodata({
  String? textLine1,
  String? textLine2,
  bool isButton = false,
  String? nameButton,
  String? icon,
  Function()? onTap,
}) {
  return Center(
    child: Column(
      children: [
        Image.asset(
          icon ?? AppImages.nodata,
          width: 80,
        ).paddingBottom(24),
        CustomText(
          text: textLine1 ?? LocaleKeys.no_data.tr,
          color: AppColor.neutral300,
          fontSize: 16,
          textAlign: TextAlign.center,
        ),
        CustomText(
          text: textLine2 ?? '',
          color: AppColor.neutral200,
          fontSize: 16,
          textAlign: TextAlign.center,
        ),
        isButton ? CostomRaisedButtom(name: nameButton ?? '', function: onTap ?? () {}).paddingAll(16) : const SizedBox()
      ],
    ).paddingOnly(
      top: 50,
    ),
  );
}
