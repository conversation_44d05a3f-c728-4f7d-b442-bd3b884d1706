// import 'package:flutter/material.dart';
// 
// import 'package:otp_text_field/otp_text_field.dart';
// import 'package:otp_text_field/style.dart';
// import 'package:u2u_dpn/utils/app_color.dart';
//
// class CustomOTPTextField extends StatelessWidget {
//   final controller;
//   final onChange;
//   const CustomOTPTextField({super.key, required this.controller, required this.onChange});
//
//   @override
//   Widget build(BuildContext context) {
//     return OTPTextField(
//         controller: controller,
//         length: 6,
//         width: double.infinity,
//         textFieldAlignment: MainAxisAlignment.spaceAround,
//         fieldWidth: 45.w,
//         otpFieldStyle: OtpFieldStyle(
//             backgroundColor: AppColor.neutral700,
//             focusBorderColor: AppColor.neutral400,
//             borderColor: Colors.yellow,
//             disabledBorderColor: Colors.green,
//             enabledBorderColor: Colors.transparent),
//         fieldStyle: FieldStyle.box,
//         outlineBorderRadius: 10,
//         style: TextStyle(fontSize: 17, color: Colors.white),
//         onChanged: onChange,
//         onCompleted: (pin) {
//           logger.d("Completed: " + pin);
//         });
//   }
// ignore_for_file: file_names

// }
