import 'package:flutter/material.dart';
import 'package:info_popup/info_popup.dart';
import 'package:u2u_dpn/utils/app_color.dart';

// ignore: must_be_immutable
class CustomPopupInfo extends StatelessWidget {
  String value;
  Widget child;

  CustomPopupInfo({super.key, required this.child, required this.value});

  @override
  Widget build(BuildContext context) {
    return InfoPopupWidget(
        contentTheme: const InfoPopupContentTheme(infoTextStyle: TextStyle(fontSize: 16, color: Colors.white), infoContainerBackgroundColor: AppColor.neutral700),
        contentTitle: value,
        child: child);
  }
}
