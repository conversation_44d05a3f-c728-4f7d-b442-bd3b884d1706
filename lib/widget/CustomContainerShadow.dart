// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';
import 'package:u2u_dpn/utils/app_color.dart';

class CustomContainerShadow extends StatelessWidget {
  final Widget child;
  const CustomContainerShadow({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: const BoxDecoration(
          boxShadow: [BoxShadow(color: Color(0xff323247), offset: Offset(0, 6))],
          borderRadius: BorderRadius.all(Radius.circular(16)),
          gradient: LinearGradient(
            colors: [Color(0xff2F2F47), Color(0xff72729A)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(1),
          child: Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(16)),
                color: AppColor.neutralBlack,
              ),
              child: child),
        ));
  }
}
