import 'dart:convert';
import 'dart:io';
import 'dart:math';

//import 'package:device_info_plus/device_info_plus.dart';
// import 'package:delightful_toast/delight_toast.dart';
// import 'package:delightful_toast/toast/components/toast_card.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:toastification/toastification.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/model/user_detail_model.dart';
import 'package:u2u_dpn/utils/app_images.dart';

class Utils {
  static String convertToStringJson(dynamic value) {
    return jsonEncode(value).toString();
  }

  static String convertUnit(double num) {
    String formatNumber(double number, int fractionDigits) {
      // Truncate to desired decimal places without rounding
      double factor = pow(10, fractionDigits).toDouble();
      double truncated = (number * factor).floorToDouble() / factor;
      // Convert to string and replace '.' with ','
      String formatted = truncated.toStringAsFixed(fractionDigits).replaceAll(',', '.');
      // Remove trailing ",0" if the number is effectively an integer
      if (formatted.endsWith('.0')) {
        formatted = formatted.substring(0, formatted.length - 2);
      }
      return formatted;
    }

    if (num >= 1000000 && num < 1000000000) {
      return "${formatNumber(num / 1000000, 3)}M";
    } else if (num >= 1000 && num < 1000000) {
      return "${formatNumber(num / 1000, 2)}K";
    } else if (num >= 1000000000) {
      return "${formatNumber(num / 1000000000, 3)}B";
    } else {
      return formatNumber(num, 2);
    }
  }

  static String calculatorPlatformFeeFromTie(String tier, int value) {
    try{
    double val = value.toDouble();
    switch (tier) {
      case 'Exclusive':
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 7.05) / 100).toInt())));
      case 'Premier':
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 9) / 100).toInt())));
      case 'Elite':
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 10.95) / 100).toInt())));
      case 'VIP':
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 13.05) / 100).toInt())));
      case 'Member':
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 14.25) / 100).toInt())));
      default:
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 15) / 100).toInt())));
    }
    }catch(e){
      return '<0.001';
    }
  }

  static String calculatorReferralFeeFromTie(String tier, int value) {
    try{
    double val = value.toDouble();
    return convertToDecimal(double.parse(Utils.convertSzaboToU2U((((val * 5) / 100)).toInt())));
  }
  catch(e){
    return '<0.001';
  }
  }

  static String calculatorNetAmountFromTie(String tier, int value) {
    try{
    double val = value.toDouble();
    switch (tier) {
      case 'Exclusive':
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 87.95) / 100).toInt())));
      case 'Premier':
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 86) / 100).toInt())));
      case 'Elite':
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 84.05) / 100).toInt())));
      case 'VIP':
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 81.95) / 100).toInt())));
      case 'Member':
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 80.75) / 100).toInt())));
      default:
        return convertToDecimal(double.parse(Utils.convertSzaboToU2U(((val * 80) / 100).toInt())));
    }}catch(e){
      return '<0.001';
    }
  }

  static String getUserID(User? user) {
    if (user == null) {
      return "...";
    } else {
      if (user.username == "") {
        return shortenUsername(user.depositAddr, 7, showlastString: true);
      } else {
        return shortenUsername(user.depositAddr, 7, showlastString: true);
      }
    }
  }

  static dynamic convertStringToJson(String data) {
    return jsonDecode(data.replaceAll('/', ''));
  }

  static int startToToday(int data) {
    var value =
        (DateTime.fromMillisecondsSinceEpoch(DateTime.now().millisecondsSinceEpoch).difference(DateTime.fromMillisecondsSinceEpoch((data) * 1000)))
            .inSeconds;
    //  logger.d(DateTime.fromMillisecondsSinceEpoch(DateTime.now().millisecondsSinceEpoch).difference(DateTime.fromMillisecondsSinceEpoch((data) * 1000)));
    return value;
  }

  static int startToTodayMicro(int data) {
    var value = (DateTime.fromMicrosecondsSinceEpoch(DateTime.now().millisecondsSinceEpoch).difference(DateTime.fromMicrosecondsSinceEpoch((data))))
        .inSeconds;
    //  logger.d(DateTime.fromMillisecondsSinceEpoch(DateTime.now().millisecondsSinceEpoch).difference(DateTime.fromMillisecondsSinceEpoch((data) * 1000)));
    return value;
  }

  static String shortenUsername(String? input, int maxLength, {bool showlastString = false}) {
    if (input == null) {
      return "...";
    }

    if (input.length <= maxLength) {
      return input;
    }
    String threeLastChars = '';
    if (showlastString) threeLastChars = input.substring(input.length - 5);
    String firstNineChars = input.substring(0, maxLength);
    String shortenedString = "$firstNineChars...$threeLastChars";
    return shortenedString;
  }

  static showSnackbar(BuildContext context, String text, {bool? isError = false}) {
    // return isError == true
    //     ? Get.snackbar(
    //         '',
    //         text,
    //         titleText: SizedBox(height: 3 ),
    //         padding: EdgeInsets.only(right: 16.w, left: 16.w, bottom: 8 ),
    //         margin: EdgeInsets.only(right: 16.w, left: 16.w, bottom: 16 ),
    //         colorText: Colors.white,
    //         backgroundColor: Colors.red.withOpacity(0.6),
    //         snackPosition: SnackPosition.BOTTOM,
    //       )
    //     : Get.snackbar(
    //         '',
    //         text,
    //         titleText: SizedBox(height: 3 ),
    //         padding: EdgeInsets.only(right: 16.w, left: 16.w, bottom: 8 ),
    //         margin: EdgeInsets.only(right: 16.w, left: 16.w, bottom: 16 ),
    //         colorText: Colors.white,
    //         backgroundColor: Colors.grey.withOpacity(0.6),
    //         snackPosition: SnackPosition.BOTTOM,
    //       );
    toastification.dismissAll();
    return isError == true
        ? toastification.show(
            context: context,
            type: ToastificationType.error,
            autoCloseDuration: const Duration(seconds: 3),
            animationDuration: const Duration(milliseconds: 300),
            alignment: Alignment.bottomCenter,
            showProgressBar: false,
            title: Text(
              text,
              style: const TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 14,
              ),
            ),
          )
        : toastification.show(
            context: context,
            type: ToastificationType.success,
            autoCloseDuration: const Duration(seconds: 3),
            animationDuration: const Duration(milliseconds: 300),
            alignment: Alignment.bottomCenter,
            showProgressBar: false,
            icon: Image.asset(
              AppImages.logo,
              width: 16,
              fit: BoxFit.contain,
            ),
            title: Text(
              text,
              style: const TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 14,
              ),
            ),
          );
    // return Fluttertoast.showToast(
    //     msg: text,
    //     toastLength: Toast.LENGTH_SHORT,
    //     gravity: gravity ?? ToastGravity.CENTER,
    //     timeInSecForIosWeb: 1,
    //     backgroundColor: AppColor.neutral600,
    //     textColor: AppColor.primaryGreen500,
    //     fontSize: 16.0);
  }

  static String intToTime(int value) {
    value = (value / 2.5).round();
    int h, m, s;

    h = value ~/ 3600;

    m = ((value - h * 3600)) ~/ 60;

    s = value - (h * 3600) - (m * 60);

    var hourLeft = h.toString().length < 2 ? '0$h' : h.toString();

    var minuteLeft = m.toString().length < 2 ? '0$m' : m.toString();

    var secondsLeft = s.toString().length < 2 ? '0$s' : s.toString();
    var result = '';
    if (hourLeft == '00') {
      result = '$minuteLeft:$secondsLeft';
    } else {
      result = '$hourLeft:$minuteLeft:$secondsLeft';
    }

    return result;
  }

  static String formattedTimeDuration(int timeInSecond) {
    if (timeInSecond < 3600) {
      int sec = timeInSecond % 60;
      int min = (timeInSecond / 60).floor();
      String minute = min.toString().length <= 1 ? "0$min" : "$min";
      String second = sec.toString().length <= 1 ? "0$sec" : "$sec";
      return "$minute : $second";
    } else {
      return '${(Duration(seconds: timeInSecond))}'.split('.')[0].padLeft(8, '0');
    }
  }

  static String converterHexadecimal(String hexString) {
    // Convert hexadecimal string to decimal string
    hexString = hexString.split('0x').last;
    double decimalValue = (int.parse(hexString, radix: 16) / 1e18);
    String decimalString = decimalValue.toString();
    return decimalString;
  }

  static String subString(String value, {int decimal = 2}) {
    if (value.toString().split('.').last.length > 3) {
      return '${value.toString().split('.').first}.${value.toString().split('.').last.substring(0, decimal)}';
    }
    return value;
  }

  static String kbToGb(int kilobytes) {
    // 1 GB = 1024 MB, 1 MB = 1024 KB
    double megabytes = kilobytes / (1024);
    if (megabytes > 1024) {
      var gigabytes = megabytes / 1024;
      if (gigabytes.toString().split('.').last.length > 3) {
        return '${gigabytes.toString().split('.').first}.${gigabytes.toString().split('.').last.substring(0, 2)} GB';
      }
      return '$gigabytes GB';
    }
    if (megabytes.toString().split('.').last.length > 3) {
      return '${megabytes.toString().split('.').first}.${megabytes.toString().split('.').last.substring(0, 2)} MB';
    }
    return '$megabytes MB';
  }

  static String convertSzaboToU2U(int szabo) {
    double u2u = szabo / 1000000;

    if (u2u > 0 && u2u < 0.001) {
      return '< 0.001';
    } else {
      if (u2u.toString().split('.').last.length > 4) {
        return '${u2u.toString().split('.').first}.${u2u.toString().split('.').last.substring(0, 3)}';
      }
      return u2u.toString();
    }
  }

  static String kmbGenerator(int szabo) {
    double num = convertSzaboToU2U(szabo).toDouble();

    if (num > 999 && num < 99999) {
      return "${(num / 1000).toStringAsFixed(1)} K";
    } else if (num > 99999 && num < 999999) {
      return "${(num / 1000).toStringAsFixed(0)} K";
    } else if (num > 999999 && num < 999999999) {
      return "${(num / 1000000).toStringAsFixed(1)} M";
    } else if (num > 999999999) {
      return "${(num / 1000000000).toStringAsFixed(1)} B";
    } else {
      return num.toString();
    }
  }

  convertTimefromMicro(int startTime, {double fontSize = 12, int index = 0}) {
    int hour = 0;
    int minutes = 0;
    int seconds = 0;
// mili
    DateTime now = DateTime.now();
    int secondPresent = now.millisecondsSinceEpoch ~/ 1000;
    int second = secondPresent - startTime;
//micro
    // int secondPresent = now.microsecondsSinceEpoch;
    // logger.d(secondPresent);
    // logger.d(startTime);
    // int second = (secondPresent - 1712303427) ~/ 1000000;

    hour = (second ~/ 3600);

    minutes = ((second - hour * 3600)) ~/ 60;
    seconds = second - (hour * 3600) - (minutes * 60);
    logger.d("${hour.toString().padLeft(2, "0")}:${minutes.toString().padLeft(2, "0")}:${seconds.toString().padLeft(2, "0")}");
    if (hour < 100) {
      return ("${hour.toString().padLeft(2, "0")}:${minutes.toString().padLeft(2, "0")}:${seconds.toString().padLeft(2, "0")}");
    } else {
      return ("${hour.toString()}:${minutes.toString().padLeft(2, "0")}:${seconds.toString().padLeft(2, "0")}");
    }
  }

  static String convertTimestamptoDatetime(int millis) {
    var dt = DateTime.fromMillisecondsSinceEpoch(millis * 1000);
    var d24 = DateFormat('dd/MM/yyyy, HH:mm').format(dt);
    return d24;
  }

  static String convertMicroTimestamptoDatetime(int micro) {
    var dt = DateTime.fromMicrosecondsSinceEpoch(micro);
    var d24 = DateFormat('dd/MM/yyyy, HH:mm').format(dt);
    return d24;
  }

  static String convertDatetimetoTimestamp(int millis) {
    var dt = DateTime.fromMillisecondsSinceEpoch(millis * 1000);
    var d24 = DateFormat('dd/MM/yyyy, HH:mm').format(dt);
    return d24;
  }

  static String convertTimestamptoDate(int millis) {
    var dt = DateTime.fromMillisecondsSinceEpoch(millis * 1000);
    var d24 = DateFormat('dd/MM/yyyy').format(dt);
    return d24;
  }

  static String convertTimestamptoTime(int millis) {
    var dt = DateTime.fromMillisecondsSinceEpoch(millis * 1000);
    var d24 = DateFormat('HH:mm').format(dt);
    return d24;
  }

  static void debugPrint(dynamic data) {
    if (kDebugMode) {
      logger.d(data.toString());
    }
  }

  String formatDurationWithDayHourMinute(int totalSeconds) {
    int minutes = (totalSeconds ~/ 60) % 60;
    int hours = (totalSeconds ~/ 3600) % 24;
    int days = totalSeconds ~/ 86400;

    return '${days.toString().padLeft(2, '0')}d '
        '${hours.toString().padLeft(2, '0')}h '
        '${minutes.toString().padLeft(2, '0')}m';
  }

  static String convertToDecimal(double value) {
    String fractionPart = value.toStringAsFixed(3).toString(); // Tách phần thập phân
    // String result;
    // if (fractionPart.length >= 3) {
    //   result = fractionPart.substring(0, 3); // Lấy 3 kí tự đầu tiên sau dấu phẩy
    // } else {
    //   result = fractionPart.padRight(3, '0'); // Điền thêm số 0 nếu không đủ 3 kí tự
    // }
    return fractionPart;
  }

  static bool isNullOrEmpty(dynamic obj) =>
      obj == '' || obj == null || (((obj is String && obj.toLowerCase() == 'null') || obj is List || obj is Map) && obj.isEmpty);
  int indexDevice = 0;
  Future deviceInfo() async {
    final deviceInfoPlugin = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;

      return androidDeviceInfo.brand;
    }
    if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;

      return iosInfo.model;
    }

    return '';
  }

  Future deviceId() async {
    final deviceInfoPlugin = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfoPlugin.androidInfo;

      return androidDeviceInfo.id;
    }
    if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;

      return iosInfo.identifierForVendor;
    }

    return '';
  }

  static Future copyFunction(BuildContext context, String text) async {
    await Clipboard.setData(ClipboardData(text: text)).then((value) {
      // ignore: use_build_context_synchronously
      Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
    });
  }
  // Map<String, dynamic> _readAndroidBuildData(AndroidDeviceInfo build) {
  //   return <String, dynamic>{
  //     'version.securityPatch': build.version.securityPatch,
  //     'version.sdkInt': build.version.sdkInt,
  //     'version.release': build.version.release,
  //     'version.previewSdkInt': build.version.previewSdkInt,
  //     'version.incremental': build.version.incremental,
  //     'version.codename': build.version.codename,
  //     'version.baseOS': build.version.baseOS,
  //     'board': build.board,
  //     'bootloader': build.bootloader,
  //     'brand': build.brand,
  //     'device': build.device,
  //     'display': build.display,
  //     'fingerprint': build.fingerprint,
  //     'hardware': build ardware,
  //     'host': build ost,
  //     'id': build.id,
  //     'manufacturer': build.manufacturer,
  //     'model': build.model,
  //     'product': build.product,
  //     // 'supported32BitAbis': build.supported32BitAbis,
  //     // 'supported64BitAbis': build.supported64BitAbis,
  //     'supportedAbis': build.supportedAbis,
  //     'tags': build.tags,
  //     'type': build.type,
  //     'isPhysicalDevice': build.isPhysicalDevice,

  //     'displaySizeInches': ((build.displayMetrics.sizeInches * 10).roundToDouble() / 10),
  //     'displayWidthPixels': build.displayMetrics.widthPx,
  //     'displayWidthInches': build.displayMetrics.widthInches,
  //     'displayHeightPixels': build.displayMetrics eightPx,
  //     'displayHeightInches': build.displayMetrics eightInches,
  //     'displayXDpi': build.displayMetrics.xDpi,
  //     'displayYDpi': build.displayMetrics.yDpi,
  //     'serialNumber': build.serialNumber,
  //   };
  // }

  // Map<String, dynamic> _readIosDeviceInfo(IosDeviceInfo data) {
  //   return <String, dynamic>{
  //     'name': data.name,
  //     'systemName': data.systemName,
  //     'systemVersion': data.systemVersion,
  //     'model': data.model,
  //     'localizedModel': data.localizedModel,
  //     'identifierForVendor': data.identifierForVendor,
  //     'isPhysicalDevice': data.isPhysicalDevice,
  //     'utsname.sysname:': data.utsname.sysname,
  //     'utsname.nodename:': data.utsname.nodename,
  //     'utsname.release:': data.utsname.release,
  //     'utsname.version:': data.utsname.version,
  //     'utsname.machine:': data.utsname.machine,
  //   };
  // }

  // Map<String, dynamic> _readLinuxDeviceInfo(LinuxDeviceInfo data) {
  //   return <String, dynamic>{
  //     'name': data.name,
  //     'version': data.version,
  //     'id': data.id,
  //     'idLike': data.idLike,
  //     'versionCodename': data.versionCodename,
  //     'versionId': data.versionId,
  //     'prettyName': data.prettyName,
  //     'buildId': data.buildId,
  //     'variant': data.variant,
  //     'variantId': data.variantId,
  //     'machineId': data.machineId,
  //   };
  // }

  // Map<String, dynamic> _readWebBrowserInfo(WebBrowserInfo data) {
  //   return <String, dynamic>{
  //     // 'browserName': describeEnum(data.browserName),
  //     'appCodeName': data.appCodeName,
  //     'appName': data.appName,
  //     'appVersion': data.appVersion,
  //     'deviceMemory': data.deviceMemory,
  //     'language': data.language,
  //     'languages': data.languages,
  //     'platform': data.platform,
  //     'product': data.product,
  //     'productSub': data.productSub,
  //     'userAgent': data.userAgent,
  //     'vendor': data.vendor,
  //     'vendorSub': data.vendorSub,
  //     'hardwareConcurrency': data ardwareConcurrency,
  //     'maxTouchPoints': data.maxTouchPoints,
  //   };
  // }

  // Map<String, dynamic> _readMacOsDeviceInfo(MacOsDeviceInfo data) {
  //   return <String, dynamic>{
  //     'computerName': data.computerName,
  //     'hostName': data ostName,
  //     'arch': data.arch,
  //     'model': data.model,
  //     'kernelVersion': data.kernelVersion,
  //     'majorVersion': data.majorVersion,
  //     'minorVersion': data.minorVersion,
  //     'patchVersion': data.patchVersion,
  //     'osRelease': data.osRelease,
  //     'activeCPUs': data.activeCPUs,
  //     'memorySize': data.memorySize,
  //     'cpuFrequency': data.cpuFrequency,
  //     'systemGUID': data.systemGUID,
  //   };
  // }

  // Map<String, dynamic> _readWindowsDeviceInfo(WindowsDeviceInfo data) {
  //   return <String, dynamic>{
  //     'numberOfCores': data.numberOfCores,
  //     'computerName': data.computerName,
  //     'systemMemoryInMegabytes': data.systemMemoryInMegabytes,
  //     'userName': data.userName,
  //     'majorVersion': data.majorVersion,
  //     'minorVersion': data.minorVersion,
  //     'buildNumber': data.buildNumber,
  //     'platformId': data.platformId,
  //     'csdVersion': data.csdVersion,
  //     'servicePackMajor': data.servicePackMajor,
  //     'servicePackMinor': data.servicePackMinor,
  //     'suitMask': data.suitMask,
  //     'productType': data.productType,
  //     'reserved': data.reserved,
  //     'buildLab': data.buildLab,
  //     'buildLabEx': data.buildLabEx,
  //     'digitalProductId': data.digitalProductId,
  //     'displayVersion': data.displayVersion,
  //     'editionId': data.editionId,
  //     'installDate': data.installDate,
  //     'productId': data.productId,
  //     'productName': data.productName,
  //     'registeredOwner': data.registeredOwner,
  //     'releaseId': data.releaseId,
  //     'deviceId': data.deviceId,
  //   };
  // }
}
