// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:u2u_dpn/utils/app_color.dart';

class CustomBottomSheet {
  static void showModal(
    BuildContext context,
    Widget child, {
    bool isScrollControlled = true,
    double initialChildSize = 0.95,
    Function? bottomSheetClose,
  }) {
    showModalBottomSheet(
        isScrollControlled: isScrollControlled,
        backgroundColor: Colors.transparent,
        barrierColor: AppColor.neutral800.withOpacity(0.4),
        context: context,
        builder: (BuildContext context) {
          return DraggableScrollableSheet(
              initialChildSize: initialChildSize,
              builder: (BuildContext context, ScrollController scrollController) {
                return ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                    child: Container(
                        color: Colors.transparent,
                        // padding: EdgeInsets.only(
                        // bottom: Platform.isAndroid
                        //     ? kBottomNavigationBarHeight
                        //     : 0),
                        child: Column(
                          children: [
                            Container(
                              width: 85,
                              height: 4,
                              decoration: const BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(16),
                                    topRight: Radius.circular(16),
                                  ),
                                  color: Color(0xFF4C556B)),
                              margin: const EdgeInsets.only(bottom: 6),
                            ),
                            Expanded(
                                child: ClipRRect(
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(16),
                                      topRight: Radius.circular(16),
                                    ),
                                    child: Container(
                                      color: AppColor.neutral800,
                                      child: child,
                                    )))
                          ],
                        )));
              });
        }).then((value) {
      if (bottomSheetClose != null) bottomSheetClose();
    });
  }

  static void showModalFullScreen(
    BuildContext context,
    Widget child, {
    bool isScrollControlled = true,
    double initialChildSize = 1,
  }) {
    showModalBottomSheet(
        isScrollControlled: isScrollControlled,
        backgroundColor: AppColor.neutral800,
        barrierColor: AppColor.neutral800.withOpacity(0.4),
        context: context,
        builder: (BuildContext context) {
          return DraggableScrollableSheet(
              initialChildSize: initialChildSize,
              builder: (BuildContext context, ScrollController scrollController) {
                return ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  // borderRadius: BorderRadius.circular(16.0),
                  child: Container(
                    color: Colors.transparent,
                    // padding: EdgeInsets.only(
                    //     bottom: Platform.isAndroid
                    //         ? kBottomNavigationBarHeight
                    //         : 0),
                    child: child,
                  ),
                );
              });
        });
  }

  static void showModalNotFullScreenWithHeight(BuildContext context, Widget child,
      {double? height, bool isUnfocus = false, Function(dynamic)? onValueThen}) {
    showModalBottomSheet(
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        //barrierColor: AppColor.neutral800,
        context: context,
        builder: (BuildContext context) {
          return Padding(
            // 👇 thêm padding né bàn phím
            padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
            child: ClipRRect(
                borderRadius: const BorderRadius.only(topRight: Radius.circular(24), topLeft: Radius.circular(24)),
                child: Container(
                    height: height ?? Get.height * 0.75,
                    color: AppColor.neutral800,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 64,
                          height: 5,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            //borderRadius: BorderRadius.only(topRight: Radius.circular(15), topLeft: Radius.circular(15)),
                            color: const Color(0xFF383846),
                          ),
                          margin: const EdgeInsets.only(top: 16),
                        ),
                        Expanded(
                            child: GestureDetector(
                          onTap: isUnfocus
                              ? () {
                                  FocusScope.of(context).unfocus();
                                }
                              : () {},
                          child: ClipRRect(
                              borderRadius: const BorderRadius.only(topRight: Radius.circular(15), topLeft: Radius.circular(15)),
                              child: Container(
                                color: AppColor.neutral800,
                                child: child,
                              )),
                        ))
                      ],
                    ))),
          );
        }).then(onValueThen ?? (value) {});
  }

  static void showModalNotFullScreen(
    BuildContext context,
    Widget child,
  ) {
    showModalBottomSheet(
        backgroundColor: Colors.transparent,
        //barrierColor: AppColor.neutral800.withOpacity(0.4),
        context: context,
        builder: (context) {
          return Container(
            decoration: const BoxDecoration(
              color: AppColor.neutral800,
              borderRadius: BorderRadius.vertical(top: Radius.circular(24.0)),
            ),
            width: double.infinity,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                    width: 64,
                    height: 5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      //borderRadius: BorderRadius.only(topRight: Radius.circular(15), topLeft: Radius.circular(15)),
                      color: const Color(0xFF383846),
                    ),
                    margin: const EdgeInsets.only(top: 16)),
                Container(
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
                    color: AppColor.neutral800,
                  ),
                  child: child,
                ),
              ],
            ),
          );
        });
  }
}
