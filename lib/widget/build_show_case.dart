import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';

Widget buildshowCase(BuildContext context, String title, Function()? onTap,
    {String? number, String? numberEnd, String? textButton, bool showSkip = true, double? width}) {
  return Container(
    width: width ?? 270,
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(16),
      // gradient: AppColor.customLineGradientGreen
      color: AppColor.neutral600,
    ),
    child: Column(
      children: [
        Text(title,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColor.neutral200,
            )),
        const Divider(
          color: AppColor.neutral500,
          thickness: 0.0,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            number != null
                ? RichText(
                    text: TextSpan(
                      text: number,
                      style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Color(0xffFFFFFF)),
                      children: <TextSpan>[
                        TextSpan(
                          text: '/${numberEnd ?? '3'}',
                          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Color(0xff65636F)),
                        ),
                      ],
                    ),
                  )
                : const SizedBox(),
            Row(
              children: [
                if (showSkip)
                  InkWell(
                    onTap: () {
                      ShowCaseWidget.of(context).dismiss();
                    },
                    child: Text(LocaleKeys.skip.tr,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: AppColor.neutral200,
                        )),
                  ).paddingOnly(right: 16),
                InkWell(
                  onTap: onTap,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 9),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(colors: [
                        AppColor.primaryBlue500,
                        Color(0xFF1AF7A9),
                      ]),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: Text(textButton ?? LocaleKeys.next.tr,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        )),
                  ),
                )
              ],
            )
          ],
        )
      ],
    ),
  );
}
