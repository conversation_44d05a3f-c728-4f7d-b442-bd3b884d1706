// ignore_for_file: file_names, deprecated_member_use

import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

import '../utils/app_utils.dart';
import 'custom_text.dart';

// ignore: non_constant_identifier_names
CustomDropdownTextField(List<Items> list, ValueChanged<Items?>? onChanged, Items value) {
  return PopupMenuButton<Items>(
      color: AppColor.neutral600,
      elevation: 0,
      shape: const RoundedRectangleBorder(
        side: BorderSide(color: AppColor.neutral500),
        borderRadius: BorderRadius.all(
          Radius.circular(8),
        ),
      ),
      // splashRadius: 8,
      itemBuilder: (context) {
        return list
            .map((item) => PopupMenuItem<Items>(
                value: item,
                //phần show list
                child: Row(
                  children: [
                    // SvgPicture.asset(
                    //   item.icon,
                    //   width: 24,
                    // ).paddingOnly(right: 2),
                    CustomText(
                      color: AppColor.neutral200,
                      // fontWeight: FontWeight.w400,
                      // paddingHorizontal: 4,
                      text: item.name,
                    ),
                  ],
                )))
            .toList();
      },
      onSelected: onChanged,

      //phần hiện ra
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // SvgPicture.asset(value.icon).paddingOnly(top: 6, bottom: 6, right: 2),
          CustomText(
            // fontWeight: FontWeight.w600,
            // paddingHorizontal: 4.w,
            color: AppColor.neutral200,
            text: value.name,
          ).paddingOnly(right: 8),
          SvgPicture.asset(
            AppImages.arrow_down,
            width: 16,
            color: AppColor.neutral400,
          )
          // Icon(
          //   Icons.keyboard_arrow_down_sharp,
          //   color: AppColor.neutral400,
          //   size: 18,
          // ),
        ],
      ));
}

class Items {
  // String icon;
  String name;
  Items({required this.name});

  static List<Items> listItems = [
    Items(name: 'All status'),
    Items(name: 'Two'),
    Items(name: 'Three'),
  ];
}
