// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/utils/app_color.dart';

class CostomRaisedButtom extends StatelessWidget {
  final String name;
  final VoidCallback function;
  final List<Color>? colorsGradient;
  final Color? colorText;
  final bool? isHintButton;
  final bool? iconLeft;
  //final String? svgImage;
  final Widget? iconButton;
  final double? paddingIconStar;
  final double? widthButtom;
  final TextOverflow? overflow;

  const CostomRaisedButtom(
      {Key? key,
      required this.name,
      required this.function,
      this.colorsGradient,
      this.colorText,
      this.isHintButton = false,
      this.iconLeft = true,
      this.iconButton,
      //this.svgImage,
      this.paddingIconStar,
      this.widthButtom,
      this.overflow})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48.0,
      width: widthButtom ?? Get.width - 32,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: colorsGradient ??
              (isHintButton == false
                  ? <Color>[
                      AppColor.primaryBlue500,
                      AppColor.primaryGreen500,
                    ]
                  : <Color>[
                      AppColor.primaryBlue500.withOpacity(0.6),
                      AppColor.primaryGreen500.withOpacity(0.6),
                    ]),
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: colorsGradient == null
            ? [
                BoxShadow(
                    blurRadius: 15,
                    color: isHintButton == false ? AppColor.primaryGreen500.withOpacity(0.5) : Colors.transparent,
                    offset: const Offset(1, 1))
              ]
            : null,
      ),
      child: ElevatedButton(
        onPressed: isHintButton == true ? () {} : function,
        style: ElevatedButton.styleFrom(
            foregroundColor: Colors.transparent,
            disabledBackgroundColor: Colors.transparent,
            surfaceTintColor: Colors.transparent,
            backgroundColor: Colors.transparent,
            shadowColor: Colors.transparent),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (iconLeft == false) iconButton.paddingRight(8),
            //if (svgImage != null) svgImage ?? ''),
            if (iconLeft == false)
              SizedBox(
                width: paddingIconStar ?? 0,
              ),
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: widthButtom != null ? widthButtom! - 122 : Get.width - 122),
              child: Text(name,
                  overflow: overflow ?? TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: colorText ?? const Color(0xFF1B1A28),
                  )),
            ),
            if (iconLeft == true) iconButton.paddingLeft(8)
          ],
        ),
      ),
    );
  }
}

class CustomRaisedButtonLoading extends StatelessWidget {
  final List<Color>? colorsGradient;
  final Color? colorText;

  final Widget? iconButton;

  const CustomRaisedButtonLoading({Key? key, this.colorsGradient, this.colorText, this.iconButton}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48.0,
      width: MediaQuery.of(context).size.width - 32,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: colorsGradient ??
              (<Color>[
                AppColor.primaryBlue500,
                AppColor.primaryGreen500,
              ]),
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: colorsGradient == null ? [BoxShadow(blurRadius: 15, color: AppColor.primaryGreen500.withOpacity(0.5))] : null,
      ),
      child: Center(
        child: SizedBox(
          width: 17,
          height: 17,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            backgroundColor: AppColor.neutral300.withOpacity(0.3),
            color: AppColor.neutral400,
          ),
        ),
      ),
    );
  }
}
