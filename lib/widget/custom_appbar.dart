import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

// ignore: non_constant_identifier_names
AppBar CustomAppBarTitle(BuildContext context, {String title = '', Function()? function, List<Widget>? actions}) {
  return AppBar(
    // title: const Text(''),
    backgroundColor: const Color(0xFF1B1A28),
    title: CustomText(
      text: title,
      fontSize: 16,
      color: Colors.white,
    ),
    leading: InkWell(
        onTap: function ??
            () {
              Navigator.pop(context);
            },
        borderRadius: BorderRadius.circular(99),
        child: const Icon(
          Icons.arrow_back_rounded,
          color: Colors.white,
          size: 24,
        )),
    actions: actions,
    // leading: InkWell(
    //   borderRadius: BorderRadius.circular(99),
    //   onTap: (){Navigator.pop(context);},
    //   child: SvgPicture.asset(AppImages.icon_arrorw_back, width: 24,),
    // ),
    centerTitle: true,
    elevation: 0,
  );
}

Widget backIcon() {
  return InkWell(
      onTap: () {
        Get.back();
      },
      borderRadius: BorderRadius.circular(99),
      child: const Icon(
        Icons.arrow_back_rounded,
        color: Colors.white,
        size: 24,
      ));
}

// ignore: non_constant_identifier_names
AppBar CustomAppbarWithLeading(BuildContext context, String title, {Function()? function, List<Widget>? actions, Color? bgColor}) {
  return AppBar(
    automaticallyImplyLeading: false,
    backgroundColor: bgColor ?? AppColor.neutral900,
    title: InkWell(
        onTap: () {
          Navigator.pop(context);
        },
        borderRadius: BorderRadius.circular(99),
        child: Row(
          children: [
            const Icon(
              Icons.arrow_back_rounded,
              color: Colors.white,
              size: 24,
            ).paddingRight(12),
            CustomText(
              text: title,
              fontSize: 18,
            )
          ],
        )),
    leadingWidth: 0,
    actions: actions,
    elevation: 0,
  );
}
