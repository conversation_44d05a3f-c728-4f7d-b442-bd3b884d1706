// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/data/app_config.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/widget/ultil.dart';
import 'package:url_launcher/url_launcher.dart';

class IconCopy extends StatelessWidget {
  final String value;
  const IconCopy({super.key, required this.value});

  @override
  Widget build(BuildContext context) {
    return const Icon(
      Icons.copy,
      color: AppColor.neutral300,
      size: 16,
    ).onTap(() async {
      Clipboard.setData(ClipboardData(text: value)).then((value) {
        Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
      });
    }).paddingLeft(5);
  }
}

class OntapOpenLink extends StatelessWidget {
  final Widget child;
  final String value;
  final bool isAddress;
  const OntapOpenLink({super.key, required this.child, this.isAddress = false, required this.value});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: child,
    ).onTap(() {
      if (isAddress) {
        launchUrl(
          Uri.parse('${AppConfig.instance.scanURL}/address/$value'),
          mode: LaunchMode.externalApplication,
        );
      } else {
        launchUrl(
          Uri.parse('${AppConfig.instance.scanURL}/transaction/$value'),
          mode: LaunchMode.externalApplication,
        );
      }
    });
  }
}
