import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../utils/app_utils.dart';
import 'custom_text.dart';

class CustomCellSetting extends StatelessWidget {
  final String title;
  final String icon;
  final VoidCallback onTap;
  final Color? textColor;
  final Color? imgColor;
  final bool? isShowIcon;
  const CustomCellSetting(
      {super.key,
      required this.icon,
      required this.title,
      required this.onTap,
      this.textColor,
      this.imgColor,
      this.isShowIcon});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            children: [
              Image.asset(
                icon,
                width: 24,
                color: imgColor,
              ).paddingOnly(right: 8),
              CustomText(
                text: title,
                fontSize: 14,
                color: textColor,
              ),
            ],
          ),
          isShowIcon == true
              ? Image.asset(
                  AppImages.arrow_up_linear,
                  width: 24,
                )
              : const SizedBox()
        ],
      ).paddingOnly(top: 12, bottom: 12),
    );
  }
}
