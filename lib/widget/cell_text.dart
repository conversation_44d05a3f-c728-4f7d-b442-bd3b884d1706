import 'package:flutter/cupertino.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

Widget cellText(String title, Widget infor, {IconData? icon, Color? color}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Row(
        children: [
          CustomText(
            text: title,
            textAlign: TextAlign.center,
            fontSize: 14,
            color: color ?? AppColor.neutral300,
          ),
          if (icon != null)
            const SizedBox(
              width: 6,
            ),
          if (icon != null)
            Icon(
              icon,
              size: 16,
              color: const Color(0xff2D2C3B),
            )
        ],
      ),
      infor,
    ],
  );
}
