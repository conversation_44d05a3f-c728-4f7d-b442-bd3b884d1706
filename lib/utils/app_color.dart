// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';

class AppColor {
  static const Color neutral100 = Color(0xffF0F0F0);
  static const Color neutral200 = Color(0xffDEDEDE);
  static const Color neutral300 = Color(0xffA7A4A4);
  static const Color neutral400 = Color(0xff65636F);
  static const Color neutral500 = Color(0xff383846);
  static const Color neutral600 = Color(0xff2D2C3B);
  static const Color neutral700 = Color(0xff242332);
  static const Color neutral800 = Color(0xff1B1A28);
  static const Color neutral900 = Color(0xff171423);
  static const Color neutralBlack = Color(0xff120E21);

  static const Color primaryBlue500 = Color(0xff08CDDA);
  static const Color primaryBlue400 = Color(0xff40E8E5);
  static const Color primaryBlue200 = Color(0xff9AFBE9);

  static const Color primaryGreen100 = Color(0xffD1FEDC);
  static const Color primaryGreen500 = Color(0xff1AF7A9);
  static const Color primaryGreen600 = Color(0xff13D4A3);
  static const Color primaryGreen700 = Color(0xff0DB198);
  static const Color primaryGreen900 = Color(0xff047176);

  static const Color ErrorColor300 = Color(0xffE34D4D);
  static const Color highLightColor300 = Color(0xffFFDE72);
  static const Color information300 = Color(0xff6DB7FC);

  static const Color borderContainerDarkmMode = Color(0xff2D2C3B);
  static const Color brownColor = Color(0xffB76519);

  static const Color member = neutral200;
  static const Color vip = highLightColor300;
  static const Color elite = Color(0xFF8FCC1E);
  static const Color premier = Color(0xFF9AFBE9);
  static const Color exclusive = Color(0xFFDAABFA);

  // static LinearGradient CustomLineGradientGreen() {
  //   return LinearGradient(
  //       begin: Alignment.centerLeft,
  //       end: Alignment.centerRight,
  //       colors: [
  //         Color(0xff08CDDA),
  //         Color(0xff1AF7A9),
  //       ]);
  // }

  static LinearGradient customLineGradientGreen = const LinearGradient(colors: [
    primaryBlue500,
    primaryGreen500,
  ]);
  static LinearGradient customLineGradientBlack = const LinearGradient(colors: [
    Color(0xff1D1C2B),
    Color(0xff2C2B3D),
  ], begin: Alignment.topCenter, end: Alignment.bottomCenter);
}
