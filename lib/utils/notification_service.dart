// ignore_for_file: prefer_const_constructors

import 'dart:typed_data';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:u2u_dpn/app/routes/app_pages.dart';
import 'package:u2u_dpn/main.dart';

/// The service used to display notifications and handle callbacks when the user taps on the notification.
///
/// This is a singleton. Just call NotificationService() to get the singleton.
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();

  factory NotificationService() => _instance;

  late FlutterLocalNotificationsPlugin plugin;

  NotificationService._internal() {
    final initializationSettings = InitializationSettings(
      android: AndroidInitializationSettings('@mipmap/launcher_icon_u2u_dpn'),
      iOS: DarwinInitializationSettings(),
    );

    plugin = FlutterLocalNotificationsPlugin();
    plugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (details) {
        if (details.id == 0) {
          Get.toNamed(Routes.DASH_BOARD);
        } else {
          Get.toNamed(Routes.NOTIFICATION);
        }
      },
      onDidReceiveBackgroundNotificationResponse: (details) async {
        if (details.id == 0) {
          Get.toNamed(Routes.DASH_BOARD);
        } else {
          Get.toNamed(Routes.NOTIFICATION);
        }
      },
    );
  }

  Future<void> newNotification(
    String msg,
    bool vibration, {
    int? id,
    bool? ongoing,
    bool? autoCancel,
  }) async {
    // Define vibration pattern
    var vibrationPattern = Int64List(4);
    vibrationPattern[0] = 0;
    vibrationPattern[1] = 1000;
    vibrationPattern[2] = 5000;
    vibrationPattern[3] = 2000;

    AndroidNotificationDetails androidNotificationDetails;

    const channelName = 'Text messages';

    androidNotificationDetails = AndroidNotificationDetails(
      channelName,
      channelName,
      icon: '@mipmap/launcher_icon_u2u_dpn',
      importance: Importance.max,
      priority: Priority.max,
      ongoing: ongoing ?? true,
      autoCancel: autoCancel ?? false,
      vibrationPattern: vibration ? vibrationPattern : null,
      enableVibration: vibration,
    );

    var iOSPlatformChannelSpecifics = DarwinNotificationDetails();
    var notificationDetails = NotificationDetails(android: androidNotificationDetails, iOS: iOSPlatformChannelSpecifics);

    try {
      await plugin.show(id ?? 0, 'U2DPN', msg, notificationDetails);
    } catch (ex) {
      logger.d(ex);
    }
  }
}
