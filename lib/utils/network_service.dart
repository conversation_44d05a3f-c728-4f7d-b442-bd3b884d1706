import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';

class NetworkService {
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _subscription;
  ConnectionController connectionController = Get.put(ConnectionController(), permanent: true);

  void startMonitoring() {
    _subscription = _connectivity.onConnectivityChanged.listen((results) async {
      if (results.any((result) => result != ConnectivityResult.none)) {
       await connectionController.getloactionBonus();
      }
    });
  }

  void stopMonitoring() {
    _subscription?.cancel();
  }
}
