// ignore_for_file: non_constant_identifier_names

// ignore: implementation_imports
import 'package:dio/src/response.dart';

import 'package:u2u_dpn/data/api_docs.dart';
import 'package:u2u_dpn/data/provider/my_callback.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/widget/ultil.dart';

class ApiRepository {
  static final MyCallBack callBack = MyCallBack();

  // // User
  // Future<Response?> loginByDevice() async {
  //   // var body = {"device_type": Utils().indexDevice, "device_info": (await Utils().deviceInfo()).toString()};
  //   var body = {
  //     "device_type": 0,
  //     "device_info":
  //         "{\r\n    \"version\": \"Android 11\",\r\n    \"board\": \"board\",\r\n    \"bootloader\": \"bootloader\",\r\n    \"brand\": \"brand\",\r\n    \"device\": \"device\",\r\n    \"display\": \"display\",\r\n    \"fingerprint\": \"fingerprint\",\r\n    \"hardware\": \"hardware\",\r\n    \"host\": \"host\",\r\n    \"id\": \"id\",\r\n    \"manufacturer\": \"manufacturer\",\r\n    \"model\": \"model\",\r\n    \"product\": \"product\",\r\n    \"supported32_bit_abis\": [\r\n        \"abi1\",\r\n        \"abi2\"\r\n    ],\r\n    \"supported64_bit_abis\": [\r\n        \"abi3\",\r\n        \"abi4\"\r\n    ],\r\n    \"supported_abis\": [\r\n        \"abi5\",\r\n        \"abi6\"\r\n    ],\r\n    \"tags\": \"tags\",\r\n    \"type_device\": \"type_device\",\r\n    \"is_physical_device\": true,\r\n    \"system_features\": [\r\n        \"feature1\",\r\n        \"feature2\"\r\n    ],\r\n    \"display_metrics\": \"metrics\",\r\n    \"serial_number\": \"123456789\"\r\n}\r\n"
  //   };

  //   var res = await callBack.post(
  //     url: ApiDocs.convertURL(ApiDocs.loginByInfo),
  //     data: body,
  //   );
  //   return res;
  // }

  Future<Response?> loginBySSO(dynamic payload) async {
    var body = payload;

    var res = await callBack.post(
      url: ApiDocs.convertURL(ApiDocs.loginBySSO),
      data: body,
    );
    return res;
  }

  Future<Response?> refreshToken() async {
    var body = {
      "access_token": SettingConfig.token,
      "refresh_token": SettingConfig.refreshToken
    };
    var res = await callBack.post(
        url: ApiDocs.convertURL(ApiDocs.refreshToken),
        data: body,
        isUseToken: true);
    return res;
  }

  Future<Response?> createCode({String? referralCode}) async {
    var body = {"referral_code": referralCode};

    return await callBack.post(
      url: ApiDocs.convertURL(ApiDocs.createCode),
      data: body,
      isUseToken: true,
    );
  }

  Future<Response?> linkCode({String? referralCode}) async {
    var body = {"referral_code": referralCode};

    return await callBack.post(
      url: ApiDocs.convertURL(ApiDocs.linkCode),
      data: body,
      isUseToken: true,
    );
  }

  Future<Response?> userDetail() async {
    return await callBack.get(
      url: ApiDocs.convertURL(ApiDocs.userDetail),
      isUseToken: true,
    );
  }

  Future<Response?> getShowTask() async {
    return await callBack.get(
      url: ApiDocs.getShowTask,
    );
  }

  // Reward
  Future<Response?> claim() async {
    return await callBack.post(
      url: ApiDocs.convertURL(ApiDocs.claim),
      isUseToken: true,
    );
  }

  Future<Response?> claimHistory() async {
    return await callBack.get(
      url: ApiDocs.convertURL(ApiDocs.claimHistory),
      isUseToken: true,
    );
  }

  Future<Response?> overview() async {
    return await callBack.get(
      url: ApiDocs.convertURL(ApiDocs.overview),
      isUseToken: true,
    );
  }

  Future<Response?> updateWithdrawAdress(String adress) async {
    var body = {"withdrawal_addr": adress};
    return await callBack.post(
      url: ApiDocs.convertURL(ApiDocs.updateWithdrawAdress),
      data: body,
      isUseToken: true,
    );
  }

  // Referral
  Future<Response?> referralOverview() async {
    return await callBack.get(
      url: ApiDocs.convertURL(ApiDocs.referralOverview),
      isUseToken: true,
    );
  }

  Future<Response?> get_total_pending_withdrawal_txs() async {
    return await callBack.get(
      url: ApiDocs.convertURL(ApiDocs.get_total_pending_withdrawal_txs),
      isUseToken: true,
    );
  }

  Future<Response?> history() async {
    return await callBack.get(
      url: ApiDocs.convertURL(ApiDocs.history),
      isUseToken: true,
    );
  }

  //Connection
  Future<Response?> getBandwidthPrice() async {
    return await callBack.get(
      url: ApiDocs.convertURL(ApiDocs.bandwidthPrice),
      isUseToken: true,
    );
  }

  Future<Response?> connectionOverview() async {
    return await callBack.get(
      url: ApiDocs.convertURL(ApiDocs.connectionOverview),
      isUseToken: true,
    );
  }

  Future<Response?> activeConnection() async {
    return await callBack.get(
      url: ApiDocs.convertURL(ApiDocs.active_connections),
      isUseToken: true,
    );
  }

  Future<Response?> connectionHistory() async {
    return await callBack.get(
      url: ApiDocs.convertURL(ApiDocs.connection_history),
      isUseToken: true,
    );
  }

  Future<Response?> detailConnection(String id) async {
    return await callBack.get(
      url: ApiDocs.convertURL(
          ApiDocs.detail_connection.replaceAll('{session_id}', id.toString())),
      isUseToken: true,
    );
  }

  Future<Response?> getSuggesttionbandwidth() async {
    return await callBack.get(
      url: ApiDocs.convertURL(ApiDocs.suggested_bandwidth_price),
      isUseToken: true,
    );
  }

  Future<Response?> setBandwidthPrice(int price) async {
    var body = {"rate_per_kb": price, "rate_per_second": 0};
    return await callBack.put(
      url: ApiDocs.convertURL(ApiDocs.bandwidthPrice),
      params: body,
      isUseToken: true,
    );
  }

  Future<Response?> postTokenFCM(String token, String device) async {
    var body = {"token": token, "device_type": device};

    var res = await callBack.post(
      url: ApiDocs.convertURL(ApiDocs.postTokenFCM),
      data: body,
      isUseToken: true,
    );
    return res;
  }

  Future<Response?> getUserTierPoints() async {
    return await callBack.get(
        url: ApiDocs.convertURL(ApiDocs.user_tier_points), isUseToken: true);
  }

  // Notification
  Future<Response?> getListNotification() async {
    return await callBack.get(
        url: ApiDocs.convertURL(ApiDocs.list_notification), isUseToken: true);
  }

  Future<Response?> updateLanguageNotification(String language) async {
    return await callBack.put(
        url: ApiDocs.convertNotificationURL(ApiDocs.languageNotification),
        isUseToken: true,
        params: {"language": language});
  }

  Future<Response?> getCurrentIp() async {
    return await callBack.get(
      url: ApiDocs.getCurrentIp,
    );
  }

  Future<Response?> getTotalTime() async {
    return await callBack.get(
        url: ApiDocs.convertURL(ApiDocs.getLoyalPoint), isUseToken: true);
  }
    Future<Response?> getLocationBonus() async {
    return await callBack.get(
        url: ApiDocs.convertURL(ApiDocs.location_bonus));
  }
    Future<Response?> getLocationBonusAll() async {
    return await callBack.get(
        url: ApiDocs.convertURL(ApiDocs.location_bonus_all));
  }

  Future<Response?> hearbeat() async {
    return await callBack.post(
        url: ApiDocs.convertURL(ApiDocs.hearbeat),
        data: {"device_id": SettingConfig.deviceInfo},
       isUseToken: true
        );
  }


  Future<Response?> startSessionLoyaltyPoint() async {
    return await callBack.post(
        url: ApiDocs.convertURL(ApiDocs.startSessionLoyaltyPoint),
        data: {"device_id": await Utils().deviceId()},
        isUseToken: true);
  }

  Future<Response?> endSessionLoyaltyPoint() async {
    return await callBack.post(
        url: ApiDocs.convertURL(ApiDocs.endSessionLoyaltyPoint),
        data: {"device_id": await Utils().deviceId()},
        isUseToken: true);
  }
}
