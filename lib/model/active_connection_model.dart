// class ActiveConnectionModel {
//   int? id;
//   String? status;
//   int? duration;
//   int? sharedBandwidth;
//   String? estimatedRewards;
//
//   ActiveConnectionModel(
//       {this.id,
//       this.status,
//       this.duration,
//       this.sharedBandwidth,
//       this.estimatedRewards});
//
//   ActiveConnectionModel.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     status = json['status'];
//     duration = json['duration'];
//     sharedBandwidth = json['shared_bandwidth'];
//     estimatedRewards = json['estimated_rewards'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['id'] = this.id;
//     data['status'] = this.status;
//     data['duration'] = this.duration;
//     data['shared_bandwidth'] = this.sharedBandwidth;
//     data['estimated_rewards'] = this.estimatedRewards;
//     return data;
//   }
// }

import '../widget/ultil.dart';

class ActiveConnectionModel {
  String? sessionHash;
  String? providerAddr;
  String? clientAddr;
  int? ratePerSecond;
  int? ratePerKb;
  int? ratePerKbV2;
  int? handshakeAt;
  int? endAt;
  int? duration;
  int? bandwidthUsage;
  int? durationFee;
  int? bandwidthFee;
  int? totalFee;
  String? status;

  ActiveConnectionModel(
      {this.sessionHash,
      this.providerAddr,
      this.clientAddr,
      this.ratePerSecond,
      this.ratePerKb,
      this.handshakeAt,
      this.endAt,
      this.duration,
      this.bandwidthUsage,
      this.durationFee,
      this.bandwidthFee,
      this.totalFee,
      this.ratePerKbV2,
      this.status});
static List<ActiveConnectionModel> fromJsonList(List<dynamic> jsonList) {
  return jsonList.map((json) => ActiveConnectionModel.fromJson(json)).toList();
}

  String get getRatePerGb => Utils.convertSzaboToU2U((ratePerKb ?? 0) * 1000 * 1000);
  String get getRatePerHour => Utils.convertSzaboToU2U((ratePerSecond ?? 0) * 60 * 60);
  String get getTotalFee => Utils.convertSzaboToU2U(totalFee ?? 0);
  String get getBandwidthUsage => Utils.kbToGb(bandwidthUsage ?? 0);
  String get getDuration => Utils.formattedTimeDuration(duration ?? 0);
  String get getRatePerGbV2 => Utils.convertSzaboToU2U((ratePerKbV2 ?? 0) * 1000 * 1000);

  String get getStarted => Utils.convertTimestamptoDatetime((handshakeAt ?? 0).toInt());
  // String get getDurationFee => // ((ratePerSecond ?? 0) * (duration ?? 0))
  //     Utils.convertSzaboToU2U((Utils.startToToday(handshakeAt ?? 0)) * (ratePerSecond ?? 0));
  String get gettotal {
    int total = (Utils.startToToday(handshakeAt ?? 0)) * (ratePerSecond ?? 0) + ((ratePerKb ?? 0) * ((bandwidthUsage ?? 0)).toInt());
    return Utils.convertSzaboToU2U(total.toInt());
  }
    String get gettotalV2 {
    int total = (Utils.startToToday(handshakeAt ?? 0)) * (ratePerSecond ?? 0) + ((ratePerKbV2 ?? 0) * ((bandwidthUsage ?? 0)).toInt());
    return Utils.convertSzaboToU2U(total.toInt());
  }

  int get gettotalNotConvert {
    int total = (Utils.startToToday(handshakeAt ?? 0)) * (ratePerSecond ?? 0) + ((ratePerKb ?? 0) * ((bandwidthUsage ?? 0)).toInt());
    return total;
  }
    int get gettotalV2NotConvert {
    int total = (Utils.startToToday(handshakeAt ?? 0)) * (ratePerSecond ?? 0) + ((ratePerKbV2 ?? 0) * ((bandwidthUsage ?? 0))).toInt();
    return total;
  }
  String getPlatformFee(String tier) {
    return Utils.calculatorPlatformFeeFromTie(tier, gettotalNotConvert);
  }

  String getReferralFee(String tier) {
    return Utils.calculatorReferralFeeFromTie(tier, gettotalNotConvert);
  }

  String getNetAmount(String tier) {
    return Utils.calculatorNetAmountFromTie(tier, gettotalNotConvert);
  }
String getPlatformFeeV2(String tier) {
    return Utils.calculatorPlatformFeeFromTie(tier, gettotalV2NotConvert);
  }

  String getReferralFeeV2(String tier) {
    return Utils.calculatorReferralFeeFromTie(tier, gettotalV2NotConvert);
  }

  String getNetAmountV2(String tier) {
    return Utils.calculatorNetAmountFromTie(tier, gettotalV2NotConvert);
  }
  ActiveConnectionModel.fromJson(Map<String, dynamic> json) {
    sessionHash = json['session_hash'];
    providerAddr = json['provider_addr'];
    clientAddr = json['client_addr'];
    ratePerSecond = json['rate_per_second'];
    ratePerKb = json['rate_per_kb'];
    handshakeAt = json['handshake_at'];
    endAt = json['end_at'];
    duration = json['duration'];
    bandwidthUsage = json['bandwidth_usage'];
    durationFee = json['duration_fee'];
    bandwidthFee = json['bandwidth_fee'];
    totalFee = json['total_fee'];
    ratePerKbV2 = json['rate_per_kb_v2'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['session_hash'] = sessionHash;
    data['provider_addr'] = providerAddr;
    data['client_addr'] = clientAddr;
    data['rate_per_second'] = ratePerSecond;
    data['rate_per_kb'] = ratePerKb;
    data['handshake_at'] = handshakeAt;
    data['end_at'] = endAt;
    data['duration'] = duration;
    data['bandwidth_usage'] = bandwidthUsage;
    data['duration_fee'] = durationFee;
    data['bandwidth_fee'] = bandwidthFee;
    data['total_fee'] = totalFee;
    data['status'] = status;
    data['rate_per_kb_v2'] = ratePerKbV2;
    return data;
  }
}
