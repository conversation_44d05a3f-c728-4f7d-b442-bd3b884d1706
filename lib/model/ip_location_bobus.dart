class IPLocationBonusModel {
  BonusInfo? bonusInfo;
  String? countryName;

  IPLocationBonusModel({this.bonusInfo, this.countryName});

  IPLocationBonusModel.fromJson(Map<String, dynamic> json) {
    bonusInfo = json['bonus_info'] != null
        ? BonusInfo.fromJson(json['bonus_info'])
        : null;
    countryName = json['country_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (bonusInfo != null) {
      data['bonus_info'] = bonusInfo!.toJson();
    }
    data['country_name'] = countryName;
    return data;
  }
}

class BonusInfo {
  double? bonusAmount;

  BonusInfo({this.bonusAmount});

  BonusInfo.fromJson(Map<String, dynamic> json) {
    bonusAmount = json['bonus_amount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['bonus_amount'] = bonusAmount;
    return data;
  }
  String get bonusAmountString => "${((bonusAmount??0)*100).toInt()}%"; 
}
class LocationBonusAllModel {
  int? countryGeonameId;
  String? countryName;
  double? bonusAmount;

  LocationBonusAllModel(
      {this.countryGeonameId, this.countryName, this.bonusAmount});

  LocationBonusAllModel.fromJson(Map<String, dynamic> json) {
    countryGeonameId = json['country_geoname_id'];
    countryName = json['country_name'];
    bonusAmount = json['bonus_amount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['country_geoname_id'] = countryGeonameId;
    data['country_name'] = countryName;
    data['bonus_amount'] = bonusAmount;
    return data;
  }
    String get getBonusAmount => bonusAmount!=null?"${((bonusAmount??0)*100).toInt()}%":'0%'; 
}