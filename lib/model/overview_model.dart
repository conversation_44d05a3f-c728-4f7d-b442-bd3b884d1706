import '../widget/ultil.dart';

class RewardOverviewModel {
  int? totalCommissionRewards;
  int? totalNetworkRewards;
  int? totalNetworkRewardsv2; // token u2u
  int? totalReferralRewardsv2; // token u2u
  int? totalReferralRewards;
  int? totalRewards;
  int? totalRewardsv2; // token u2u
  int? totalTaskRewards;
  int? unclaimedRewards;
  int? unclaimedRewardsv2; // token u2u
  double? minutesUptime;

  RewardOverviewModel(
      {this.totalCommissionRewards,
      this.totalNetworkRewards,
      this.totalReferralRewards,
      this.totalRewards,
      this.totalTaskRewards,
      this.minutesUptime,
      this.unclaimedRewards,
      this.unclaimedRewardsv2,
      this.totalNetworkRewardsv2,
      this.totalReferralRewardsv2,
      this.totalRewardsv2});

  RewardOverviewModel.fromJson(Map<String, dynamic> json) {
    totalCommissionRewards = json['total_commission_rewards'];
    totalNetworkRewards = json['total_network_rewards'];
    totalReferralRewards = json['total_referral_rewards'];
    totalRewards = json['total_rewards'];
    totalRewardsv2 = json['total_rewards_v2'];
    totalTaskRewards = json['total_task_rewards'];
    unclaimedRewards = json['unclaimed_rewards'];
    unclaimedRewardsv2 = json['unclaimed_rewards_v2'];
    minutesUptime = json['minutes_uptime'];
    totalNetworkRewardsv2 = json['total_network_rewards_v2'];
    totalReferralRewardsv2 = json['total_referral_rewards_v2'];
  }

  String get getTotalRewards => Utils.convertSzaboToU2U(totalRewards ?? 0);
  String get getTotalTaskRewards => Utils.convertSzaboToU2U(totalTaskRewards ?? 0);
  String get getTotalReferralRewards => Utils.convertSzaboToU2U(totalReferralRewards ?? 0);
  String get getTotalNetworkRewards => Utils.convertSzaboToU2U(totalNetworkRewards ?? 0);
  String get getUnclaimedRewards => Utils.convertSzaboToU2U(unclaimedRewards ?? 0);
  String get getCommissionRewards => Utils.convertSzaboToU2U(totalCommissionRewards ?? 0);
  String get getTotalRewardsv2 => Utils.convertSzaboToU2U(totalRewardsv2 ?? 0);
  String get getUnclaimedRewardsv2 => Utils.convertSzaboToU2U(unclaimedRewardsv2 ?? 0);
  String get getTotalNetworkRewardsv2 => Utils.convertSzaboToU2U(totalNetworkRewardsv2 ?? 0);
  String get getTotalReferralRewardsv2 => Utils.convertSzaboToU2U(totalReferralRewardsv2 ?? 0);

  String get getTime {
    double points = minutesUptime ?? 0.0;
    String value = '0.0';
    if (points < 100) {
      value = points.toString();
    } else if (points < 1000) {
      value = points.toString();
    } else if (points < 1000000) {
      value = '${(points / 1000).toStringAsFixed(points % 1000 == 0 ? 0 : 1)}K';
    } else {
      double millions = points / 1000000;
      value = '${millions.toStringAsFixed(millions < 10 ? 1 : 0)}M';
    }
    return '$value xp';
    // if (minutes_uptime! <= 60) {
    //   return '${minutes_uptime?.toInt()} ' + LocaleKeys.mimutes.tr;
    // } else {
    //   int remindUptime = (minutes_uptime! ~/ 60);
    //   return '${remindUptime} ' + LocaleKeys.hour.tr;
    // }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total_commission_rewards'] = totalCommissionRewards;
    data['total_network_rewards'] = totalNetworkRewards;
    data['total_referral_rewards'] = totalReferralRewards;
    data['total_rewards'] = totalRewards;
    data['total_task_rewards'] = totalTaskRewards;
    data['unclaimed_rewards'] = unclaimedRewards;
    data['minutes_uptime'] = minutesUptime;
    data['total_rewards_v2'] = totalRewardsv2;
    data['unclaimed_rewards_v2'] = unclaimedRewardsv2;
    data['total_network_rewards_v2'] = totalNetworkRewardsv2;
    data['total_referral_rewards_v2'] = totalReferralRewardsv2;
    return data;
  }
}
