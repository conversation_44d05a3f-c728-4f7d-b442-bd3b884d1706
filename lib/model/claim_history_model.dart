import 'package:u2u_dpn/widget/ultil.dart';

class ClaimHistoryModel {
  int? id;
  String? fromAddr;
  String? toAddr;
  String? txHash;
  int? amount;
  String? txType;
  String? txStatus;
  int? createdAt;
  String?chainTxHash;

  ClaimHistoryModel({this.id, this.fromAddr, this.toAddr, this.txHash, this.amount, this.txType, this.txStatus, this.createdAt, this.chainTxHash});

  String get getAmount => Utils.convertSzaboToU2U(amount ?? 0);

  ClaimHistoryModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    fromAddr = json['from_addr'];
    toAddr = json['to_addr'];
    txHash = json['tx_hash'];
    amount = json['amount'];
    txType = json['tx_type'];
    txStatus = json['tx_status'];
    createdAt = json['created_at'];
    chainTxHash = json['chain_tx_hash'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['from_addr'] = fromAddr;
    data['to_addr'] = toAddr;
    data['tx_hash'] = txHash;
    data['amount'] = amount;
    data['tx_type'] = txType;
    data['tx_status'] = txStatus;
    data['created_at'] = createdAt;
    data['chain_tx_hash'] = chainTxHash;
    return data;
  }
}
