// ignore_for_file: non_constant_identifier_names

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/ultil.dart';

class ConnectionHistoryModel {
  String? sessionHash;
  int? providerId;
  int? clientId;
  int? ratePerSecond;
  int? ratePerKb;
  int? handshakeAt;
  int? endAt;
  int? duration;
  int? bandwidthUsage;
  int? durationFee;
  int? bandwidthFee;
  int? totalFee;
  int? totalFeeV2;
  int? ratePerKbV2;
  
  String? status;

  ConnectionHistoryModel(
      {this.sessionHash,
      this.providerId,
      this.clientId,
      this.ratePerSecond,
      this.ratePerKb,
      this.handshakeAt,
      this.endAt,
      this.duration,
      this.bandwidthUsage,
      this.durationFee,
      this.bandwidthFee,
      this.totalFee,
      this.totalFeeV2,
      this.ratePerKbV2,
      this.status});

  String get getsharedBandwidth => Utils.kbToGb(bandwidthUsage ?? 0);
  String get getRatePerGb =>
      Utils.convertSzaboToU2U((ratePerKb ?? 0) * 1000 * 1000);
  String get getRatePerHour =>
      Utils.convertSzaboToU2U((ratePerSecond ?? 0) * 60 * 60);
  String get getTotalFee => Utils.convertSzaboToU2U(totalFee ?? 0);
  String get gettotalV2 => Utils.convertSzaboToU2U(totalFeeV2 ?? 0);
  String get getRatePerGbV2 =>
      Utils.convertSzaboToU2U((ratePerKbV2 ?? 0) * 1000 * 1000);
  String get getBandwidthFee => Utils.convertSzaboToU2U(bandwidthFee ?? 0);
  // String get getDurationFee => Utils.convertSzaboToU2U(durationFee ?? 0);

  String get getStarted =>
      Utils.convertTimestamptoDatetime((handshakeAt ?? 0).toInt());
  String get getEnded => Utils.convertTimestamptoDatetime((endAt ?? 0).toInt());

  String get gettotal {
    int total = (Utils.startToToday(handshakeAt ?? 0)) * (ratePerSecond ?? 0) +
        ((ratePerKb ?? 0) * ((bandwidthUsage ?? 0)).toInt());
    return Utils.convertSzaboToU2U(total.toInt());
  }
  String? get getTier {
    int tier = durationFee ?? 0;
    switch (tier) {
      case 1425:
        return "Member";
      case 1350:
        return "VIP";
      case 1095:
        return "Elite";
      case 900:
        return "Premier";
      case 705:
        return "Exclusive";
      default:
        return  null;
        
    }
  }

  String getReferralFee(String tier) {
    return Utils.calculatorReferralFeeFromTie(getTier?? tier, totalFee ?? 0);
  }

  String getPlatformFee(String tier) {
    return Utils.calculatorPlatformFeeFromTie(getTier?? tier, totalFee ?? 0);
  }

  String getNetAmount(String tier) {
    return Utils.calculatorNetAmountFromTie(getTier?? tier, totalFee ?? 01);
  }

  String getReferralFeeV2(String tier) {
    return Utils.calculatorReferralFeeFromTie(getTier?? tier, totalFeeV2 ?? 0);
  }

  String getNetAmountV2(String tier) {
    return Utils.calculatorNetAmountFromTie(getTier?? tier, totalFeeV2 ?? 0);
  }


  String getPlatformFeeV2(String tier) {
    return Utils.calculatorPlatformFeeFromTie(getTier?? tier, totalFeeV2 ?? 0);
  }

  String get getImage {
    switch (status) {
      case "Finished":
        return AppImages.icon_connection_wifi_finshed;
      case "BreachOfRules":
        return AppImages.icon_connection_wifi_breach_of_rules;
      case "Interrupted":
        return AppImages.icon_connection_wifi_encountering_interruption;
      default:
        return AppImages.icon_connection_wifi_rejected;
    }
  }

  String get getStatus {
    switch (status) {
      case "Finished":
        return "Finished".tr;
      case "BreachOfRules":
        return 'Breach of rules'.tr;
      case "Interrupted":
        return "Encountering interruption".tr;
      default:
        return "Rejected".tr;
    }
  }

  String get getStarTime => Utils.convertTimestamptoDatetime(handshakeAt ?? 0);

  String get getEndTime => Utils.convertTimestamptoDatetime(endAt ?? 0);

  String get getDuration => Utils.formattedTimeDuration(duration ?? 0);

  List<Color> get getColorStatus {
    switch (status) {
      case "Finished":
        return [
          AppColor.primaryGreen500,
          AppColor.primaryBlue500,
        ];
      case "BreachOfRules":
        return [
          AppColor.ErrorColor300,
          AppColor.ErrorColor300,
        ];
      case "Interrupted":
        return [
          AppColor.highLightColor300,
          AppColor.highLightColor300,
        ];
      default:
        return [
          AppColor.neutral400,
          AppColor.neutral400,
        ];
    }
  }

  ConnectionHistoryModel.fromJson(Map<String, dynamic> json) {
    sessionHash = json['session_hash'];
    providerId = json['provider_id'];
    clientId = json['client_id'];
    ratePerSecond = json['rate_per_second'];
    ratePerKb = json['rate_per_kb'];
    handshakeAt = json['handshake_at'];
    endAt = json['end_at'];
    duration = json['duration'];
    bandwidthUsage = json['bandwidth_usage'];
    durationFee = json['duration_fee'];
    bandwidthFee = json['bandwidth_fee'];
    totalFee = json['total_fee'];
    totalFeeV2 = json['total_fee_v2'];
    ratePerKbV2 = json['rate_per_kb_v2'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['session_hash'] = sessionHash;
    data['provider_id'] = providerId;
    data['client_id'] = clientId;
    data['rate_per_second'] = ratePerSecond;
    data['rate_per_kb'] = ratePerKb;
    data['handshake_at'] = handshakeAt;
    data['end_at'] = endAt;
    data['duration'] = duration;
    data['bandwidth_usage'] = bandwidthUsage;
    data['duration_fee'] = durationFee;
    data['bandwidth_fee'] = bandwidthFee;
    data['total_fee'] = totalFee;
    data['status'] = status;
    data['total_fee_v2'] = totalFeeV2;
    data['rate_per_kb_v2'] = ratePerKbV2;
    return data;
  }
}
