class BandwidthPriceModel {
  int? ratePerKb;
  int? ratePerSecond;
  int? userId;

  BandwidthPriceModel({this.ratePerKb, this.ratePerSecond, this.userId});

  BandwidthPriceModel.fromJson(Map<String, dynamic> json) {
    ratePerKb = json['rate_per_kb'];
    ratePerSecond = json['rate_per_second'];
    userId = json['user_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  <String, dynamic>{};
    data['rate_per_kb'] = ratePerKb;
    data['rate_per_second'] = ratePerSecond;
    data['user_id'] = userId;
    return data;
  }
}
