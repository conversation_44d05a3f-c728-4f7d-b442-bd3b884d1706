// class UserDetailModel {
//   User? user;
//   UserTier? userTier;
//   UserReferral? userReferral;

//   UserDetailModel({this.user, this.userTier, this.userReferral});

//   UserDetailModel.fromJson(Map<String, dynamic> json) {
//     user = json['user'] != null ? new User.fromJson(json['user']) : null;
//     userTier = json['user_tier'] != null ? new UserTier.fromJson(json['user_tier']) : null;
//     userReferral = json['user_referral'] != null ? new UserReferral.fromJson(json['user_referral']) : null;
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     if (this.user != null) {
//       data['user'] = this.user!.toJson();
//     }
//     if (this.userTier != null) {
//       data['user_tier'] = this.userTier!.toJson();
//     }
//     if (this.userReferral != null) {
//       data['user_referral'] = this.userReferral!.toJson();
//     }
//     return data;
//   }
// }

// class User {
//   String? username;
//   String? fingerprint;
//   String? pincode;
//   String? depositAddr;
//   String? withdrawalAddr;
//   int? balance;
//   int? createdAt;
//   int? lastLogin;

//   User({this.username, this.fingerprint, this.pincode, this.depositAddr, this.withdrawalAddr, this.balance, this.createdAt, this.lastLogin});

//   User.fromJson(Map<String, dynamic> json) {
//     username = json['username'];
//     fingerprint = json['fingerprint'];
//     pincode = json['pincode'];
//     depositAddr = json['deposit_addr'];
//     withdrawalAddr = json['withdrawal_addr'];
//     balance = json['balance'];
//     createdAt = json['created_at'];
//     lastLogin = json['last_login'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['username'] = this.username;
//     data['fingerprint'] = this.fingerprint;
//     data['pincode'] = this.pincode;
//     data['deposit_addr'] = this.depositAddr;
//     data['withdrawal_addr'] = this.withdrawalAddr;
//     data['balance'] = this.balance;
//     data['created_at'] = this.createdAt;
//     data['last_login'] = this.lastLogin;
//     return data;
//   }
// }

// class UserTier {
//   int? points;
//   String? tier;
//   String? userAddr;

//   UserTier({this.points, this.tier, this.userAddr});

//   UserTier.fromJson(Map<String, dynamic> json) {
//     points = json['points'];
//     tier = json['tier'];
//     userAddr = json['user_addr'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['points'] = this.points;
//     data['tier'] = this.tier;
//     data['user_addr'] = this.userAddr;
//     return data;
//   }
// }

// class UserReferral {
//   String? userAddr;
//   String? referralCode;
//   int? createdAt;
//   String? referredBy;
//   int? referredAt;
//   String? txHash;

//   UserReferral({this.userAddr, this.referralCode, this.createdAt, this.referredBy, this.referredAt, this.txHash});

//   UserReferral.fromJson(Map<String, dynamic> json) {
//     userAddr = json['user_addr'];
//     referralCode = json['referral_code'];
//     createdAt = json['created_at'];
//     referredBy = json['referred_by'];
//     referredAt = json['referred_at'];
//     txHash = json['tx_hash'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['user_addr'] = this.userAddr;
//     data['referral_code'] = this.referralCode;
//     data['created_at'] = this.createdAt;
//     data['referred_by'] = this.referredBy;
//     data['referred_at'] = this.referredAt;
//     data['tx_hash'] = this.txHash;
//     return data;
//   }
// }

// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';

import '../utils/app_utils.dart';

class UserDetailModel {
  User? user;
  UserReferral? userReferral;
  UserTier? userTier;
  UserXp? userXp;
  UserDetailModel({this.user, this.userReferral, this.userTier, this.userXp});

  UserDetailModel.fromJson(Map<String, dynamic> json) {
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    userReferral = json['user_referral'] != null
        ? UserReferral.fromJson(json['user_referral'])
        : null;
    userTier =
        json['user_tier'] != null ? UserTier.fromJson(json['user_tier']) : null;
    userXp =
        json['user_xp'] != null ? UserXp.fromJson(json['user_xp']) : UserXp();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (user != null) {
      data['user'] = user!.toJson();
    }
    if (userReferral != null) {
      data['user_referral'] = userReferral!.toJson();
    }
    if (userTier != null) {
      data['user_tier'] = userTier!.toJson();
    }
    if (userXp != null) {
      data['user_xp'] = userXp!.toJson();
    }
    return data;
  }
}

class User {
  // int? balance;
  int? createdAt;
  String? depositAddr;
  String? fingerprint;
  int? lastLogin;
  String? pincode;
  String? username;
  String? withdrawalAddr;

  User(
      {
      //this.balance,
      this.createdAt,
      this.depositAddr,
      this.fingerprint,
      this.lastLogin,
      this.pincode,
      this.username,
      this.withdrawalAddr});

  User.fromJson(Map<String, dynamic> json) {
    // balance = json['balance'];
    createdAt = json['created_at'];
    depositAddr = json['deposit_addr'];
    fingerprint = json['fingerprint'];
    lastLogin = json['last_login'];
    pincode = json['pincode'];
    username = json['username'];
    withdrawalAddr = json['withdrawal_addr'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    // data['balance'] = this.balance;
    data['created_at'] = createdAt;
    data['deposit_addr'] = depositAddr;
    data['fingerprint'] = fingerprint;
    data['last_login'] = lastLogin;
    data['pincode'] = pincode;
    data['username'] = username;
    data['withdrawal_addr'] = withdrawalAddr;
    return data;
  }
}

class UserReferral {
  int? createdAt;
  String? referralCode;
  int? referredAt;
  String? referredBy;
  String? txHash;
  String? userAddr;

  UserReferral(
      {this.createdAt,
      this.referralCode,
      this.referredAt,
      this.referredBy,
      this.txHash,
      this.userAddr});

  UserReferral.fromJson(Map<String, dynamic> json) {
    createdAt = json['created_at'];
    referralCode = json['referral_code'];
    referredAt = json['referred_at'];
    referredBy = json['referred_by'];
    txHash = json['tx_hash'];
    userAddr = json['user_addr'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['created_at'] = createdAt;
    data['referral_code'] = referralCode;
    data['referred_at'] = referredAt;
    data['referred_by'] = referredBy;
    data['tx_hash'] = txHash;
    data['user_addr'] = userAddr;
    return data;
  }
}

class UserTier {
  int? points;

  UserTier({this.points});
  int get getPoint {
    return (((points ?? 0) / 60) * 2).toInt();
  }

  String get tier {
    // if (getPoint < 16500000) return 'Member';
    if (getPoint >= 5000000) return 'Exclusive';
    if (getPoint >= 1000000) return 'Premier';
    if (getPoint >= 150000) return 'Elite';
    if (getPoint >= 20000) return 'VIP';
    if (getPoint >= 2000) return 'Member';

    return 'No Tier';
  }

  String get getPointRank {
    switch (tier) {
      case 'Exclusive':
        return '5M';
      case 'Premier':
        return '5M';
      case 'Elite':
        return '1M';
      case 'VIP':
        return '150k';
      case 'Member':
        return '20k';
      default:
        return '2k';
    }
  }

  String get needToUpTier {
    return ((getMaxPoints - getPoint).toInt()).toString();
  }

  String get getPointNextRank {
    switch (tier) {
      case 'Exclusive':
        return '';
      case 'Premier':
        return '5M';
      case 'Elite':
        return '1M';
      case 'VIP':
        return '150k';
      case 'Member':
        return '20k';
      default:
        return '2k';
    }
  }

  double get getMinPoints {
    switch (tier) {
      case 'Exclusive':
        return 5000000;
      case 'Premier':
        return 1000000;
      case 'Elite':
        return 150000;
      case 'VIP':
        return 20000;
      case 'Member':
        return 2000;
      default:
        return 0;
    }
  }

  double get getMaxPoints {
    switch (tier) {
      case 'Exclusive':
        return 5000000;
      case 'Premier':
        return 5000000;
      case 'Elite':
        return 1000000;
      case 'VIP':
        return 150000;
      case 'Member':
        return 20000;
      default:
        return 2000;
    }
  }

  Color get getColor {
    switch (tier) {
      case 'Exclusive':
        return AppColor.exclusive;
      case 'Premier':
        return AppColor.premier;
      case 'Elite':
        return AppColor.elite;
      case 'VIP':
        return AppColor.highLightColor300;
      case 'Member':
        return AppColor.neutral200;
      default:
        return Colors.white;
    }
  }

  String get getImageRank {
    switch (tier) {
      case 'Exclusive':
        return AppImages.exclusive;
      case 'Premier':
        return AppImages.premier;
      case 'Elite':
        return AppImages.elite;
      case 'VIP':
        return AppImages.vip;
      case 'Member':
        return AppImages.member;
      default:
        return AppImages.member;
    }
  }

  List<Color> get getlistColor {
    switch (tier) {
      case 'Exclusive':
        return [
          const Color.fromARGB(255, 199, 125, 239),
          const Color.fromARGB(255, 150, 123, 249),
        ];
      case 'Premier':
        return [
          const Color.fromARGB(255, 109, 142, 252),
          const Color.fromARGB(255, 76, 203, 245)
        ];
      case 'Elite':
        return [
          const Color.fromARGB(255, 156, 255, 114),
          const Color.fromARGB(255, 116, 255, 114),
        ];
      case 'VIP':
        return [
          const Color.fromARGB(255, 247, 254, 118),
          const Color.fromARGB(255, 245, 220, 92),
        ];
      default:
        return [
          const Color.fromARGB(255, 194, 224, 239),
          const Color.fromARGB(255, 255, 255, 255)
        ];
    }
  }
  //   String convertKbToGb(int kilobytes) {
  //   // 1 GB = 1024 MB, 1 MB = 1024 KB
  //   //1 TB = 1024GB
  //   // 1 TB = 1.073.741.824 KB
  //   double megabytes = kilobytes / (1024);
  //   double gigabytes = megabytes / 1024;
  //   if(gigabytes>1024){
  //     var terabyte = gigabytes/1024;
  //     if(terabyte.toString().split('.').last.length>3){
  //       return '${terabyte.toString().split('.').first}.${terabyte.toString().split('.').last.substring(0, 2)}';
  //     }
  //     return terabyte.toString();
  //   } else{
  //      if (gigabytes.toString().split('.').last.length > 3) {
  //       if(gigabytes<0.00001 && gigabytes!=0){
  //         return '<0.00001';
  //       }
  //         return '${gigabytes.toString().split('.').first}.${gigabytes.toString().split('.').last.substring(0, 4)}';
  //       }
  //   return '$gigabytes';
  //   }
  // }

  static String convertKbToGbOrT(int kilobytes) {
    // 1 GB = 1024 MB, 1 MB = 1024 KB
    double megabytes = kilobytes / (1024);
    if (megabytes > 1024) {
      var gigabytes = megabytes / 1024;
      if (gigabytes > 1024) {
        var terabyte = gigabytes / 1024;
        if (terabyte.toString().split('.').last.length > 3) {
          return '${terabyte.toString().split('.').first}.${terabyte.toString().split('.').last.substring(0, 2)} TB';
        }
        return '$terabyte TB';
      } else {
        if (gigabytes.toString().split('.').last.length > 3) {
          return '${gigabytes.toString().split('.').first}.${gigabytes.toString().split('.').last.substring(0, 2)} GB';
        }
        return '$gigabytes GB';
      }
    }
    if (megabytes.toString().split('.').last.length > 3) {
      return '${megabytes.toString().split('.').first}.${megabytes.toString().split('.').last.substring(0, 2)} MB';
    }
    return '$megabytes MB';
  }

  UserTier.fromJson(Map<String, dynamic> json) {
    points = json['points'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['points'] = points;
    return data;
  }
}

class UserXp {
  double? minutes_uptime;

  UserXp({this.minutes_uptime});

  UserXp.fromJson(Map<String, dynamic> json) {
    minutes_uptime = json['minutes_uptime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['minutes_uptime'] = minutes_uptime ?? 0;
    return data;
  }

  String get getTime {
    minutes_uptime ??= 0;
    return '${minutes_uptime?.toInt()} ' 'xp';
    // if (minutes_uptime! <= 60) {
    //   return '${minutes_uptime?.toInt()} ' + LocaleKeys.mimutes.tr;
    // } else {
    //   int remindUptime = (minutes_uptime! ~/ 60);
    //   return '${remindUptime} ' + LocaleKeys.hour.tr;
    // }
  }
}
