import '../widget/ultil.dart';

class ConnectionOverviewModel {

  int? totalRewards;
    int? totalRewardsV2;
  int? totalBandwidthUsages;


  ConnectionOverviewModel({ this.totalRewards, this.totalBandwidthUsages, this.totalRewardsV2});

  String get getTotalRewardsV2 => Utils.convertSzaboToU2U( totalRewardsV2?? 0);
  String get getTotalRewards => Utils.convertSzaboToU2U(totalRewards ?? 0);
  String get getTotalBandwidthUsages => Utils.kbToGb(totalBandwidthUsages ?? 0);

  ConnectionOverviewModel.fromJson(Map<String, dynamic> json) {

    totalRewards = json['total_rewards'];
    totalBandwidthUsages = json['total_bandwidth_served'];
    totalRewardsV2 = json['total_rewards_v2'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  <String, dynamic>{};

    data['total_rewards'] = totalRewards;
    data['total_bandwidth_served'] = totalBandwidthUsages;
    data['total_rewards_v2'] = totalRewardsV2;
    return data;
  }
}
