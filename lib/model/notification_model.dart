import 'package:u2u_dpn/widget/ultil.dart';

class NotificationModel {
  String? id;
  String? header;
  String? content;
  String? level;
  int? createdAt;

  NotificationModel({this.id, this.header, this.content, this.level, this.createdAt});

  String get getDateTime => Utils.convertTimestamptoDatetime(createdAt ?? 0);

  NotificationModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    header = json['header'];
    content = json['content'];
    level = json['level'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['header'] = header;
    data['content'] = content;
    data['level'] = level;
    data['created_at'] = createdAt;
    return data;
  }
}
