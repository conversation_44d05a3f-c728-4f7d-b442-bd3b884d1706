class ReferralHistoryModel {
  String? userAddr;
  String? referralCode;
  int? createdAt;
  String? referredBy;
  int? referredAt;
  String? txHash;

  ReferralHistoryModel({this.userAddr, this.referralCode, this.createdAt, this.referredBy, this.referredAt, this.txHash});

  ReferralHistoryModel.fromJson(Map<String, dynamic> json) {
    userAddr = json['user_addr'];
    referralCode = json['referral_code'];
    createdAt = json['created_at'];
    referredBy = json['referred_by'];
    referredAt = json['referred_at'];
    txHash = json['tx_hash'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  <String, dynamic>{};
    data['user_addr'] = userAddr;
    data['referral_code'] = referralCode;
    data['created_at'] = createdAt;
    data['referred_by'] = referredBy;
    data['referred_at'] = referredAt;
    data['tx_hash'] = txHash;
    return data;
  }
}
