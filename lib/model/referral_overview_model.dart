import '../widget/ultil.dart';

class ReferralOverviewModel {
  int? totalReferees;
  int? totalRefereesTxs;
  int? totalCommision;
  int? unclaimedCommission;
  int? totalCommisionTxs;
  int? totalCommisionV2;
  int? unclaimedCommissionV2;

  ReferralOverviewModel(
      {this.totalReferees,
      this.totalRefereesTxs,
      this.totalCommision,
      this.unclaimedCommission,
      this.totalCommisionTxs,
      this.totalCommisionV2,
      this.unclaimedCommissionV2});
  String get getTotalRefereesTxs =>
      totalRefereesTxs == null ? '0' : totalRefereesTxs.toString();
  String get getTotalCommisionTxs =>
      totalCommisionTxs == null ? '0' : totalCommisionTxs.toString();
  String get getUnclaimedCommission =>
      Utils.convertSzaboToU2U(unclaimedCommission ?? 0);
  String get getTotalCommision => Utils.convertSzaboToU2U(totalCommision ?? 0);
  String get getTotalReferees =>
      totalReferees == null ? '0' : totalReferees.toString();
  String get getTotalCommisionV2 =>
      Utils.convertSzaboToU2U(totalCommisionV2 ?? 0);
  String get getUnclaimedCommissionV2 =>
      Utils.convertSzaboToU2U(unclaimedCommissionV2 ?? 0);

  ReferralOverviewModel.fromJson(Map<String, dynamic> json) {
    totalReferees = json['total_referees'];
    totalRefereesTxs = json['total_referees_txs'];
    totalCommision = json['total_commision'];
    unclaimedCommission = json['unclaimed_commission'];
    totalCommisionTxs = json['total_commission_txs'];
    totalCommisionV2 = json['total_commision_v2'];
    unclaimedCommissionV2 = json['unclaimed_commission_v2'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total_referees'] = totalReferees;
    data['total_referees_txs'] = totalRefereesTxs;
    data['total_commision'] = totalCommision;
    data['unclaimed_commission'] = unclaimedCommission;
    data['total_commission_txs'] = totalCommisionTxs;
    data['total_commision_v2'] = totalCommisionV2;
    data['unclaimed_commission_v2'] = unclaimedCommissionV2;
    return data;
  }
}
