import 'package:u2u_dpn/model/user_detail_model.dart';
import 'package:u2u_dpn/widget/ultil.dart';

class UserTierPointModel {
  int? createdAt;
  int? points;
  // String? pointsType;
  String? userAddr;

  UserTierPointModel({this.createdAt, this.points,
  //  this.pointsType,
    this.userAddr});

  UserTierPointModel.fromJson(Map<String, dynamic> json) {
    createdAt = json['created_at'];
    points = json['points'];
    // pointsType = json['points_type'];
    userAddr = json['user_addr'];
  }

  String get getCreatAt => Utils.convertTimestamptoDatetime(createdAt ?? 0);
  //  if (pointsType == 'Diamond') {
  //     return 'Unlimited';
  //   } else if (pointsType == 'Platinum') {
  //     return '500';
  //   } else if(pointsType == 'Gold'){
  //     return '150';
  //   }
  //   else {
  //     return '50';
  //   }

  String get getPoint=> UserTier.convertKbToGbOrT(points?? 0);

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['created_at'] = createdAt;
    data['points'] = points;
    // data['points_type'] = this.pointsType;
    data['user_addr'] = userAddr;
    return data;
  }
}
