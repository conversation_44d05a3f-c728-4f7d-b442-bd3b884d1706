class GoogleSSOModel {
  String? id;
  String? email;
  String? displayName;
  String? photoUrl;
  String? token;

  GoogleSSOModel({this.id, this.email, this.displayName, this.photoUrl, this.token});

  GoogleSSOModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    email = json['email'];
    displayName = json['display_name'];
    photoUrl = json['photo_url'];
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  <String, dynamic>{};
    data['id'] = id;
    data['email'] = email;
    data['display_name'] = displayName;
    data['photo_url'] = photoUrl;
    data['token'] = token;
    return data;
  }
}

class AppleSSOModel {
  String? email;
  String? fullName;
  String? code;
  String? token;

  AppleSSOModel({this.email, this.fullName, this.code, this.token});

  AppleSSOModel.fromJson(Map<String, dynamic> json) {
    email = json['email'];
    fullName = json['full_name'];
    code = json['code'];
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['email'] = email;
    data['full_name'] = fullName;
    data['code'] = code;
    data['token'] = token;
    return data;
  }
}
