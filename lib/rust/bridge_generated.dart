// AUTO GENERATED FILE, DO NOT EDIT.
// Generated by `flutter_rust_bridge`@ 1.82.6.
// ignore_for_file: non_constant_identifier_names, unused_element, duplicate_ignore, directives_ordering, curly_braces_in_flow_control_structures, unnecessary_lambdas, slash_for_doc_comments, prefer_const_literals_to_create_immutables, implicit_dynamic_list_literal, duplicate_import, unused_import, unnecessary_import, prefer_single_quotes, prefer_const_constructors, use_super_parameters, always_use_package_imports, annotate_overrides, invalid_use_of_protected_member, constant_identifier_names, invalid_use_of_internal_member, prefer_is_empty, unnecessary_const

import "bridge_definitions.dart";
import 'dart:convert';
import 'dart:async';
import 'package:meta/meta.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge.dart';
import 'package:uuid/uuid.dart';

import 'dart:convert';
import 'dart:async';
import 'package:meta/meta.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge.dart';
import 'package:uuid/uuid.dart';

import 'dart:ffi' as ffi;

class DpnBridgeImpl implements DpnBridge {
  final DpnBridgePlatform _platform;
  factory DpnBridgeImpl(ExternalLibrary dylib) =>
      DpnBridgeImpl.raw(DpnBridgePlatform(dylib));

  /// Only valid on web/WASM platforms.
  factory DpnBridgeImpl.wasm(FutureOr<WasmModule> module) =>
      DpnBridgeImpl(module as ExternalLibrary);
  DpnBridgeImpl.raw(this._platform);
  Future<void> runPeerNode(
      {required String webserverAddr,
      required int webserverPort,
      required String adminAddr,
      required String accessToken,
      required String refreshToken,
      dynamic hint}) {
    var arg0 = _platform.api2wire_String(webserverAddr);
    var arg1 = api2wire_u16(webserverPort);
    var arg2 = _platform.api2wire_String(adminAddr);
    var arg3 = _platform.api2wire_String(accessToken);
    var arg4 = _platform.api2wire_String(refreshToken);
    return _platform.executeNormal(FlutterRustBridgeTask(
      callFfi: (port_) => _platform.inner
          .wire_run_peer_node(port_, arg0, arg1, arg2, arg3, arg4),
      parseSuccessData: _wire2api_unit,
      parseErrorData: null,
      constMeta: kRunPeerNodeConstMeta,
      argValues: [
        webserverAddr,
        webserverPort,
        adminAddr,
        accessToken,
        refreshToken
      ],
      hint: hint,
    ));
  }

  FlutterRustBridgeTaskConstMeta get kRunPeerNodeConstMeta =>
      const FlutterRustBridgeTaskConstMeta(
        debugName: "run_peer_node",
        argNames: [
          "webserverAddr",
          "webserverPort",
          "adminAddr",
          "accessToken",
          "refreshToken"
        ],
      );

  void dispose() {
    _platform.dispose();
  }
// Section: wire2api

  void _wire2api_unit(dynamic raw) {
    return;
  }
}

// Section: api2wire

@protected
int api2wire_u16(int raw) {
  return raw;
}

@protected
int api2wire_u8(int raw) {
  return raw;
}

// Section: finalizer

class DpnBridgePlatform extends FlutterRustBridgeBase<DpnBridgeWire> {
  DpnBridgePlatform(ffi.DynamicLibrary dylib) : super(DpnBridgeWire(dylib));

// Section: api2wire

  @protected
  ffi.Pointer<wire_uint_8_list> api2wire_String(String raw) {
    return api2wire_uint_8_list(utf8.encoder.convert(raw));
  }

  @protected
  ffi.Pointer<wire_uint_8_list> api2wire_uint_8_list(Uint8List raw) {
    final ans = inner.new_uint_8_list_0(raw.length);
    ans.ref.ptr.asTypedList(raw.length).setAll(0, raw);
    return ans;
  }
// Section: finalizer

// Section: api_fill_to_wire
}

// ignore_for_file: camel_case_types, non_constant_identifier_names, avoid_positional_boolean_parameters, annotate_overrides, constant_identifier_names

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint

/// generated by flutter_rust_bridge
class DpnBridgeWire implements FlutterRustBridgeWireBase {
  @internal
  late final dartApi = DartApiDl(init_frb_dart_api_dl);

  /// Holds the symbol lookup function.
  final ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
      _lookup;

  /// The symbols are looked up in [dynamicLibrary].
  DpnBridgeWire(ffi.DynamicLibrary dynamicLibrary)
      : _lookup = dynamicLibrary.lookup;

  /// The symbols are looked up with [lookup].
  DpnBridgeWire.fromLookup(
    ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName) lookup,
  ) : _lookup = lookup;

  void store_dart_post_cobject(DartPostCObjectFnType ptr) {
    return _store_dart_post_cobject(ptr);
  }

  late final _store_dart_post_cobjectPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(DartPostCObjectFnType)>>(
    'store_dart_post_cobject',
  );
  late final _store_dart_post_cobject = _store_dart_post_cobjectPtr
      .asFunction<void Function(DartPostCObjectFnType)>();

  Object get_dart_object(int ptr) {
    return _get_dart_object(ptr);
  }

  late final _get_dart_objectPtr =
      _lookup<ffi.NativeFunction<ffi.Handle Function(ffi.UintPtr)>>(
    'get_dart_object',
  );
  late final _get_dart_object =
      _get_dart_objectPtr.asFunction<Object Function(int)>();

  void drop_dart_object(int ptr) {
    return _drop_dart_object(ptr);
  }

  late final _drop_dart_objectPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.UintPtr)>>(
    'drop_dart_object',
  );
  late final _drop_dart_object =
      _drop_dart_objectPtr.asFunction<void Function(int)>();

  int new_dart_opaque(Object handle) {
    return _new_dart_opaque(handle);
  }

  late final _new_dart_opaquePtr =
      _lookup<ffi.NativeFunction<ffi.UintPtr Function(ffi.Handle)>>(
    'new_dart_opaque',
  );
  late final _new_dart_opaque =
      _new_dart_opaquePtr.asFunction<int Function(Object)>();

  int init_frb_dart_api_dl(ffi.Pointer<ffi.Void> obj) {
    return _init_frb_dart_api_dl(obj);
  }

  late final _init_frb_dart_api_dlPtr =
      _lookup<ffi.NativeFunction<ffi.IntPtr Function(ffi.Pointer<ffi.Void>)>>(
    'init_frb_dart_api_dl',
  );
  late final _init_frb_dart_api_dl = _init_frb_dart_api_dlPtr
      .asFunction<int Function(ffi.Pointer<ffi.Void>)>();

  void wire_run_peer_node(
    int port_,
    ffi.Pointer<wire_uint_8_list> webserver_addr,
    int webserver_port,
    ffi.Pointer<wire_uint_8_list> admin_addr,
    ffi.Pointer<wire_uint_8_list> access_token,
    ffi.Pointer<wire_uint_8_list> refresh_token,
  ) {
    return _wire_run_peer_node(
      port_,
      webserver_addr,
      webserver_port,
      admin_addr,
      access_token,
      refresh_token,
    );
  }

  late final _wire_run_peer_nodePtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
            ffi.Int64,
            ffi.Pointer<wire_uint_8_list>,
            ffi.Uint16,
            ffi.Pointer<wire_uint_8_list>,
            ffi.Pointer<wire_uint_8_list>,
            ffi.Pointer<wire_uint_8_list>,
          )>>('wire_run_peer_node');
  late final _wire_run_peer_node = _wire_run_peer_nodePtr.asFunction<
      void Function(
        int,
        ffi.Pointer<wire_uint_8_list>,
        int,
        ffi.Pointer<wire_uint_8_list>,
        ffi.Pointer<wire_uint_8_list>,
        ffi.Pointer<wire_uint_8_list>,
      )>();

  ffi.Pointer<wire_uint_8_list> new_uint_8_list_0(int len) {
    return _new_uint_8_list_0(len);
  }

  late final _new_uint_8_list_0Ptr = _lookup<
          ffi
          .NativeFunction<ffi.Pointer<wire_uint_8_list> Function(ffi.Int32)>>(
      'new_uint_8_list_0');
  late final _new_uint_8_list_0 = _new_uint_8_list_0Ptr
      .asFunction<ffi.Pointer<wire_uint_8_list> Function(int)>();

  void free_WireSyncReturn(WireSyncReturn ptr) {
    return _free_WireSyncReturn(ptr);
  }

  late final _free_WireSyncReturnPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(WireSyncReturn)>>(
    'free_WireSyncReturn',
  );
  late final _free_WireSyncReturn =
      _free_WireSyncReturnPtr.asFunction<void Function(WireSyncReturn)>();
}

final class _Dart_Handle extends ffi.Opaque {}

final class wire_uint_8_list extends ffi.Struct {
  external ffi.Pointer<ffi.Uint8> ptr;

  @ffi.Int32()
  external int len;
}

typedef DartPostCObjectFnType = ffi.Pointer<
    ffi.NativeFunction<
        ffi.Bool Function(DartPort port_id, ffi.Pointer<ffi.Void> message)>>;
typedef DartPort = ffi.Int64;
