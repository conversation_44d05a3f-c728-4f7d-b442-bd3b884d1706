// This file initializes the dynamic library and connects it with the stub
// generated by flutter_rust_bridge_codegen.

import 'dart:ffi';

import 'bridge_generated.dart';
import 'bridge_definitions.dart';
// export 'bridge_definitions.dart';

// // Re-export the bridge so it is only necessary to import this file.
// export 'bridge_generated.dart';
import 'dart:io' as io;

const _base = 'dpn_bridge';

// On MacOS, the dynamic library is not bundled with the binary,
// but rather directly **linked** against the binary.
final _dylib = io.Platform.isWindows ? './rust/target/dpn_bridge.dll' : 'lib$_base.so';

final DpnBridge api = DpnBridgeImpl(io.Platform.isIOS
    ? DynamicLibrary.process()
    : io.Platform.isMacOS
        ? DynamicLibrary.executable()
        : DynamicLibrary.open(_dylib));
