// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

// ignore_for_file: lines_longer_than_80_chars, constant_identifier_names
// ignore: avoid_classes_with_only_static_members

class LocaleKeys {
  LocaleKeys._();
  static const total_sessions = 'total_sessions';
  static const refresh = 'refresher';

  static const total_rewards = 'total_rewards';
  static const confirm = 'confirm';
  static const cancel = 'cancel';
  static const save = 'save';
  static const network_share_rewards = 'network_share_rewards';
  static const referrals_rewards = 'referrals_rewards';
  static const claimable = 'claimable';
  static const done = 'done';
  static const close = 'close';
  static const back = 'back';
  static const skip = 'skip';
  static const next = 'next';
  static const account = 'account';
  static const wallet = 'wallet';
  static const news = 'news';
  static const same_ip = 'same_ip';
  static const success = 'success';
  static const report = 'report';
  static const logout = 'logout';
  static const logout_successfully = 'logout_successfully';
  static const confirm_logout = 'confirm_logout';
  static const login_expired = 'login_expired';
  static const press_again_to_exit = 'press_again_to_exit';
  static const no_internet_connection = 'no_internet_connection';
  static const internet_connection_restored = 'internet_connection_restored';
  static const it_should_take = 'it_should_take';
  static const transactions_is_progress = 'transactions_is_progress';
  static const application_is_under_maintenance = 'application_is_under_maintenance';
  static const u2dpn_will_automatically = 'u2dpn_will_automatically';
  static const share = 'share';
  static const total_online = 'total_online';
  //-------login----------
  static const become_a_provider = 'become_a_provider';
  static const sign_in_with_google = 'sign_in_with_google';
  static const sign_in_with_apple = 'sign_in_with_apple';
  static const sign_in = 'sign_in';
  static const i_accept_all = 'i_accept_all';
  static const privacy_policy = 'privacy_policy';
  //------dpn subnet------
  static const history_showcase = 'history_showcase';
  static const statistic_showcase = 'statistic_showcase';
  static const setting_showcase = 'setting_showcase';
  static const button_connected_showcase = 'button_connected_showcase';
  static const active_sessions_showcase = 'active_sessions_showcase';
  static const bandwidth_price = 'bandwidth_price';
  static const disconnected = 'disconnected';
  static const connecting = 'connecting';
  static const connection_time = 'connection_time';
  static const the_loyalty_point = 'the_loyalty_point';
  static const node_activated = 'node_activated';
  static const session_connected = 'session_connected';
  static const reconnect_failed = 'reconnect_failed';
  static const location_bonus = 'location_bonus';
  static const search_location = 'search_location';
  static const reward_bonus = 'reward_bonus';
  static const location = 'location';
  static const the_more_you_connect_exp = 'the_more_you_connect_exp';
  static const the_more_you_connect_location = 'the_more_you_connect_location';
  static const what_is_EXP = 'what_is_EXP';
  static const what_is_location_bonus = 'what_is_location_bonus';
  static const view_all_tiers = 'view_all_tiers';
  static const view_all_location = 'view_all_location';
  static const referral_bonus = 'referral_bonus';
  static const discount_platform_fee = 'discount_platform_fee';
  static const fee_reduction = 'fee_reduction';
  static const rank_tiers = 'rank_tiers';
  static const need = 'need';
  static const loyalty_points_to_tier_up = 'loyalty_points_to_tier_up';
  static const bandwidth = 'bandwidth';
  static const price = 'price';
  static const rewards = 'rewards';
  static const active_sessions = 'active_sessions';
  static const current_sessions = 'current_sessions';
  static const there_is_no_connection = 'there_is_no_connection';
  static const start_sharing_to_get_new_connection = 'start_sharing_to_get_new_connection';
  static const connect_dpn_subnet = 'connect_dpn_subnet';
  static const statistic = 'statistic';
  static const total_bandwidth_shared = 'total_bandwidth_shared';
  static const rewards_claimable = 'rewards_claimable';
  static const go_to_account = 'go_to_account';
  static const how_to_claim_rewards = 'how_to_claim_rewards';
  // static const session_history = 'session_history';
  static const sessions = 'sessions';
  static const no_data_history = 'no_data_history';
  static const start_in = 'start_in';
  static const end_in = 'end_in';
  static const duration = 'duration';
  static const selling_price = 'selling_price';
  static const selling_price_per_hour = 'selling_price_per_hour';
  static const selling_price_per_gb = 'selling_price_per_gb';
  static const total_received = 'total_received';
  static const status = 'status';
  static const reason = 'reason';
  static const important_note = 'important_note';
  static const you_should_open_u2dpn = 'you_should_open_u2dpn';
  static const got_it = 'got_it';
  static const shared_bandwidth = 'shared_bandwidth';
  static const last_updated = 'last_updated';
  static const started_at = 'started_at';
  static const ended_at = 'ended_at';
  static const duration_fee = 'duration_fee';
  static const stop_sharing = 'stop_sharing';
  static const start_sharing = 'start_sharing';
  static const connecting_to_U2DPN_subnet = 'connecting_to_U2DPN_subnet';
  static const connected = 'connected';
  static const edit_price = 'edit_price';
  static const our_suggestion = 'our_suggestion';
  static const the_cost_for_each = 'the_cost_for_each';
  static const each_country_has_a_different = 'each_country_has_a_different';
  // session page
  static const today_boost = 'today_boost';
  static const running_session = 'running_session';
  static const all_sessions = 'all_sessions';
  static const bandwidth_shared = 'bandwidth_shared';
  static const total_online_time = 'total_online_time';
  static const rewards_from_sharing = 'rewards_from_sharing';
  static const session_detail = 'session_detail';
  static const sameip = 'sameip';
  static const invalidauth = 'invalidauth';
  static const streamreadwritefailed = 'streamreadwritefailed';
  static const no_connection_is_running = 'no_connection_is_running';
  static const proxy_server_shutted_down = 'proxy_server_shutted_down';
  //
  static const your_connection_time_showcase = 'your_connection_time_showcase';
  static const your_curent_rank_showcase = 'your_curent_rank_showcase';
  static const your_current_point_showcase = 'your_current_point_showcase';
  static const time_based_fee = 'time_based_fee';
  static const shared_bandwidth_fee = 'shared_bandwidth_fee';
  static const click_here_save_sharing_price_information = 'click_here_save_sharing_price_information';
  static const are_you_sure_wanna_change_selling_price = 'are_you_sure_wanna_change_selling_price';
  static const price_updated = 'price_updated';
  static const the_suggested_bandwidth_sharing_rate = 'the_suggested_bandwidth_sharing_rate';
  static const no_data = 'no_data';
  static const about = 'about';
  static const join_U2DPN = 'join_U2DPN';
  static const hour = 'hour';
  static const seconds = 'seconds';
  static const mimutes = 'minutes';
  static const tapPoint = 'tapPoint';
  static const session_hash = 'session_hash';
  static const gross_amount = 'gross_amount';
  static const platform_fee = 'platform_fee';
  static const net_amount = 'net_amount';
  static const referral_fee = 'referral_fee';
  static const automatically_connect = 'automatically_connect';
  static const tutorial = 'tutorial';
  static const mark_as_all_read = 'mark_as_all_read';
  static const sponsor_id = 'sponsor_id';
  static const try_again = 'try_again';
  static const connection_failed = 'connection_failed';
  static const could_not_connect = 'could_not_connect';
  //-----------Referrals-----------
  static const referrals = 'referrals';
  static const total_transaction_referees = 'total_transaction_referees';
  static const total_commission_transaction = 'total_commission_transaction';
  static const commission_claimable = 'commission_claimable';
  static const link_with_your_friends = 'link_with_your_friends';
  static const view_referral_history = 'view_referral_history';
  static const create_ref_code = 'create_ref_code';
  static const copied = 'copied';
  static const enter_referral_code = 'enter_referral_code';
  static const you_can_only = 'you_can_only';
  static const paste = 'paste';
  static const are_you_sure_claim = 'are_you_sure_claim';
  static const you_has_successfully = 'you_has_successfully';
  static const create_referral_code = 'create_referral_code';
  static const type_in_any_letters = 'type_in_any_letters';
  static const referral_code_must = 'referral_code_must';
  static const make_sure_your_referral_code = 'make_sure_your_referral_code';
  static const create_and_save = 'create_and_save';
  static const your_referral_code_was = 'your_referral_code_was';
  static const referrals_History = 'referrals_History';
  static const there_is_no_data_available = 'there_is_no_data_available';
  static const link_referral_code = 'link_referral_code';
  static const referral_code = 'referral_code';
  static const your_code = 'your_code';
  static const total_referees = 'total_referees';
  static const total_commission = 'total_commission';
  static const copy = 'copy';
  static const click_link_with_friends = 'click_link_with_friends';
  static const link_code = 'link_code';
  static const referral_rewards = 'referral_rewards';
  static const total_referee_transactions = 'total_referee_transactions';
  static const total_invited = 'total_invited';
  static const invite_history = 'invite_history';
  static const view_all = 'view_all';

  //----------------Account------------------------
  static const my_account = 'my_account';
  static const claim_all_rewards = 'claim_all_rewards';
  static const claim_history = 'claim_history';
  static const you_can_receive_claimable = 'you_can_receive_claimable';
  static const enter_u2u_wallet = 'enter_u2u_wallet';
  static const claim_rewards = 'claim_rewards';
  static const network = 'network';
  static const fee = 'fee';
  static const destination_address = 'destination_address';
  static const claim_now = 'claim_now';
  static const claim_processing = 'claim_processing';
  static const the_claim_processing = 'the_claim_processing';
  static const view_on_claim_history = 'view_on_claim_history';
  static const enter_wallet_address = 'enter_wallet_address';
  static const we_will_send = 'we_will_send';
  static const change_u2u_wallet = 'change_u2u_wallet';
  static const your_ranking = 'your_ranking';
  static const your_rank = 'your_rank';
  static const how_to_up_your_rank = 'how_to_up_your_rank';
  static const complete_more_missions = 'complete_more_missions';
  static const recent_points = 'recent_points';
  static const benefits = 'benefits';
  static const none = 'none';
  static const fee_reduction_15 = 'fee_reduction_15';
  static const fee_reduction_13 = 'fee_reduction_13';
  static const fee_reduction_11 = 'fee_reduction_11';
  static const fee_reduction_9 = 'fee_reduction_9';
  static const fee_reduction_7 = 'fee_reduction_7';
  static const submit = 'submit';
  static const how_to_install_U2U_wallet = 'how_to_install_U2U_wallet';
  static const view_claim_history = 'view_claim_history';
  static const task_rewards = 'task_rewards';
  static const copied_successfully = 'copied_successfully';
  static const unconnected_wallet = 'unconnected_wallet';
  static const connect = 'connect';
  static const u2U_chain = 'u2U_chain';
  static const silver = 'silver';
  static const u2_DPN_is_a_decentralized = 'u2_DPN_is_a_decentralized';
  static const language = 'language';
  static const english = 'english';
  static const user_guide = 'user_guide';
  static const legal = 'legal';
  static const send_feedback = 'send_feedback';
  static const how_to_use_U2DPN_interface = 'how_to_use_U2DPN_interface';

  static const bronze = 'bronze';
  static const platinum = 'platinum';
  static const diamond = 'diamond';
  static const gold = 'gold';
  static const wallet_address_is_incorrect = 'wallet_address_is_incorrect';
  static const update_language_failed = 'update_language_failed';
  static const update_language_success = 'update_language_success';
  static const my_wallet = 'my_wallet';
  static const change_wallet = 'change_wallet';
  static const copy_wallet_address = 'copy_wallet_address';
  static const disconnect_wallet = 'disconnect_wallet';
  static const are_you_sure_you_want_wallet = 'are_you_sure_you_want_wallet';
  static const balance = 'balance';
  static const connect_u2u_wallet = 'connect_u2u_wallet';
  static const wallet_address = 'wallet_address';
  static const connect_wallet = 'connect_wallet';
  static const confirm_withdraw = 'confirm_withdraw';
  static const confirm_withdraw_description = 'confirm_withdraw_description';
  static const withdraw_processing = 'withdraw_processing';
  static const withdraw_note = 'withdraw_note';
  static const claim_reward = 'claim_reward';
  static const claim_detail = 'claim_detail';
  static const transaction_hash = 'transaction_hash';
  static const from = 'from';
  static const to = 'to';
  static const amount = 'amount';
  static const network_fee = 'network_fee';
  static const date = 'date';
  static const u2u_is_a_decentralized_private_network = 'u2u_is_a_decentralized_private_network';

  //--------------Setting-------------------------
  static const setting = 'setting';
  static const about_U2DPN = 'about_U2DPN';
  static const vietnamese = 'vietnamese';

  static const term_of_service = 'term_of_service';
  static const introduction = 'introduction';
  static const links = 'links';
  static const make_a_review = 'make_a_review';
  static const reconnect_dpn = 'reconnect_dpn';
  static const turn_on_reconnect_dpn = 'turn_on_reconnect_dpn';
  static const application_will_automatically = 'application_will_automatically';
  static const turn_off_reconnect_dpn = 'turn_off_reconnect_dpn';
  static const the_system_will_not_automatically = 'the_system_will_not_automatically';
  static const turn_off = 'turn_off';
  static const turn_on_now = 'turn_on_now';
  static const change_log = 'change_log';
  static const update_new_features = 'update_new_features';

  //------------Guide install wallet--------------
  static const step_by_step_to_install = 'step_by_step_to_install';
  static const step_1 = 'step_1';
  static const step_2 = 'step_2';
  static const step_3 = 'step_3';
  static const step_4 = 'step_4';
  static const step_5 = 'step_5';
  static const install_U2U_super_app = 'install_U2U_super_app';
  static const create_new_wallet = 'create_new_wallet';
  static const after_creating_your_first_wallet = 'after_creating_your_first_wallet';
  static const open_U2DPN_provider_app = 'open_U2DPN_provider_app';
  static const paste_your_wallet_address = 'paste_your_wallet_address';
  //----------------Notification----------------------
  static const you_cannot_enter_your_referral_code = 'you_cannot_enter_your_referral_code';
  static const login_successfully = 'login_successfully';
  static const login_fail = 'login_fail';
  static const notification = 'notification';
  static const due_to_unusually_high_demand = 'due_to_unusually_high_demand';
  static const connect_to_u2dpn = 'connect_to_u2dpn';
    static const reconnect_dpn_is_temporarily_streamreadwritefailed = 'reconnect_dpn_is_temporarily_streamreadwritefailed';
  static const reconnect_dpn_is_temporarily_sameip = 'reconnect_dpn_is_temporarily_sameip';
  static const reconnect_dpn_has_been_disabled = 'reconnect_dpn_has_been_disabled';
  static const your_app_will_automatically = 'your_app_will_automatically';
  static const you_will_need_to_manually = 'you_will_need to manually';
  static const reconnect_dpn_is_on = 'reconnect_dpn_is_on';
  static const reconnect_dpn_is_off = 'reconnect_dpn_is_off';
  static const your_device_is_offline = 'your_device_is_offline';
  static const network_connection_warning = 'network_connection_warning';
  static const network_connection_warning_description = 'network_connection_warning_description';

}

// class AppTranslation {
//   static Map<String, Map<String, String>> translations = {
//     'en': Locales.en,
//     'vi': Locales.vi,
//   };
// }

// class Locales {
//   static const en = {
//     'app_name': 'U2DPN',
//   };

//   static const vi = {
//     'app_name': 'U2DPN',
//   };
// }
