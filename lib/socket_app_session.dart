import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/data/app_config.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/socket_app.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class SocketAppSession {
  // private active
  // private conn
  // new() {

  // }

  // fn run() {
  //   cheeck_pnig_pong()
  //   listen()
  // }

  // WebSocket channel for communication
  static WebSocketChannel? channelConnection;
  static Timer? pingTimer;

  static DateTime? lastHeartbeatAckTime;
  static Timer? heartbeatCheckTimer;
  static bool active = true;
  // GetX controller for managing connection-related state
  static ConnectionController connectionController =
      Get.find<ConnectionController>();
  // Establish WebSocket connection
  static Future connectSocketSession() async {
    // Waits for ws server up
    try {
      await channelConnection?.sink.close();
      pingTimer?.cancel();
      lastHeartbeatAckTime = DateTime.now();
      heartbeatCheckTimer?.cancel();

      // Connect to WebSocket server using AppConfig instance's wsConnection
      channelConnection =
          IOWebSocketChannel.connect(AppConfig.instance.wsConnection);
      // Send authorization token to the server

      // channelConnection!.sink.add(jsonEncode({"Ping": null}));
      await Future.delayed(const Duration(milliseconds: 1000));
      // await startPeriodicFunction();

      channelConnection!.sink.add(jsonEncode(
          {"type": "authenticate", "access_token": SettingConfig.token}));

      await Future.delayed(const Duration(milliseconds: 500));

      pingTimer = Timer.periodic(Duration(seconds: 30), (timer) async {
        if (connectionController.statusConnection.value ==
            StateButtonConnect.start) {
       
          await Future.delayed(const Duration(milliseconds: 500));
          channelConnection!.sink.add(jsonEncode({"type": "heartbeat"}));
        } else {
          hideApp();
        }
      });
      heartbeatCheckTimer =
          Timer.periodic(const Duration(seconds: 45), (timer) async {
        final now = DateTime.now();
        if (connectionController.statusConnection.value ==
            StateButtonConnect.start) {
          if (lastHeartbeatAckTime == null ||
              now.difference(lastHeartbeatAckTime!).inSeconds > 45) {
            logger.w(
                "⚠️ No heartbeat_ack received in last 60 seconds → reconnecting...");
            connectSocketSession();
          }
        }
      });
      listenConnection();
    } catch (e) {
      pingTimer?.cancel();
      await channelConnection?.sink.close();
      SocketApp.logout();
      // Handle connection error
      logger.d('Error connecting to WebSocket: $e');
    }
  }

  static void refreshToken() {}
  // Listen to incoming messages on the WebSocket channel
  static void listenConnection() {
    channelConnection!.stream.listen(
      (data) async {
        logger.d(data);

        // Decode the incoming JSON message
        Map<String, dynamic> jsonValue = jsonDecode(data);
        final String? type = jsonValue['type'];

        // Handle different types of messages
        switch (type) {
          case 'auth_success':
            if (channelConnection != null) {
              try {
                await Future.delayed(const Duration(milliseconds: 500));
                channelConnection!.sink.add(jsonEncode({"type": "heartbeat"}));
              } catch (e) {
                connectSocketSession();
                // Optional: Reconnect logic here
              }
            }

            logger.d(
                "✅ Auth success! Provider: ${jsonValue["payload"]["provider_addr"]}");
            break;

          case 'sessions' || 'initial_sessions' || 'session_update':
            final sessions = jsonValue["payload"]["sessions"];
            await connectionController.getActiveConnection(sessions);
            break;

          case 'heartbeat_ack':
            lastHeartbeatAckTime = DateTime.now();
            logger.d("✅ Heartbeat acknowledged at $lastHeartbeatAckTime");
            break;

          case 'UnknownMessage':
            // Handle unknown message type
            break;

          default:
          // Handle other message types if needed
        }
      },
    );
  }

  static void logout() async {
    pingTimer?.cancel();
    heartbeatCheckTimer?.cancel();
    await channelConnection?.sink.close();
  }

  static void hideApp() async {
    pingTimer?.cancel();
    heartbeatCheckTimer?.cancel();
    await Future.delayed(const Duration(milliseconds: 500));
    await channelConnection?.sink.close();
  }
}
