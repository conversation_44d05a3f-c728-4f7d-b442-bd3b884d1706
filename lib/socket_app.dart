import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background/flutter_background.dart';

import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
//import 'package:u2u_dpn/app/routes/navigate_keys.dart';
import 'package:u2u_dpn/data/app_config.dart';
//import 'package:u2u_dpn/data/provider/my_callback.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/socket_app_session.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/CustomContainerShadow.dart';
import 'package:u2u_dpn/widget/custom_raisebutton.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
//import 'package:u2u_dpn/widget/ultil.dart';
import 'package:web_socket_channel/io.dart';

class SocketApp {
  static late IOWebSocketChannel channel;
  static ConnectionController connectionController = Get.find();
  static Timer? _loyaltyPointTimer;
  static var disableButton = false.obs;
  static bool isBottomSheetOpen = false;
  static int countReconnecting = 0;
  

  static Future<void> connectSocket() async {
    logger.d('connectSocket');
    final wsAddr =
        'ws://${AppConfig.instance.wsHost}:${AppConfig.instance.wsPort.toString()}';
    logger.d(wsAddr);
    // Waits for ws server up
    await Future.delayed(const Duration(milliseconds: 500));

    channel = IOWebSocketChannel.connect(wsAddr);

    // Then start connection to masternode
    await Future.delayed(const Duration(milliseconds: 100));
    listen();
  }

  static Future<void> startConnection() async {
    if (countReconnecting < 11) {
      try {
        countReconnecting++;
        connectionController.isShutdown.value = false;
        connectionController.isConnecting.value = true;

        await connectSocket();
        Future.delayed(
          const Duration(milliseconds: 500),
        );
        channel.sink.add(
            "{\"StartConnection\":{\"access_token\":\"${SettingConfig.token}\",\"refresh_token\":\"${SettingConfig.refreshToken}\"}}");
        await SocketAppSession.connectSocketSession();

        await Future.delayed(const Duration(seconds: 10), () {
          if (connectionController.statusConnection.value !=
                  StateButtonConnect.start &&
              connectionController.isConnecting.value == true) {
            SocketAppSession.logout();
            //  stopConnection();
            Future.delayed(const Duration(seconds: 1), () {
              startConnection();
            });

            logger.d('Fallback StartConnection Sent after 1s');
          }
        });

        // channel.sink.add({

        //   "StartConnection": {
        //     "access_token": SettingConfig.token,
        //     "refresh_token": SettingConfig.refreshToken,
        //   }
        // });
        if (!kIsWeb) {
          if (Platform.isAndroid) {
            final connectionEnableBackground =
                await FlutterBackground.enableBackgroundExecution();
            if (connectionEnableBackground) {
              // NotificationService().newNotification(LocaleKeys.connected.tr, false);
            } else {
              Get.snackbar(
                '',
                '',
                titleText: const SizedBox(height: 3),
                messageText: const Text(
                  'Enable Background Error',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white),
                ),
                padding: const EdgeInsets.only(right: 16, left: 16, bottom: 8),
                margin: const EdgeInsets.only(right: 16, left: 16, bottom: 32),
                //maxWidth: 200.w,
                // ignore: deprecated_member_use
                backgroundColor: Colors.red.withOpacity(0.6),
                snackPosition: SnackPosition.BOTTOM,
              );
            }
          }
        }
      } catch (e) {
        logger.d('Error open socket: $e');
      }
    } else {
      logger.d('Too many reconnecting attempts, stopping connection');
      stopConnection();
      showBottomSheetDisconnect(message: LocaleKeys.reconnect_failed.tr);
      Future.delayed(const Duration(seconds: 9), () {
        countReconnecting = 0;
      });
      return;
    }
  }

  static void restartConnection() async {
    try {
      if (connectionController.statusConnection.value ==
          StateButtonConnect.start) {
        return;
      }
      await channel.sink.close();
      SocketAppSession.logout();

      await Future.delayed(const Duration(seconds: 5), () async {
        if (connectionController.statusConnection.value ==
            StateButtonConnect.start) {
          return;
        }

        logger.d('restart');
        await Future.delayed(const Duration(milliseconds: 2000));

        startConnection();
      });
    } catch (e) {
      //   startConnection();
    }
  }

  static void stopConnection() {
    try {
      connectionController.statusConnection.value = StateButtonConnect.stop;
      connectionController.isConnecting.value = false;
      connectionController.listActiveConnection.value = [];
      channel.sink.add("{\"StopConnection\":[]}");
      // connectionController.statusConnection.value = StateButtonConnect.stop;
      // connectionController.isConnecting.value = false;
      _loyaltyPointTimer?.cancel();

      if (!kIsWeb) {
        if (Platform.isAndroid) {
          FlutterBackground.disableBackgroundExecution();
          // NotificationService().plugin.cancel(0);
        }
      }
    } catch (e) {
      // connectSocket();
    }
  }

  static void listen() {
    channel.stream.listen(
        (event) async {
          Map<String, dynamic> jsonValue = jsonDecode(event);
          logger.d(jsonValue);
          connectionController.isConnecting.value = false;

          switch (jsonValue.keys.first) {
            case 'Info':
              switch (jsonValue.values.first) {
                case 'RECONNECTING':
                  connectionController.statusConnection.value =
                      StateButtonConnect.start;

                  break;
                case 'ASSIGNING_MASTERNODE':
                  connectionController.isConnecting.value = true;
                  break;
                case 'RUNNING_PROXY_SERVER':
                  connectionController.isConnecting.value = false;
                  connectionController.statusConnection.value =
                      StateButtonConnect.start;
                  SettingConfig.isConnection = true;
                  connectionController.startSesionLoyaltyPoint();
                  startLoyaltyPointTimer();
                  countReconnecting = 0;

                  break;
                default:
                  break;
              }
              break;
            // case 'StartConnection':
            //   logger.d('start');
            //   connectionController.isConnecting.value = false;
            //   connectionController.statusConnection.value =
            //       StateButtonConnect.start;
            //         startLoyaltyPointTimer();
            //   break;
            case 'StopConnection':
              SocketAppSession.pingTimer?.cancel();
              SettingConfig.isConnection = false;
              connectionController.statusConnection.value =
                  StateButtonConnect.stop;
              connectionController.endSessionLoyaltyPoint();
              stopLoyaltyPointTimer();

              break;
            case 'ConnectionStopped':
              connectionController.listActiveConnection.clear();

              connectionController.statusConnection.value =
                  StateButtonConnect.stop;
              SettingConfig.isConnection = false;
              stopLoyaltyPointTimer();
              SocketAppSession.pingTimer?.cancel();

              String message = jsonValue.values.first.toString().toLowerCase();
              if (!contains(message, 'PROXY_SERVER_SHUTTED_DOWN')) {
                Sentry.captureMessage(
                    'UserId: ${SettingConfig.user_id} - ${jsonValue.values.toString()} - State Connection: ${SettingConfig.isConnection ? 'Start' : 'Stop'} }',
                    level: SentryLevel.error); // Khi lỗi trả về
              }
              if (contains(message, 'sameip')) {
                showBottomSheetDisconnect(
                    message: LocaleKeys.sameip.tr,
                    image: AppImages.same_ip,
                    recommand: LocaleKeys.reconnect_dpn_has_been_disabled.tr,
                    messageRecommand:
                        LocaleKeys.reconnect_dpn_is_temporarily_sameip.tr);
              } else if (contains(message, 'invalidauth')) {
                showBottomSheetDisconnect(message: LocaleKeys.invalidauth.tr);
              } else if (contains(message, 'no_connection_is_running')) {
                if (connectionController.isShutdown.value == false) {
                  showBottomSheetDisconnect(
                      message: LocaleKeys.no_connection_is_running.tr);
                }
              } else if (contains(message, 'REFRESHED_AUTH_TOKENS')) {
                restartConnection();
              } else if (contains(message, 'streamreadwritefailed')) {
                showBottomSheetDisconnect(
                    message: LocaleKeys.streamreadwritefailed.tr,
                    recommand: LocaleKeys.reconnect_dpn_has_been_disabled.tr,
                    messageRecommand: LocaleKeys
                        .reconnect_dpn_is_temporarily_streamreadwritefailed.tr);
                // delay một chút để đảm bảo bottomSheet không bị đè nhau
                Future.delayed(const Duration(milliseconds: 500), () async {
                  Get.find<ConnectionController>().startTest();
                });

                // call function speedtest here
              } else if (contains(message, 'proxy_server_shutted_down')) {
                logger.d('PROXY_SERVER_SHUTTED_DOWN');
                connectionController.isShutdown.value = true;
              } else {
                if (!contains(
                    message, 'connection closed due to error: closed')) {
                  logger.d(connectionController.isShutdown.value);
                  if (connectionController.isShutdown.value == false) {
                    if (SettingConfig.autoConnection) {
                      restartConnection();
                    } else {
                      showBottomSheetDisconnect();
                    }
                  }
                }
              }
              connectionController.endSessionLoyaltyPoint();
              SocketAppSession.logout();
              break;
            case 'MessageParsingError':
              break;
            default:
          }
        },
        onError: (e) {},
        onDone: () {
          //   restartConnection();
        });
  }

  static bool contains(String source, String keyword) {
    return source.contains(keyword.toLowerCase());
  }

  static void logout() async {
    stopConnection();
    await channel.sink.close();
  }

  static void startLoyaltyPointTimer() {
    _loyaltyPointTimer?.cancel(); // Xóa Timer cũ nếu có

    // if (!kIsWeb && Platform.isAndroid) {
    //   // Đăng ký công việc chạy nền mỗi 15 phút trên Android
    //   Workmanager().registerPeriodicTask(
    //     "loyaltyTask",
    //     "loyaltyPointTask",
    //     frequency: const Duration(minutes: 1),
    //   );
    // }
    // Chạy Timer trên iOS hoặc Web
    connectionController.getTotalTime();
    _loyaltyPointTimer = Timer.periodic(const Duration(seconds: 50), (timer) {
      connectionController.getTotalTime();
      connectionController.heartBeat();
    }
        // logger.d('Đã đăng ký WorkManager để gọi getLoyalPoint() mỗi 1 phút');

        );
  }

  static void stopLoyaltyPointTimer() {
    _loyaltyPointTimer?.cancel();
    _loyaltyPointTimer = null;
    logger.d('Đã hủy Timer getLoyalPoint()');
  }

  static showBottomSheetDisconnect(
      {String? image,
      String? title,
      String? message,
      String? recommand,
      String? messageRecommand}) {
    if (SettingConfig.token.isNotEmpty) {
      connectionController.statusConnection.value = StateButtonConnect.stop;

      if (!isBottomSheetOpen) {
        isBottomSheetOpen = true;

        return Get.bottomSheet(
          Container(
            decoration: const BoxDecoration(
              color: AppColor.neutral700,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            constraints: BoxConstraints(
              maxHeight: Get.height * 2 / 3, // Tối đa 2/3 màn hình
              // minHeight: Get.height * 3 / 4,//
            ),
            child: Column(
              children: [
                // Drag bar
                Container(
                  width: 85,
                  height: 4,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(16)),
                    color: Color(0xFF4C556B),
                  ),
                  margin: const EdgeInsets.only(top: 8, bottom: 16),
                ),

                // Nội dung scroll được
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Align(
                          alignment: Alignment.topRight,
                          child: Icon(Icons.close, color: AppColor.neutral400),
                        ).onTap(() => Get.back()),
                        Image.asset(
                          image ?? AppImages.connection_failed_warning,
                          width: 80,
                        ),
                        CustomText(
                          text: title ?? LocaleKeys.connection_failed.tr,
                          fontSize: 20,
                        ).paddingOnly(top: 24, bottom: 8),
                        CustomText(
                          text: message ?? LocaleKeys.could_not_connect.tr,
                          color: AppColor.neutral300,
                          textAlign: TextAlign.center,
                          fontSize: 14,
                        ),
                        if (recommand != null)
                          CustomContainerShadow(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Image.asset(AppImages.shield),
                                    CustomText(text: recommand)
                                        .paddingOnly(left: 10),
                                  ],
                                ),
                                CustomText(
                                  text: messageRecommand ?? '',
                                  color: AppColor.neutral300,
                                ).paddingTop(16),
                              ],
                            ),
                          ).paddingSymmetric(vertical: 24).paddingBottom(16),
                      ],
                    ),
                  ),
                ),
                if (recommand == null)
                  // Nút Cancel luôn nằm dưới
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                    child: CostomRaisedButtom(
                      colorsGradient: const [
                        AppColor.neutral600,
                        AppColor.neutral600,
                      ],
                      colorText: AppColor.neutral100,
                      name: LocaleKeys.cancel.tr,
                      function: () {
                        isBottomSheetOpen = false;
                        Get.back();
                      },
                    ),
                  ),
              ],
            ),
          ),
          barrierColor: Colors.black.withOpacity(0.5),
          isDismissible: true,
          enableDrag: true,
        ).whenComplete(() {
          isBottomSheetOpen = false;
        });
      }
    } // Kích hoạt kéo thả);
  }
}
