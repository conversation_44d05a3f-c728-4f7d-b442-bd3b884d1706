

import 'package:get/get.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/app/modules/dash_board/controllers/dash_board_controller.dart';
import 'package:u2u_dpn/app/modules/maintenance/controllers/maintenance_controller.dart';
import 'package:u2u_dpn/app/modules/my_tier/controllers/my_tier_controller.dart';
import 'package:u2u_dpn/app/modules/notification/controllers/notification_controller.dart';
import 'package:u2u_dpn/app/modules/referrals/controllers/referrals_controller.dart';
import 'package:u2u_dpn/app/modules/rewards/controllers/rewards_controller.dart';
import 'package:u2u_dpn/app/modules/sign_in/controllers/sign_in_controller.dart';
import 'package:u2u_dpn/data/app_controller.dart';

import 'app/modules/home/<USER>/home_controller.dart';

class AppBinding extends Bindings {
  @override
  void dependencies() {
    Get.put(() => ConnectionController(), permanent: true);
    Get.put(() => AppController());
    Get.put(() => DashBoardController());
    Get.put(() => HomeController());
    Get.put(() => ReferralsController());
    Get.put(() => RewardsController());
    Get.put(() => SignInController());
    Get.put(() => MaintenanceController());
    Get.put(() => NotificationController());
    Get.put(() => MyTierController());
  }
}
