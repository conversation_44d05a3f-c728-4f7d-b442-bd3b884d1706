// ignore_for_file: deprecated_member_use, unused_element

import 'dart:async';
import 'dart:io';
import 'dart:isolate';
// import 'dart:io';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:logger/logger.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:u2u_dpn/app/routes/navigate_keys.dart';

import 'package:u2u_dpn/app_state.dart' show AppState, SettingType;
import 'package:u2u_dpn/app_state.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/locales/locale_string.dart';
import 'package:u2u_dpn/model/peer_node_config_model.dart';
import 'package:u2u_dpn/rust/run_peer_node.dart';
import 'package:u2u_dpn/socket_app.dart';
import 'package:u2u_dpn/socket_app_session.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
// import 'package:u2u_dpn/socket_app_session.dart';
import 'package:u2u_dpn/utils/system_utils.dart';
import 'package:u2u_dpn/widget/CustomContainerShadow.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
import 'package:u2u_dpn/widget/ultil.dart';
// import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:window_manager/window_manager.dart';

import 'app/modules/home/<USER>/home_controller.dart';
import 'app/routes/app_pages.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/app/modules/dash_board/controllers/dash_board_controller.dart';
import 'package:u2u_dpn/app/modules/maintenance/controllers/maintenance_controller.dart';
import 'package:u2u_dpn/app/modules/my_tier/controllers/my_tier_controller.dart';
import 'package:u2u_dpn/app/modules/notification/controllers/notification_controller.dart';
import 'package:u2u_dpn/app/modules/referrals/controllers/referrals_controller.dart';
import 'package:u2u_dpn/app/modules/rewards/controllers/rewards_controller.dart';
import 'package:u2u_dpn/app/modules/setting/controllers/setting_controller.dart';
import 'package:u2u_dpn/app/modules/sign_in/controllers/sign_in_controller.dart';
import 'package:u2u_dpn/data/app_config.dart';
import 'package:u2u_dpn/data/app_controller.dart';
import 'package:u2u_dpn/utils/network_service.dart';

import 'locales/locales.g.dart';

final Logger logger = Logger();
Future<void> backgroundInitialization() async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  SettingConfig.deviceInfo = await Utils().deviceId();
}

Future<void> _setupWindowManager() async {
  await windowManager.ensureInitialized();
  WindowOptions windowOptions = const WindowOptions(
    size: Size(480, 812),
    minimumSize: Size(400, 712),
    center: true,
    skipTaskbar: false,
    fullScreen: false,
    titleBarStyle: TitleBarStyle.normal,
  );

  windowManager.waitUntilReadyToShow(windowOptions, () async {
    await windowManager.show();
    await windowManager.focus();
  });
}

final RouteObserver<ModalRoute<void>> routeObserver = RouteObserver<ModalRoute<void>>();

void main({Enviroment? env}) async {
  WidgetsFlutterBinding.ensureInitialized();

  // if (!kIsWeb && (Platform.isWindows || Platform.isMacOS)) {
  //   await _setupWindowManager();
  // }

  //  Get.lazyPut(() => SettingController());

  await GetStorage.init();
  await initialize();
  await AppConfig.instance.setEnvironment(env ?? Enviroment.prod);

  await backgroundInitialization(); // Ensure proper initialization
  Get.put(SignInController());

  // runApp(const MyApp());
  await SentryFlutter.init(
    (options) {
      options.dsn = 'https://<EMAIL>/4509359298838608'; // <- Thay bằng DSN của bạn
      options.enableAutoSessionTracking = false;
      options.enableAppLifecycleBreadcrumbs = false;
      options.attachThreads = false;
      options.enableAutoPerformanceTracing = false;
      options.environment = 'production'; 
    },
    appRunner: () {
      // ❌ Gỡ auto error từ Flutter framework
      FlutterError.onError = (details) {
        FlutterError.dumpErrorToConsole(details); // log local
      };

      // ❌ Gỡ auto error từ platform channel / async
      PlatformDispatcher.instance.onError = (error, stack) {
        debugPrint('⚠️ Platform error: $error');
        return true; // <--- Đánh dấu là đã xử lý để **không bị đẩy lên Sentry**
      };

      runApp(const MyApp());
    },
  );

  // WidgetsBinding.instance.addPostFrameCallback((_) {
  //   Future.delayed(const Duration(milliseconds: 500), () {
  //     if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
  //       WakelockPlus.enable();
  //     }
  //   });
  // });
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  bool isInternet = false;
  bool hideApp = false;
    Isolate? peerIsolate;
  String token = '';
  static FirebaseAnalytics? analytics;
  static FirebaseAnalyticsObserver? observer;
// late ConnectionController connectionController;
  final NetworkService _networkService = NetworkService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _networkService.startMonitoring();
    SystemUtils.setPortraitScreenOrientation();
    initializeControllers();
    token = SettingConfig.token;
    Future.delayed(Duration.zero, () async {
      await Firebase.initializeApp(); // Ensure Firebase is initialized before Analytics
      analytics = FirebaseAnalytics.instance;
      observer = FirebaseAnalyticsObserver(analytics: analytics!);
      setState(() {}); // Rebuild the UI after analytics is initialized

      final config = PeerNodeConfig(
        accessToken: SettingConfig.token,
        refreshToken: SettingConfig.refreshToken,
        adminAddr: AppConfig.instance.adminAddr,
        wsHost: AppConfig.instance.wsHost,
        wsPort: AppConfig.instance.wsPort,
      );
      Isolate.spawn<PeerNodeConfig>(runPeerNode, config);

    //  compute(runPeerNode, config);
      if (SettingConfig.token != '') {
        checkSocket();
      }
      // Xử lý deep link khởi động sau khi widget được dựng xong
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await _processInitialDeepLink();
      });

      // Lắng nghe deep link khi ứng dụng đang chạy
      FirebaseDynamicLinks.instance.onLink.listen((dynamicLinkData) {
        // ignore: unrelated_type_equality_checks
        if (dynamicLinkData.link != '') {
          final Uri deepLink = dynamicLinkData.link;
          Get.log("deepLink: $deepLink");
          handleDeepLink(deepLink);
        }
      }).onError((error) {
        debugPrint("Lỗi xử lý dynamic link: $error");
      });
      //    connectionController = Get.find<ConnectionController>(); // Assign after initialization
    });

    // WidgetsBinding.instance.addPostFrameCallback((_) async {
    //   await _processInitialDeepLink();
    // });

    Future.delayed(Duration.zero, () {
      InternetConnectionChecker().onStatusChange.listen((event) {
        handleInternetStatus(event == InternetConnectionStatus.connected);
      });
    });
  }

  void initializeControllers() {
    // Register ConnectionController first
    Get.lazyPut(() => ConnectionController());
    // Get.put(ConnectionController());

    // Register other controllers
    Get.lazyPut(() => AppController());

    Get.lazyPut(() => DashBoardController());
    // Get.put(DashBoardController());

    Get.lazyPut(() => HomeController());
    Get.lazyPut(() => ReferralsController());

    Get.lazyPut(() => RewardsController());
    // Get.put(RewardsController());

    Get.lazyPut(() => SettingController());
    Get.lazyPut(() => MaintenanceController());
    Get.lazyPut(() => NotificationController());
    Get.lazyPut(() => MyTierController());
  }

  Future<void> _processInitialDeepLink() async {
    final PendingDynamicLinkData? initialLink = await FirebaseDynamicLinks.instance.getInitialLink();
    if (initialLink?.link != null) {
      try {
        handleDeepLink(initialLink!.link);
      } catch (error) {
        logger.d("Error handling initial deep link: $error");
      }
    }
  }

  void handleDeepLink(Uri deepLink) {
    Get.log("deepLink: $deepLink");
    if (deepLink.toString().contains('referral')) {
      Get.find<DashBoardController>().selectIndex.value = 0;
      AppState.instance.settingBox.write(SettingType.referral.toString(), deepLink.toString().split('referral=').last);
      final referralsController = Get.find<ReferralsController>();
      if (SettingConfig.token != '') {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!referralsController.checkNullReferredBy()) {
            referralsController.linkReferralcontroller.value.text = deepLink.toString().split('referral=').last;
            referralsController.linkReferralDeepLink();
          }
        });
      }
    }
  }

  void checkSocket() async {
    try {
      SocketApp.connectSocket();
      //  SocketAppSession.connectSocketSession();
    } catch (error) {
      logger.d("Error connecting socket: $error");
    }
  }

  void handleInternetStatus(bool isConnected) {
    setState(() async {
      if (!isConnected && !isInternet) {
        isInternet = true;

        SocketApp.stopConnection();
        SocketAppSession.logout();
        !SettingConfig.isConnection
            ? Get.snackbar(
                '',
                '',
                titleText: const SizedBox(height: 3),
                messageText: Text(
                  LocaleKeys.no_internet_connection.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white),
                ),
                backgroundColor: Colors.red.withOpacity(0.6),
                snackPosition: SnackPosition.BOTTOM,
              )
            : Get.bottomSheet(
                Container(
                  //  color: AppColor.neutral700,
                  decoration: const BoxDecoration(
                    color: AppColor.neutral700,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Column(
                    children: [
                      Container(
                        width: 85,
                        height: 4,
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(16)),
                          color: Color(0xFF4C556B),
                        ),
                        margin: const EdgeInsets.only(top: 8),
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                            child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min, // để tránh chiếm full height
                                  children: [
                                    ClipRRect(
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(16),
                                          topRight: Radius.circular(16),
                                        ),
                                        child: Container(
                                          color: AppColor.neutral700,
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              const Align(alignment: Alignment.topRight, child: Icon(Icons.close, color: AppColor.neutral400))
                                                  .onTap(() {
                                                Get.back();
                                              }),
                                              Image.asset(
                                                AppImages.no_internet,
                                                width: 80,
                                              ),
                                              CustomText(
                                                text: LocaleKeys.no_internet_connection.tr,
                                                fontSize: 20,
                                              ).paddingOnly(top: 24, bottom: 8),
                                              CustomText(
                                                text: LocaleKeys.your_device_is_offline.tr,
                                                color: AppColor.neutral300,
                                                textAlign: TextAlign.center,
                                                fontSize: 14,
                                              ),
                                              CustomContainerShadow(
                                                  child: Column(
                                                children: [
                                                  Row(
                                                    children: [
                                                      Image.asset(AppImages.shield),
                                                      CustomText(
                                                              text: SettingConfig.autoConnection
                                                                  ? LocaleKeys.reconnect_dpn_is_on.tr
                                                                  : LocaleKeys.reconnect_dpn_is_off.tr)
                                                          .paddingOnly(left: 10),
                                                    ],
                                                  ),
                                                  CustomText(
                                                    text: SettingConfig.autoConnection
                                                        ? LocaleKeys.your_app_will_automatically.tr
                                                        : LocaleKeys.you_will_need_to_manually.tr,
                                                    color: AppColor.neutral300,
                                                  ).paddingTop(16)
                                                ],
                                              )).paddingSymmetric(vertical: 24),
                                            ],
                                          ),
                                        ))
                                  ],
                                ))),
                      ),
                    ],
                  ),
                ),

                barrierColor: Colors.black.withOpacity(0.5), // Màu nền mờ
                isDismissible: true, // Cho phép vuốt để đóng
                enableDrag: true,
              );
      } else if (isConnected && isInternet) {
        isInternet = false;
     
        // call function speedtest here 

        if (SettingConfig.autoConnection) {
          SocketApp.restartConnection();
          //  SocketAppSession.connectSocketSession();
        }
        Get.snackbar(
          '',
          '',
          titleText: const SizedBox(height: 3),
          messageText: const Text(
            "Internet connection restored",
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.green.withOpacity(0.6),
          snackPosition: SnackPosition.BOTTOM,
        );
      }
      await     ConnectionController().startTest();
    });
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.detached) {
      //  SocketAppSession.logout();
      SocketApp.stopConnection();
      // Không phải lúc nào cũng được gọi!
    } else if (state == AppLifecycleState.paused) {
      //  SocketApp.stopConnection();
      hideApp = true;
    } else if (state == AppLifecycleState.resumed && hideApp) {
      hideApp = false;
      if(Platform.isIOS){
        final config = PeerNodeConfig(
      accessToken: SettingConfig.token,
      refreshToken: SettingConfig.refreshToken,
      adminAddr: AppConfig.instance.adminAddr,
      wsHost: AppConfig.instance.wsHost,
      wsPort: AppConfig.instance.wsPort,
    );

    try {
      peerIsolate?.kill(priority: Isolate.immediate); // kill nếu đang tồn tại
      peerIsolate = await Isolate.spawn(runPeerNode, config);
    } catch (e) {
      print('Lỗi khởi tạo lại isolate: $e');
    }
      }
       
      if (SettingConfig.isConnection == true && ConnectionController().statusConnection.value == StateButtonConnect.stop) {
        SocketApp.restartConnection();
      }
      ConnectionController().getTotalTime();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _networkService.stopMonitoring();
    // SocketAppSession.logout();
    SocketApp.stopConnection();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: "Application",
      //  initialBinding: AppBinding(),
      theme: ThemeData(fontFamily: 'Manrope'),
      // initialRoute:SettingConfig.token == '' ? AppPages.INITIAL_SPLASH,
      //home:token == '' ? SignInView() :const DashBoardView(),
      getPages: AppPages.routes,
      debugShowCheckedModeBanner: false,
      // navigatorObservers: observer != null ? <NavigatorObserver>[observer!] : [],
      navigatorObservers: observer != null ? [observer!, routeObserver] : [routeObserver],
      translations: LocaleString(),
      locale: AppState.instance.settingBox.read(SettingType.locale.toString()) == 'Vietnamese'
          ? const Locale('vi', 'VN')
          : AppState.instance.settingBox.read(SettingType.locale.toString()) == 'Japanese'
              ? const Locale('ja', 'JP')
              : const Locale('en', 'US'),
      fallbackLocale: AppState.instance.settingBox.read(SettingType.locale.toString()) == 'Vietnamese'
          ? const Locale('vi', 'VN')
          : AppState.instance.settingBox.read(SettingType.locale.toString()) == 'Japanese'
              ? const Locale('ja', 'JP')
              : const Locale('en', 'US'),
      builder: (context, child) {
        final scale = MediaQuery.of(context).textScaleFactor.clamp(0.8, 1.0);
        return MediaQuery(data: MediaQuery.of(context).copyWith(textScaleFactor: scale), child: child!);
      },
      navigatorKey: NavigateKeys.navigationKey,
    );
  }
}
