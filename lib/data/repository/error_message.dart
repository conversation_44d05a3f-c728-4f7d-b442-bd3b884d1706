import 'package:u2u_dpn/model/base_model.dart';

class ErrorResponse extends BaseResponse {
  ErrorResponse({
    this.errMessage,
    this.rule,
    this.attribute,
  });
  String? errMessage;
  String? rule;
  String? attribute;

  factory ErrorResponse.fromMap(Map<String, dynamic> json) => ErrorResponse(
        errMessage: json["message"] ?? '',
        rule: json["rule"] ?? '',
        attribute: json["attribute"] ?? '',
      );
}
