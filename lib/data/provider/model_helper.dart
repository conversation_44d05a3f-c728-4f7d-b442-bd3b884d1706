
String pareData(data) {
  if (data != null) {
    return data.toString().trim();
  } else {
    return '';
  }
}

int pareDataToInt(data) {
  if (data != null) {
    if(data.toString().trim().contains(".")){
      return int.parse(data.toString().trim().split(".").first);
    }else{
      return int.parse(data.toString().trim());
    }
  } else {
    return 0;
  }
}

double? pareDataToDouble(data) {
  if (data != null) {
    return double.parse(data.toString().trim());
  } else {
    return 0.0;
  }
}

bool? pareDataToBool(data) {
  if (data != null) {
    return data;
  } else {
    return null;
  }
}