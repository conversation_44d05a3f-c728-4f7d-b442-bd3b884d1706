// ignore_for_file: depend_on_referenced_packages

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import 'package:get/get_instance/get_instance.dart';

import 'package:get/get_rx/get_rx.dart';

import 'package:get/get_utils/get_utils.dart';
import 'package:get/route_manager.dart';
import 'package:logging/logging.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/repository/api_repository.dart';
import 'package:u2u_dpn/socket_app.dart';
import 'package:u2u_dpn/socket_app_session.dart';
import 'package:u2u_dpn/widget/dialog/process_dialog.dart';
import 'package:u2u_dpn/widget/ultil.dart';

import '../../app/routes/app_pages.dart';
import '../../locales/locales.g.dart';
import '../../utils/app_images.dart';
import '../../widget/custom_text.dart';

class MyCallBack {
  // final String _apiBaseUrl = ApiRootGlobal.getInstance().env == Env.dev ? "192.168.0.99:2023" : "192.168.0.99:2023";

  final Logger _logger = Logger("MyCallBack");
  final Dio _dio = Dio();
  var isLogout = false.obs;
  ApiRepository apiRepository = ApiRepository();
  static final MyCallBack _instance = MyCallBack._internal();
  bool checkToken = true;
  var connectionController = Get.put(ConnectionController());
  MyCallBack._internal() {
    setupInterceptors();
  }

  factory MyCallBack() {
    return _instance;
  }

  Future<Response?> get({
    required String url,
    final object,
    final Map<String, dynamic>? params,
    Map<String, String>? dataReplaceAll,
    String? tokenProtected,
    String? hashCode,
    String? dataAddMore,
    bool isUseToken = false,
    Map<String, dynamic>? header,
  }) async {
    try {
      Response response = await _dio
          .get(url,
              options: await options(
                  tokenProtected: tokenProtected,
                  hashCode: hashCode,
                  header: header,
                  isUseToken: isUseToken),
              queryParameters: params)
          .timeout(const Duration(seconds: 40));

      return base(response);
    } on DioException catch (e) {
      logger.d('reload${e.response}');

      log("Exception occured: $e");
      if (e.response?.statusCode == 401) {
        await refreshToken();
        Response response = await _dio
            .get(url,
                options: await options(
                    tokenProtected: tokenProtected,
                    hashCode: hashCode,
                    header: header,
                    isUseToken: isUseToken),
                queryParameters: params)
            .timeout(const Duration(seconds: 40));
        return base(response);
      }
      return e.response;
    }
  }

  Future<Response?> post({
    required String url,
    final object,
    final Map<String, dynamic>? data,
    String? tokenProtected,
    String? hashCode,
    bool isUseToken = false,
    Map<String, dynamic>? header,
    Map<String, dynamic>? queryParam,
  }) async {
    try {
      Response response = await _dio
          .post(url,
              options: await options(
                  tokenProtected: tokenProtected,
                  hashCode: hashCode,
                  header: header,
                  isUseToken: isUseToken),
              data: data,
              queryParameters: queryParam)
          .timeout(const Duration(seconds: 40));

      return base(
        response,
      );
    } on DioException catch (e) {
      log("Exception occured: $e");
      if (e.response?.statusCode == 401) {
        await refreshToken();
        Response response = await _dio
            .post(url,
                options: await options(
                    tokenProtected: tokenProtected,
                    hashCode: hashCode,
                    header: header,
                    isUseToken: isUseToken),
                data: data,
                queryParameters: queryParam)
            .timeout(const Duration(seconds: 40));
        return base(response);
      }
      return e.response;
    }
  }

  Future<Response?> put({
    required String url,
    final object,
    final Map<String, dynamic>? params,
    Map<String, dynamic>? queryParam,
    bool isUseToken = false,
  }) async {
    try {
      Response response = await _dio.put(url,
          options: await options(isUseToken: isUseToken),
          data: params,
          queryParameters: queryParam);

      return base(response);
      // ignore: deprecated_member_use
    } on DioError catch (e) {
      if (e.response?.statusCode == 401) {
        await refreshToken();
        Response response = await _dio.put(url,
            options: await options(isUseToken: isUseToken),
            data: params,
            queryParameters: queryParam);
        return base(response);
      } else if (DioExceptionType.receiveTimeout == e.type ||
          DioExceptionType.connectionTimeout == e.type) {
        logger.d("object");
      } else if (DioExceptionType.unknown == e.type) {
        if (e.message!.contains('SocketException')) {
          return e.response;
        }
      } else {
        return e.response;
      }
      _logger.log(Level.INFO, "Exception occured: $e");
    }
    return null;
  }

  // Future<Response?> delete({
  //   required String url,
  //   final object,
  //   final Map<String, dynamic>? queryParam = null,
  //   bool isUseToken = false,
  //   bool isP2P = false,
  // }) async {
  //   try {
  //     logger.d("flutter params" + queryParam.toString());
  //     Response response = await _dio.delete(url, options: await options(isUseToken: isUseToken), queryParameters: queryParam);
  //     if (response.statusCode == 401) {
  //       await refreshToken();
  //       response = await _dio.delete(url, options: await options(isUseToken: isUseToken), queryParameters: queryParam);
  //     }
  //     return base(response);
  //   } on DioError catch (e) {
  //     if (DioErrorType.receiveTimeout == e.type || DioErrorType.connectionTimeout == e.type) {
  //       logger.d("object");
  //     } else if (DioErrorType.unknown == e.type) {
  //       if (e.message!.contains('SocketException')) {
  //         return e.response;
  //       }
  //     } else {
  //       return e.response;
  //     }
  //     _logger.log(Level.INFO, "Exception occured: " + e.toString());
  //   } catch (exc) {
  //     // ErrorResolver.toast(exc.toString());
  //     _logger.log(Level.INFO, "Exception occured: " + exc.toString());
  //   }
  //   return null;
  // }

  // Future<dynamic> sendImage({
  //   required final String url,
  //   final object,
  //   dynamic data,
  //   String? tokenProtected,
  //   String? hashCode,
  //   bool isUseToken = false,
  //   String enctype = '',
  //   Map<String, dynamic>? header,
  //   Map<String, dynamic>? queryParam,
  // }) async {
  //   try {
  //     var response = await _dio
  //         .post(
  //           url,
  //           options: await options(isUseToken: isUseToken, tokenProtected: tokenProtected, hashCode: hashCode, header: header, enctype: enctype),
  //           data: data,
  //         )
  //         .timeout(const Duration(seconds: 40));

  //     return response;
  //   } on DioError catch (e) {
  //     log("Exception occured: " + e.toString());
  //   } catch (exc) {
  //     log("Exception occured: " + exc.toString());
  //   }
  //   return null;
  // }

  Future<Response?> base(final Response response) async {
    try {
      if (response.statusCode == 200 || response.statusCode == 201) {
        Response data = response;
        return data;
      }
    } catch (exc) {
      debugPrint('catch ${exc.toString()}');
      _logger.log(Level.INFO, "Exception occured: $exc");
    }
    return null;
  }

  void setupInterceptors() async {
    _dio.interceptors.add(PrettyDioLogger(
      requestBody: true,
      requestHeader: true,
    ));
  }

  Future<Options> options(
      {bool isNotUsedContentType = true,
      String? hashCode,
      String? tokenProtected,
      bool isUseToken = false,
      bool isNotContentType = false,
      Map<String, dynamic>? header,
      String enctype = ''}) async {
    var token = SettingConfig.token.toString();
    var options = Options(
      headers: {
        'Accept': 'application/json',
      },
    );

    if (header != null) {
      header.forEach((key, value) {
        options.headers![key] = value;
      });
    }
    if (!Utils.isNullOrEmpty(enctype)) {
      options.headers!['enctype'] = enctype;
    }
    if (hashCode != null) {
      options.headers!['hash'] = hashCode;
    }
    if (tokenProtected != null) {
      options.headers!['protected_token'] = tokenProtected;
    }

    if (isNotContentType) {
      if (isNotUsedContentType) {
        options.contentType = 'application/json';
      } else {
        options.contentType = "application/x-www-form-urlencoded";
      }
    }
    try {
      if (token.isNotEmpty && isUseToken) {
        options.headers!['Authorization'] = 'Bearer $token';
      }
    } catch (e) {
      logger.d(e);
    }
    return options;
  }

  Future refreshToken() async {
    try {
      if (SettingConfig.token != '') {
        var res = await apiRepository.refreshToken();

        if (res!.statusCode == 201 || res.statusCode == 200) {
          SettingConfig.token = res.data['access_token'];
          SettingConfig.refreshToken = res.data['refresh_token'];
          SettingConfig.user_id = res.data['user_id'];
          checkToken = true;
          logger.d(SettingConfig.token);

          if (connectionController.statusConnection.value ==
              StateButtonConnect.start) {
            SocketApp.startConnection();

          SocketAppSession.connectSocketSession();
          }
        } else if (checkToken) {
          //   refreshToken();
          checkToken = false;
          ProgressDialog.showDialogNotification(
            isShowCancel: false,
            barrierDismissible: false,
            content: Column(
              children: [
                Image.asset(
                  AppImages.ic_warning,
                  width: 40,
                  height: 40,
                ),
                CustomText(
                  text: LocaleKeys.notification.tr,
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                ).paddingSymmetric(vertical: 10),
                CustomText(
                  text: LocaleKeys.login_expired.tr,
                  textAlign: TextAlign.center,
                ).paddingSymmetric(vertical: 10),
              ],
            ),
            onPressed: () {
              Get.offAllNamed(Routes.SIGN_IN);
              SocketApp.stopConnection();
              SocketApp.logout();
              SocketAppSession.logout();
              logout();
              //  settingController.logout();
            },
          );
        }
      }
    } catch (e) {
      logger.d('refresh token: $e');
      refreshToken();

      // Get.offAllNamed(Routes.SIGN_IN);
    }
  }

  void logout() {
    SettingConfig.referralCode = '';
    SettingConfig.token = '';
    SettingConfig.refreshToken = '';
    SettingConfig.user_id = '0x0000000000000000000000000000000000000000';
    isLogout.value = true;
  }
}
