import 'package:u2u_dpn/data/provider/error_resolver.dart';
import 'package:u2u_dpn/data/provider/model_helper.dart';
import 'package:u2u_dpn/widget/ultil.dart';

class MyResponse<T> {
  bool status;
  String msg;
  int code;
  dynamic data;
  String? errors;
  Map<String, dynamic> pagination;

  int? page;
  int? pages;
  int? total;

  MyResponse(this.status, this.msg, this.code, this.data, this.errors, this.pagination, this.total, this.page, this.pages);

  late List a;

  MyResponse.fromJson(Map<String, dynamic> json, BaseObject? target)
      : msg = json.containsKey("msg") ? (json["msg"] ?? "") : "",
        code = json.containsKey("code") ? (pareDataToInt(json["code"] ?? 0)) : (json.containsKey("status") ? (json["status"] ? 0 : 400) : 0),
        status = json.containsKey("status") ? json["status"] : (json.containsKey("success") ? json["success"] : false),
        data = target == null
            ? ((json["data"] is int || json["data"] is double)
                ? [json["data"]]
                : (json["data"] is Map ? List<T>.from([Map<String, dynamic>.from(json["data"])]) : json["data"]))
            : json.containsKey("data")
                ? ((json["data"] is Map)
                    ? List<T>.from([target.fromJson(Map<String, dynamic>.from(json["data"]))])
                    : List<T>.from(json["data"].map((i) => (target).fromJson(i))))
                : [],
        errors = json["errors"] ?? '',
        pagination = json.containsKey("pagination") ? (json['pagination'] ?? {}) : {},
        pages = json.containsKey("pages") ? pareDataToInt((json["pages"] ?? 0).toString()) : null,
        page = json.containsKey("page") ? pareDataToInt((json["page"] ?? 0).toString()) : null,
        total = json.containsKey("total") ? pareDataToInt((json["total"] ?? 0).toString()) : null;

  bool get isNotSuccess => status == false;

  String get errorMessage =>
      Utils.isNullOrEmpty(ErrorResolver.getCodeError(code)) ? (Utils.isNullOrEmpty(msg) ? 'Error' : msg) : ErrorResolver.getCodeError(code);

  factory MyResponse.fail(String error, int code) {
    return MyResponse(false, error, code, null, null, {}, null, null, null);
  }

  int get getLastPage => pagination.containsKey('lastPage') ? int.parse(pagination['lastPage'].toString()) : 0;

  int get getCurrentPage => pagination.containsKey('currentPage') ? int.parse(pagination['currentPage'].toString()) : 0;

  bool get canLoadMore => getCurrentPage < getLastPage;
}

abstract class BaseObject<T> {
  T fromJson(json);
}
