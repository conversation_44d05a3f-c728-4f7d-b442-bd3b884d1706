// import 'dart:convert';
// import 'dart:io';

import 'package:get/get.dart';

import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/model/user_detail_model.dart';
import 'package:u2u_dpn/repository/api_repository.dart';

class AppController extends GetxController {
  ApiRepository apiRepository = ApiRepository();
  var userDetailModel = UserDetailModel().obs;

  // Future loginByDevice() async {
  //   try {
  //     var res = await apiRepository.loginByDevice();

  //     if (res != null && res.data != null) {
  //       SettingConfig.token = res.data['bearer'];
  //       SettingConfig.user_id = res.data['user_id'];
  //     }
  //     // getUserDetail();
  //   } catch (e) {
  //     logger.d(e);
  //   }
  // }

  Future<UserDetailModel?> getUserDetail() async {
    try {
      var res = await apiRepository.userDetail();
      // if ((res != null && res.statusCode == 401) || SettingConfig.token == '') {
      //   await refreshToken();
      // } else {
      if (res != null && res.data != null) {
        userDetailModel.value = UserDetailModel.fromJson(res.data);
        SettingConfig.referralCode = userDetailModel.value.userReferral?.referralCode ?? '';
      }
      return userDetailModel.value;
      //   }
    } catch (e) {
      logger.d(e);
      return null;
    }
  }

  // Future<UserDetailModel?> getShowTask() async {
  //   try {
  //     if (Platform.isAndroid) {
  //       SettingConfig.showsTask = true;
  //     } else {
  //       var res = await apiRepository.getShowTask();
  //       // if ((res != null && res.statusCode == 401) || SettingConfig.token == '') {
  //       //   await refreshToken();
  //       // } else {
  //       if (res != null && res.data != null) {
  //         SettingConfig.showsTask = jsonDecode(res.data)['showTasks'];
  //       }

  //       //   }
  //     }
  //   } catch (e) {
  //     logger.d(e);
  //     return null;
  //   }
  //   return null;
  // }

  // Future refreshToken() async {
  //   try {
  //     var res = await apiRepository.refreshToken();

  //     if ((res != null && res.statusCode != 201)) {
  //       SettingConfig.token = res.data['access_token'];
  //       SettingConfig.refreshToken = res.data['refresh_token'];
  //       SettingConfig.user_id = res.data['user_id'];
  //     } else {
  //       Get.offAllNamed(Routes.SIGN_IN);
  //     }
  //   } catch (e) {
  //     logger.d(e);
  //   }
  // }
}
