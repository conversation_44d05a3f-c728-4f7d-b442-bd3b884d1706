// ignore_for_file: non_constant_identifier_names

import 'package:nb_utils/nb_utils.dart';

class SettingConfig {
  static String get token => getStringAsync('token', defaultValue: '');

  static set token(String value) => setValue('token', value);
  static String get deviceInfo => getStringAsync('deviceInfo', defaultValue: '');

  static set deviceInfo(String value) => setValue('deviceInfo', value);

  static String get refreshToken => getStringAsync('refreshToken', defaultValue: '');

  static set refreshToken(String value) => setValue('refreshToken', value);

  static String get referralCode => getStringAsync('referralCode', defaultValue: '');

  static set referralCode(String value) => setValue('referralCode', value);

  static String get user_id => getStringAsync('user_id', defaultValue: '0x0000000000000000000000000000000000000000');

  static set user_id(String value) => setValue('user_id', value);
  static bool get showsTask => getBoolAsync('showsTask', defaultValue: false);

  static set showsTask(bool value) => setValue('showsTask', value);

  static String get user_email => getStringAsync('user_email', defaultValue: '...');
  static set user_email(String value) => setValue('user_email', value);
  static bool get autoConnection => getBoolAsync('autoConnection', defaultValue: true);

  static set autoConnection(bool value) => setValue('autoConnection', value);
    static bool get isConnection => getBoolAsync('isConnection', defaultValue: false);

  static set isConnection(bool value) => setValue('isConnection', value);
}
