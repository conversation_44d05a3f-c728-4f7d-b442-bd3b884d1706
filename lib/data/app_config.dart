import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/repository/api_repository.dart';

class AppConfig {
  ApiRepository apiRepository = ApiRepository();
  static final AppConfig instance = AppConfig._internal();

  AppConfig._internal();

  factory AppConfig() => instance;

  Future setEnvironment(Enviroment evm) async {
   await getAppConfig();
    String contents;
    if (evm == Enviroment.dev) {
      contents = await rootBundle.loadString(
        "assets/config/dev.json",
      );
    } else if (evm == Enviroment.stg) {
      contents = await rootBundle.loadString(
        "assets/config/stg.json",
      );
    } else {
      contents = dataAppConfig.value;
    }
    // contents = await rootBundle.loadString(
    //   "assets/config/${evm.name.toString()}.json",
    // );
    final json = jsonDecode(contents);
    _urlU2UScan=evm == Enviroment.dev
        ? 'https://testnet.u2uscan.xyz/tx/'
        : 'https://u2uscan.xyz/tx/';
    _wsConnection = json['wsAdminURL'];
    _adminAddr = json['adminURL'];
    _notificationURL = json['notificationURL'];
    _wsHost = json['wsHost'] ?? '127.0.0.1';
    _wsPort = json['wsPort'] ?? 8099;
    _scanURL = json['scanURL'] ?? 'https://scan.u2dpn.xyz/';
  }
String? _urlU2UScan;
  String? _adminAddr;
  int? _wsPort;
  String? _wsHost;
  String? _wsConnection;
  String? _scanURL;
  String? _notificationURL;
  String get adminAddr => _adminAddr ?? '';
  int get wsPort => _wsPort ?? 0000;
  String get wsHost => _wsHost ?? "";
  String get wsConnection => _wsConnection ?? "";
  String get scanURL => _scanURL ?? "";
  String get notificationURL => _notificationURL ?? "";
  String get urlU2UScan => _urlU2UScan ?? "";
  var dataAppConfig = ''.obs;

  Future getAppConfig() async {
    try {
      var res = await apiRepository.getShowTask();
      // if ((res != null && res.statusCode == 401) || SettingConfig.token == '') {
      //   await refreshToken();
      // } else {
      if (res != null && res.data != null) {
        dataAppConfig.value = res.data;
        if (!kIsWeb) {
          if (Platform.isAndroid) {
            SettingConfig.showsTask = true;
          } else {
            SettingConfig.showsTask = jsonDecode(res.data)['showTasks'];
          }
        } else {
          SettingConfig.showsTask = jsonDecode(res.data)['showTasks'];
        }
      }

      //   }
    } catch (e) {
      return null;
    }
    return null;
  }
}

enum Enviroment { prod, dev, stg }
