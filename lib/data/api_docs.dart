// ignore_for_file: constant_identifier_names

import 'package:u2u_dpn/data/app_config.dart';

class ApiDocs {
  static final ApiDocs _instance = ApiDocs._internal();

  factory ApiDocs() => _instance;

  ApiDocs._internal();

  //Auth
  static const loginByInfo = 'auth/login_by_device_info';
  static const loginBySSO = 'auth/sso_sign_in';
  static const refreshToken = 'auth/refresh_token';

  //User
  static const userDetail = 'users/detail';
  // static const postTokenFCM = 'users/detail';
  static const postTokenFCM = 'users/notification/register';
  static const createCode = 'users/referral/create'; //{ "referral_code": "1234abcfffff"}
  static const linkCode = 'users/referral/link'; //{ "referral_code": "1234abcfffff"}
  static const history = 'users/referral/history';
  static const referralOverview = 'users/referral/overview';
  //reward
  static const claim = 'users/rewards/claim';
  static const claimHistory = 'users/rewards/claim_history';
  static const overview = 'users/rewards/overview';
  static const updateWithdrawAdress = 'users/rewards/update_withdrawal_address';
  static const get_total_pending_withdrawal_txs = 'users/rewards/get_total_pending_withdrawal_txs';

  //Connection
  static const bandwidthPrice = 'connections/bandwidth-price';
  static const connectionOverview = 'connections/overview';
  static const active_connections = 'connections/active_connections';
  static const connection_history = 'connections/connection_history';
  static const suggested_bandwidth_price = 'connections/suggested_bandwidth_price';
  static const detail_connection = 'connections/detail/{session_id}';

  //Loyalpotint
  static const getLoyalPoint = 'users/loyalty_points/get_total_time';
  static const hearbeat = 'users/loyalty_points/heartbeat';
  static const startSessionLoyaltyPoint = 'users/loyalty_points/start_session';
  static const endSessionLoyaltyPoint = 'users/loyalty_points/end_session';

//location
  static const location_bonus = 'locations/bonus-info';
  static const location_bonus_all = 'locations/bonus-info/all';
  //Notification
  static const list_notification = 'users/notifications';
  static const languageNotification = 'users/language';
  static const user_tier_points = 'users/tier_points';
  static const getShowTask =
      'https://raw.githubusercontent.com/unicornultralabs/subnet-dpn-config/main/peer-app-config.json';
  static const getHealth = 'metrics/health';
    static const getCurrentIp = 'https://ifconfig.io/all.json';
  static String convertURL(String path) {
    return '${AppConfig.instance.adminAddr}/$path';
  }

  static String convertNotificationURL(String path) {
    return '${AppConfig.instance.notificationURL}/$path';
  }
  static String u2uscan(String hash) {
    return '${AppConfig.instance.urlU2UScan}$hash';
  }
}
