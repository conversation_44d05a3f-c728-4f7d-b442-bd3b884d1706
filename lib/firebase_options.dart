// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        return linux;
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBJva9iPnTgq89sJpvNKC0MVlvJPOtYKf4',
    appId: '1:212760149569:web:931015aae4a4b6e7ec50c0',
    messagingSenderId: '212760149569',
    projectId: 'u2u-dpn',
    authDomain: 'u2u-dpn.firebaseapp.com',
    storageBucket: 'u2u-dpn.appspot.com',
    measurementId: 'G-9VV0QZ8Z1Q',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDxCqyklGBf5iOM1euI2zT8anzfqBdzmEw',
    appId: '1:212760149569:android:d170c6ef4f328365ec50c0',
    messagingSenderId: '212760149569',
    projectId: 'u2u-dpn',
    storageBucket: 'u2u-dpn.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA3cT3SWeh0tFe7qN9Yg2zL6Qp7kM3YTwg',
    appId: '1:212760149569:ios:b1fcc5bf4d778246ec50c0',
    messagingSenderId: '212760149569',
    projectId: 'u2u-dpn',
    storageBucket: 'u2u-dpn.appspot.com',
    androidClientId: '212760149569-3ishca084f6ncv3o4rs47toh6505qgl0.apps.googleusercontent.com',
    iosClientId: '212760149569-ekbpnoj9mekbvfhn1mnhgt0h790hbuhg.apps.googleusercontent.com',
    iosBundleId: 'com.unicornultra.app.u2dpn',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyA3cT3SWeh0tFe7qN9Yg2zL6Qp7kM3YTwg',
    appId: '1:212760149569:ios:3e4d7472c539a67dec50c0',
    messagingSenderId: '212760149569',
    projectId: 'u2u-dpn',
    storageBucket: 'u2u-dpn.appspot.com',
    androidClientId: '212760149569-3ishca084f6ncv3o4rs47toh6505qgl0.apps.googleusercontent.com',
    iosClientId: '212760149569-vefnuf0dk7ks5t1rqg2n97p1ph9gf3o5.apps.googleusercontent.com',
    iosBundleId: 'com.uniultra.u2udpn.RunnerTests',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBJva9iPnTgq89sJpvNKC0MVlvJPOtYKf4',
    appId: '1:212760149569:web:e64c29ab67e124d2ec50c0',
    messagingSenderId: '212760149569',
    projectId: 'u2u-dpn',
    authDomain: 'u2u-dpn.firebaseapp.com',
    storageBucket: 'u2u-dpn.appspot.com',
    measurementId: 'G-YBP8Y9X8Z0',
  );

  static const FirebaseOptions linux = FirebaseOptions(
    apiKey: 'AIzaSyBJva9iPnTgq89sJpvNKC0MVlvJPOtYKf4',
    appId: '1:212760149569:web:08de205b6853b211ec50c0',
    messagingSenderId: '212760149569',
    projectId: 'u2u-dpn',
    authDomain: 'u2u-dpn.firebaseapp.com',
    storageBucket: 'u2u-dpn.appspot.com',
    measurementId: 'G-CFQWD8DM30',
  );
}
