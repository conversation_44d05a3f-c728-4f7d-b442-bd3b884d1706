import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/claim_history/controllers/claim_history_controller.dart';
import 'package:u2u_dpn/app/routes/navigate_keys.dart';
import 'package:u2u_dpn/data/provider/my_callback.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/model/overview_model.dart';
import 'package:u2u_dpn/model/user_detail_model.dart';
import 'package:u2u_dpn/repository/api_repository.dart';
import 'package:u2u_dpn/widget/dialog/process_dialog.dart';

import '../../../../data/app_controller.dart';
import '../../../../model/user_tier_points_model.dart';
import '../../../../utils/app_utils.dart';

class RewardsController extends GetxController {
  var isLoadButtonClaim = false.obs;
  var controllerEnterWalletAddress = TextEditingController().obs;
  var changeWalletAddress = TextEditingController().obs;
  var isHintButtonEnterWalletAddress = true.obs;
  var isHintButtonChangeWalletAddress = true.obs;
  // double controllerClaimable = 0;
  // String TestText = '**********************************';
  ClaimHistoryController claimController = Get.put(ClaimHistoryController());
  var overviewData = RewardOverviewModel().obs;
  // ignore: non_constant_identifier_names
  var user_xp = 0.obs;
  AppController appController = Get.find();
  ApiRepository apiRepository = ApiRepository();
  var userDetailModel = UserDetailModel().obs;
  var totalHistoryClaim = ''.obs;
  var listUserTierPoints = <UserTierPointModel>[].obs;
  var isLogout = false.obs;
  var loadingListPoint = false.obs;
  Timer? reloadOverviewTime;

  var isCopy = false.obs;
  var isBalanceU2U = true.obs;
  var isExpanded = false.obs;
   var resetBalance = false.obs;

  void changeHintButton() {
    if (controllerEnterWalletAddress.value.text.isEmpty) {
      isHintButtonEnterWalletAddress.value = true;
    } else {
      isHintButtonEnterWalletAddress.value = false;
    }
  }

  Future<void> onRefresh() async {
    // monitor network fetch
    //  await Future.delayed(Duration(milliseconds: 1000));
    // if failed,use refreshFailed()
    await overview();
    //var resUser = await appController.getUserDetail();
    // if (resUser != null) {
    //   userDetailModel.value = resUser;
    // }
    //await getTotalHistoryClaim();
    await claimController.getListHistoryClaim(isRoute: false);
  }

  void changeHintButtonChangeWalletAddress() {
    if (changeWalletAddress.value.text.isEmpty) {
      isHintButtonChangeWalletAddress.value = true;
    } else {
      isHintButtonChangeWalletAddress.value = false;
    }
  }

  // bool checkNullClaimableValue() {
  //   if (overviewData.value.unclaimedRewards == null || overviewData.value.unclaimedRewards == 0.0 || overviewData.value.unclaimedRewards == 0) {
  //     return true;
  //   }
  //   return false;
  // }

  bool checkNullWalletAddress() {
    if (userDetailModel.value.user?.withdrawalAddr == null || userDetailModel.value.user?.withdrawalAddr == '') {
      return true;
    }
    return false;
    // return true;
  }

  bool checkClaimable() {
    if (overviewData.value.getUnclaimedRewards.toDouble() == 0 || checkNullWalletAddress()) {
      return true;
    }
    return false;
  }

  // Future reloadOverview() async {
  //   try {
  //     reloadOverviewTime?.cancel();
  //     reloadOverviewTime = Timer.periodic(const Duration(minutes: 3), (timer) async {
  //       if (!isLogout.value && !MyCallBack().isLogout.value) {
  //         var res = await apiRepository.overview();
  //         if (res != null && res.data != null) {
  //           overviewData.value = RewardOverviewModel.fromJson(res.data);
  //         }
  //       } else {
  //         timer.cancel();
  //       }
  //     });
  //   } on DioException catch (e) {
  //     logger.d(e.response);
  //   }
  // }

  Future overview() async {
    try {
      isLogout.value = false;
      MyCallBack().isLogout.value = false;
      var res = await apiRepository.overview();
      if (res != null && res.data != null) {
        overviewData.value = RewardOverviewModel.fromJson(res.data);
      }

      var resUser = await appController.getUserDetail();
      if (resUser != null) {
        userDetailModel.value = resUser;
      }

      await getTotalHistoryClaim();
    } catch (e) {
      logger.d(e);
    }
  }

  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }

  Future getTotalHistoryClaim() async {
    try {
      var res = await apiRepository.get_total_pending_withdrawal_txs();

      if (res != null && res.data != null) {
        totalHistoryClaim.value = res.data['total_pending_withdrawal_txs'].toString();
      }
    } catch (e) {
      logger.d(e);
    }
  }

  Future updateWithdrawAdress(String withdrawalAddr) async {
    try {
      if (withdrawalAddr == userDetailModel.value.user?.depositAddr) {
        ProgressDialog.showDialogNotification(
            isShowCancel: false,
            content: Column(
              children: [
                Image.asset(
                  AppImages.ic_warning,
                  width: 104,
                ),
                const SizedBox(
                  height: 16,
                ),
                Text(
                  LocaleKeys.wallet_address_is_incorrect.tr,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
              ],
            ));
      } else {
        ProgressDialog.show(NavigateKeys.navigationKey.currentContext!);
        var res = await apiRepository.updateWithdrawAdress(withdrawalAddr);

        if (res != null && res.data != null && (res.statusCode == 201 || res.statusCode == 200)) {
          Get.back();

          if (NavigateKeys.navigationKey.currentContext!.mounted) ProgressDialog.hide(NavigateKeys.navigationKey.currentContext!);
          ProgressDialog.showDialogNotification(
              onPressed: () async {
                Get.back();
                var resUser = await appController.getUserDetail();
                if (resUser != null) {
                  userDetailModel.value = resUser;
                }
              },
              isShowCancel: false,
              content: Column(
                children: [
                  Image.asset(
                    AppImages.claim_rewards_done,
                    width: 104,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Text(
                    LocaleKeys.success.tr,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFFFFFFFF),
                    ),
                  ),
                ],
              ));
        } else {
          Get.back();
          if (NavigateKeys.navigationKey.currentContext!.mounted) ProgressDialog.hide(NavigateKeys.navigationKey.currentContext!);
          ProgressDialog.showDialogNotification(
              isShowCancel: false,
              content: Column(
                children: [
                  const SizedBox(
                    height: 16,
                  ),
                  Text(
                    LocaleKeys.wallet_address_is_incorrect.tr,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFFFFFFFF),
                    ),
                  ),
                ],
              ));
        }
        if (NavigateKeys.navigationKey.currentContext!.mounted) ProgressDialog.hide(NavigateKeys.navigationKey.currentContext!);
      }
      changeWalletAddress.value.text = '';
      controllerEnterWalletAddress.value.text = '';
    } catch (e) {
      Get.back();
      changeWalletAddress.value.text = '';
      controllerEnterWalletAddress.value.text = '';
      if (NavigateKeys.navigationKey.currentContext!.mounted) ProgressDialog.hide(NavigateKeys.navigationKey.currentContext!);
      ProgressDialog.showDialogNotification(
          isShowCancel: false,
          content: Column(
            children: [
              const SizedBox(
                height: 16,
              ),
              Text(
                e.toString(),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFFFFFFFF),
                ),
              ),
            ],
          ));
      logger.d(e);
    }
  }

  Future userTierPoints() async {
    try {
      loadingListPoint.value = true;
      var res = await apiRepository.getUserTierPoints();
      if (res != null && res.data != null && (res.statusCode == 200 || res.statusCode == 201)) {
        listUserTierPoints.value = (res.data as List).map((e) => UserTierPointModel.fromJson(e)).toList();
      }
      loadingListPoint.value = false;
      logger.d('User Tier Points: ${listUserTierPoints.length}');
    } catch (e) {
      logger.d(e);
      loadingListPoint.value = false;
    }
  }
}
