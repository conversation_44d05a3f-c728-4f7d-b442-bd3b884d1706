import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/rewards/controllers/rewards_controller.dart';
import 'package:u2u_dpn/app/modules/rewards/views/install_u2u.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/widget/custom_raisebutton.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

import '../../../../utils/app_utils.dart';

class EnterU2UWallet extends GetView<RewardsController> {
  const EnterU2UWallet({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // ConstrainedBox(
                //     constraints: BoxConstraints(maxWidth: Get.width * 0.8),
                //     child: Text(LocaleKeys.enter_u2u_wallet.tr,
                //         style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: Color(0xFFFFFFFF)))),
                const SizedBox(),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                    controller.controllerEnterWalletAddress.value.text = '';
                  },
                  child: SvgPicture.asset(AppImages.icon_close),
                )
              ],
            ),
            const SizedBox(
              height: 12,
            ),
            Image.asset(AppImages.my_wallet,width: 80, height: 80,).paddingOnly(right: 16),
            ConstrainedBox(
                constraints: BoxConstraints(maxWidth: Get.width * 0.8),
                child: Text(LocaleKeys.connect_u2u_wallet.tr,
                    style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: Color(0xFFFFFFFF)))),
            const SizedBox(
              height: 32,
            ),
            Align(
              alignment: Alignment.centerLeft,
              child: CustomText(
                text: LocaleKeys.wallet_address.tr,
                overflow: TextOverflow.ellipsis,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColor.neutral300,
              ).paddingBottom(12),
            ),
            Stack(
              children: [
                Obx(() => TextFormField(
                      controller: controller.controllerEnterWalletAddress.value,
                      cursorColor: const Color(0xFF1AF7A9),
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Color(0xFFFFFFFF)),
                      maxLines: null,
                      maxLength: 150,
                      onChanged: (value) {
                        controller.changeHintButton();
                      },
                      decoration: InputDecoration(
                        counterText: '',
                        hintText: LocaleKeys.enter_wallet_address.tr,
                        hintStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Color(0xFF383846)),
                        enabledBorder: const OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(8)),
                          borderSide: BorderSide(color: Color(0xFF120E21), width: 0.6),
                        ),
                        focusedBorder: const OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(8)),
                          borderSide: BorderSide(color: Color(0xFF120E21)),
                        ),
                        contentPadding: const EdgeInsets.symmetric(vertical: 25, horizontal: 25.0),
                        filled: true,
                        fillColor: const Color(0xFF120E21),
                        suffixIcon: const SizedBox(
                          height: 24,
                          width: 100,
                        ),
                      ),
                    )),
                Positioned(
                  right: 16,
                  top: 5,
                  child: InkWell(
                    child: IntrinsicHeight(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 16, bottom: 16),
                        child: Container(
                          padding: const EdgeInsets.only(left: 16, right: 16, top: 5, bottom: 5),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            color: const Color(0xFF2D2C3B),
                          ),
                          child: Text(
                            LocaleKeys.paste.tr,
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                    onTap: () async {
                      var data = await Clipboard.getData("text/plain");
                      if (data?.text?.isNotEmpty ?? false) {
                        controller.controllerEnterWalletAddress.value.text = data!.text!.trim();
                        controller.changeHintButton();
                        // ignore: use_build_context_synchronously
                        FocusScope.of(context).unfocus();
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 12,
            ),
            SizedBox(
              width: Get.width - 32,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Icon(
                    Icons.brightness_1,
                    size: 4,
                    color: Color(0xFF65636F),
                  ),
                  const SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    child: Text(LocaleKeys.we_will_send.tr,
                        maxLines: 10,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF65636F),
                        )),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 24,
            ),
            Obx(() => CostomRaisedButtom(
                  isHintButton: controller.isHintButtonEnterWalletAddress.value,
                  name: LocaleKeys.connect_wallet.tr,
                  function: () async {
                    FocusManager.instance.primaryFocus!.unfocus();
                    Get.back();
                    await controller.updateWithdrawAdress(controller.controllerEnterWalletAddress.value.text);
                  },
                )),
            const SizedBox(
              height: 24,
            ),
            CostomRaisedButtom(
              name: LocaleKeys.how_to_install_U2U_wallet.tr,
              iconButton: const Icon(
                Icons.arrow_forward,
                color: Colors.white,
                size: 20,
              ),
              colorText: Colors.white,
              colorsGradient: const [Color(0xFF2D2C3B), Color(0xFF2D2C3B)],
              function: () {
                Get.to(const InstallU2U());
              },
            )
          ],
        ),
      ),
    );
  }
}
