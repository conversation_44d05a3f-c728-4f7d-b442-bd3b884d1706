// ignore_for_file: use_build_context_synchronously, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';

// import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
// import 'package:u2u_dpn/app/modules/claim_history/controllers/claim_history_controller.dart';
import 'package:u2u_dpn/app/modules/claim_history/views/claim_detail_view.dart';
import 'package:u2u_dpn/app/modules/rewards/views/balance_view.dart';
import 'package:u2u_dpn/app/modules/rewards/views/change_u2u_wallet.dart';
import 'package:u2u_dpn/app/modules/rewards/views/confirm_withdraw_bottom_shett.dart';
import 'package:u2u_dpn/app/modules/rewards/views/entre_u2u.dart';
import 'package:u2u_dpn/app/routes/app_pages.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/model/claim_history_model.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/CustomContainerShadow.dart';
import 'package:u2u_dpn/widget/custom_popup_info.dart';
import 'package:u2u_dpn/widget/dialog/process_dialog.dart';
import 'package:u2u_dpn/widget/loading.dart';
import 'package:u2u_dpn/widget/ultil.dart';
import 'package:u2u_dpn/widget/widget.dart';

import '../controllers/rewards_controller.dart';

class RewardsView extends GetView<RewardsController> {
  const RewardsView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFF1B1A28),
        body: Container(
          width: Get.width,
          height: Get.height,
          padding: const EdgeInsets.all(16.0),
          decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AppImages.background), fit: BoxFit.fill)),
          child: RefreshIndicator(
            onRefresh: () async {
              await controller.onRefresh();
            },
            child: ListView(
              children: [
                buildMyWallet(context),
                buildBalance(context),
                buildClaimHistory(),
              ],
            ),
          ),
        ));
  }

  Widget buildMyWallet(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomText(
            text: LocaleKeys.my_wallet.tr,
            overflow: TextOverflow.ellipsis,
            fontSize: 24,
            fontWeight: FontWeight.w500,
            color: AppColor.neutral100,
          ),
          const SizedBox(
            height: 8,
          ),
          buildUnconnected(context)
        ],
      ),
    );
  }

  Widget buildUnconnected(BuildContext context) {
    return Obx(() => Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            color: const Color(0xFF120E21),
          ),
          child: Row(
            children: [
              controller.checkNullWalletAddress()
                  ? SvgPicture.asset(
                      AppImages.icon_unconnected_wallet,
                      width: 20,
                    )
                  : SvgPicture.asset(
                      AppImages.icon_wallet,
                      width: 20,
                    ),
              const SizedBox(
                width: 4,
              ),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: Get.width * 0.3),
                child: Obx(
                  () => Text(
                      controller.checkNullWalletAddress()
                          ? LocaleKeys.unconnected_wallet.tr
                          : (CustomHideTex.hideMiddleText(controller.userDetailModel.value.user?.withdrawalAddr) ?? ''),
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: controller.checkNullWalletAddress() ? AppColor.neutral300 : AppColor.neutral200)),
                ),
              ),
              const SizedBox(
                width: 4,
              ),
              SvgPicture.asset(
                AppImages.icon_arrow_left,
                color: AppColor.neutral300,
                width: 20,
              )
            ],
          ).onTap(() {
            if (controller.checkNullWalletAddress()) {
              CustomBottomSheet.showModalNotFullScreenWithHeight(
                context,
                const EnterU2UWallet(),
                height: MediaQuery.of(context).size.height * 0.7,
              );
            } else {
              // controller.changeWalletAddress.value.text = '';
              // controller.changeHintButtonChangeWalletAddress();
              CustomBottomSheet.showModalNotFullScreenWithHeight(
                context,
                buildMenuWallet(context),
                height: MediaQuery.of(context).size.height * 0.4,
              );
            }
          }),
        ));
  }

  Widget buildMenuWallet(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: Get.width * 0.8),
                child: CustomText(
                  text: LocaleKeys.wallet.tr,
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFFFFFFFF),
                ),
              ),
              InkWell(
                onTap: () {
                  Get.back();
                },
                child: SvgPicture.asset(AppImages.icon_close),
              )
            ],
          ),
          const SizedBox(
            height: 24,
          ),
          walletCell(AppImages.icon_edit, LocaleKeys.change_wallet.tr, () {
            controller.changeWalletAddress.value.text = '';
            controller.changeHintButtonChangeWalletAddress();
            Get.back();
            CustomBottomSheet.showModalNotFullScreenWithHeight(
              context,
              const ChangeU2UWallet(),
              height: MediaQuery.of(context).size.height * 0.5,
            );
          }),
          const SizedBox(
            height: 16,
          ),
          walletCell(AppImages.icon_copy_svg, LocaleKeys.copy_wallet_address.tr, () {
            Clipboard.setData(ClipboardData(text: controller.userDetailModel.value.user?.withdrawalAddr ?? '')).then((value) {
              Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
            });
            Get.back();
          }),
          const SizedBox(
            height: 16,
          ),
          // walletCell(AppImages.icon_pow, LocaleKeys.disconnect_wallet.tr, () {
          //   Get.back();
          //   CustomBottomSheet.showModalNotFullScreenWithHeight(
          //     context,
          //     disconnectWalletBottomSheet(context),
          //     height: MediaQuery.of(context).size.height * 0.5,
          //   );
          // }),
        ],
      ),
    );
  }

  Widget disconnectWalletBottomSheet(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const SizedBox(),
              InkWell(
                onTap: () {
                  Get.back();
                },
                child: SvgPicture.asset(AppImages.icon_close),
              )
            ],
          ),
          const SizedBox(
            height: 24,
          ),
          SvgPicture.asset(
            AppImages.icon_pow,
            width: 80,
            color: AppColor.ErrorColor300,
          ).paddingOnly(bottom: 12),
          CustomText(
            text: LocaleKeys.disconnect_wallet.tr,
            overflow: TextOverflow.ellipsis,
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: AppColor.neutral100,
          ).paddingOnly(bottom: 12),
          CustomText(
            text: LocaleKeys.are_you_sure_you_want_wallet.tr,
            fontSize: 14,
            textAlign: TextAlign.center,
            fontWeight: FontWeight.w500,
            color: AppColor.neutral300,
          ).paddingOnly(bottom: 12),
          const Spacer(),
          CostomRaisedButtom(
              colorsGradient: const [
                AppColor.ErrorColor300,
                AppColor.ErrorColor300,
              ],
              colorText: AppColor.neutral100,
              name: LocaleKeys.disconnected.tr,
              function: () {
                Get.back();
              }).paddingBottom(16)
        ],
      ),
    );
  }

  Widget walletCell(String icon, String title, Function() onTap) {
    return Row(
      children: [
        SvgPicture.asset(icon).paddingRight(12),
        CustomText(
          text: title,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: AppColor.neutral100,
        ),
      ],
    ).onTap(onTap);
  }

  Widget buildBalance(BuildContext context) {
    return CustomContainerShadow(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            text: '${LocaleKeys.total_rewards.tr} U2U',
            overflow: TextOverflow.ellipsis,
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppColor.neutral300,
          ).paddingOnly(bottom: 12),
          Obx(
            () => balanceCell(AppImages.token_u2u, controller.overviewData.value.getTotalRewardsv2, () {
              controller.isBalanceU2U.value = true;
              Get.to(const BalanceView());
            }),
          ),
          const SizedBox(
            height: 24,
          ),
          CustomText(
            text: '${LocaleKeys.total_rewards.tr} U2DPN',
            overflow: TextOverflow.ellipsis,
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppColor.neutral300,
          ).paddingOnly(bottom: 12),
          Obx(
            () => balanceCell(AppImages.token_u2dpn, controller.overviewData.value.getTotalRewards, () {
              controller.isBalanceU2U.value = false;
              Get.to(const BalanceView());
            }),
          ),
          const SizedBox(
            height: 24,
          ),
          Obx(() => CostomRaisedButtom(
                iconButton: controller.checkNullWalletAddress()
                    ? SvgPicture.asset(
                        AppImages.wallet,
                        color: AppColor.neutralBlack,
                      )
                    : SvgPicture.asset(
                        AppImages.icon_claim,
                        color: AppColor.neutralBlack,
                      ),
                iconLeft: false,
                name: controller.checkNullWalletAddress() ? LocaleKeys.connect_u2u_wallet.tr : LocaleKeys.claim_rewards.tr,
                function: () async {
                  if (controller.checkNullWalletAddress()) {
                    CustomBottomSheet.showModalNotFullScreenWithHeight(
                      context,
                      const EnterU2UWallet(),
                      height: MediaQuery.of(context).size.height * 0.7,
                    );
                  } else {
                    ProgressDialog.show(context);
                    await controller.overview();
                    ProgressDialog.hide(context);
                    CustomBottomSheet.showModalNotFullScreenWithHeight(
                      context,
                      const ConfirmWithdrawBottomShett(),
                      height: MediaQuery.of(context).size.height * 0.6,
                    );
                  }
                },
                //isHintButton: !controller.isClaim.value,
              )),
        ],
      ),
    );
  }

  Widget balanceCell(String img, String data, Function() onTap) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Image.asset(img).paddingOnly(right: 12),
              CustomPopupInfo(
                  value: data,
                  child: ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: Get.width * 0.65),
                    child: CustomText(
                      text: data,
                      textAlign: TextAlign.left,
                      overflow: TextOverflow.ellipsis,
                      fontSize: 30,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ))
            ],
          ),
          SvgPicture.asset(
            AppImages.icon_arrow_left,
            color: AppColor.neutral300,
            width: 40,
          )
        ],
      ),
    );
  }

  Widget buildClaimHistory() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText(
              text: LocaleKeys.claim_history.tr.toUpperCase(),
              overflow: TextOverflow.ellipsis,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColor.neutral300,
            ),
            Row(
              children: [
                CustomText(
                  text: LocaleKeys.view_all.tr,
                  overflow: TextOverflow.ellipsis,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColor.primaryGreen500,
                ).paddingRight(8),
                SvgPicture.asset(
                  AppImages.icon_arrow_left,
                  color: AppColor.primaryGreen500,
                  width: 20,
                )
              ],
            ).onTap(() {
              controller.claimController.getListHistoryClaim();
            })
          ],
        ),
        Obx(
          () => controller.claimController.isloading.value
              ? const LoadingScreen(height: 200)
              : controller.claimController.listHistoryClaim.isNotEmpty
                  ? ListView.separated(
                      shrinkWrap: true,
                      padding: const EdgeInsets.only(bottom: 16),
                      itemCount: controller.claimController.listHistoryClaim.length > 5 ? 5 : controller.claimController.listHistoryClaim.length,
                      itemBuilder: (BuildContext context, int index) {
                        return inforCell(
                          context,
                          claimHistoryModel: controller.claimController.listHistoryClaim[index],
                        );
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return const SizedBox(height: 5);
                      },
                    )
                  : CustomNodata(textLine2: ''),
        ),
      ],
    ).paddingTop(24);
  }

  Widget inforCell(BuildContext context, {required ClaimHistoryModel claimHistoryModel}) {
    return Container(
      padding: const EdgeInsets.only(top: 16, left: 16, right: 16),
      margin: const EdgeInsets.only(top: 16),
      decoration: BoxDecoration(
          color: AppColor.neutral700,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [BoxShadow(color: AppColor.neutral700, blurRadius: 2)]),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomText(
                text: LocaleKeys.claim_reward.tr,
                fontSize: 16,
              ),
              GradientText(
                claimHistoryModel.txStatus?.toLowerCase().tr ?? '',
                style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                gradientType: GradientType.linear,
                gradientDirection: GradientDirection.rtl,
                colors: ((claimHistoryModel.txStatus == 'Success')
                    ? [AppColor.primaryGreen500, AppColor.primaryBlue500]
                    : ((claimHistoryModel.txStatus == 'Failed')
                        ? [
                            AppColor.ErrorColor300,
                            AppColor.ErrorColor300,
                          ]
                        : [AppColor.highLightColor300, AppColor.highLightColor300])),
              )
              // CustomText(
              //   text:
              //       '${Utils.convertTimestamptoDate(claimHistoryModel.createdAt ?? 0)} - ${Utils.convertTimestamptoTime(claimHistoryModel.createdAt ?? 0)}',
              //   fontSize: 14,
              //   color: AppColor.neutral400,
              //   fontWeight: FontWeight.w400,
              // )
            ],
          ).paddingBottom(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Image.asset(
                    claimHistoryModel.txType == 'Withdrawal' ? AppImages.token_u2dpn : AppImages.token_u2u,
                    width: 16,
                  ).paddingRight(8),
                  CustomText(text: claimHistoryModel.getAmount).paddingRight(4),
                ],
              ),
              // GradientText(
              //   claimHistoryModel.txStatus ?? '',
              //   style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
              //   gradientType: GradientType.linear,
              //   gradientDirection: GradientDirection.rtl,
              //   colors: ((claimHistoryModel.txStatus == 'Success')
              //       ? [AppColor.primaryGreen500, AppColor.primaryBlue500]
              //       : ((claimHistoryModel.txStatus == 'Failed')
              //           ? [
              //               AppColor.ErrorColor300,
              //               AppColor.ErrorColor300,
              //             ]
              //           : [AppColor.highLightColor300, AppColor.highLightColor300])),
              // )
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Row(
              //   children: [
              //     Image.asset(
              //       AppImages.token_u2dpn,
              //       width: 16,
              //     ).paddingRight(8),
              //     CustomText(text: claimHistoryModel.getAmount)
              //   ],
              // ),
              CustomText(
                text:
                    '${Utils.convertTimestamptoDate(claimHistoryModel.createdAt ?? 0)} - ${Utils.convertTimestamptoTime(claimHistoryModel.createdAt ?? 0)}',
                fontSize: 14,
                color: AppColor.neutral400,
                fontWeight: FontWeight.w400,
              ),
              const Icon(
                Icons.arrow_forward,
                color: AppColor.neutral400,
              )
            ],
          ).paddingSymmetric(vertical: 8),
        ],
      ),
    ).onTap(() {
      controller.claimController.selectClaimHistory = claimHistoryModel;
      Get.to(const ClaimDetailView());
    });
  }

  Widget inforCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        color: const Color(0xFF120E21),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            margin: const EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: const LinearGradient(
                  colors: [
                    Color(0xFF2D2C3B),
                    Color(0xFF1D1C2B),
                  ],
                  begin: FractionalOffset(1.0, 1.0),
                  end: FractionalOffset(1.0, 0.0),
                  stops: [0.0, 1.0],
                  tileMode: TileMode.clamp),
            ),
            child: Obx(() => InkWell(
                  onTap: () async {
                    Get.toNamed(Routes.MY_TIER);
                    await controller.userTierPoints();
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset(
                            AppImages.icon_diamond,
                            color: controller.userDetailModel.value.userTier?.getColor,
                          ).paddingOnly(right: 4),
                          ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: Get.width * 0.15),
                            child: GradientText(
                              (controller.userDetailModel.value.userTier?.tier ?? '').toLowerCase().tr,
                              maxLines: 1,
                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                              overflow: TextOverflow.fade,
                              gradientType: GradientType.linear,
                              gradientDirection: GradientDirection.rtl,
                              colors: controller.userDetailModel.value.userTier?.getlistColor ?? [AppColor.brownColor, AppColor.brownColor],
                            ),
                          ),
                        ],
                      ),
                      const Icon(
                        Icons.arrow_forward_ios_rounded,
                        color: Color(0xFFA7A4A4),
                        size: 12,
                      ).paddingOnly(left: 2),
                    ],
                  ),
                )),
          ),
          inforWallet(context),
          //detail rewards
          Obx(() => Column(
                children: [
                  // SettingConfig.showsTask
                  //     ? detailRewards(title: LocaleKeys.task_rewards.tr, price: controller.overviewData.value.getTotalTaskRewards, paddingBottom: 16 )
                  //     : SizedBox(),
                  detailRewards(
                      title: LocaleKeys.network_share_rewards.tr, price: controller.overviewData.value.getTotalNetworkRewards, paddingBottom: 16),
                  detailRewards(title: LocaleKeys.referrals_rewards.tr, price: controller.overviewData.value.getCommissionRewards, paddingBottom: 16),
                  detailRewards(
                      title: LocaleKeys.claimable.tr,
                      price: controller.overviewData.value.getUnclaimedRewards,
                      paddingBottom: 16,
                      isGreenColor: true,
                      isClaimNeu4: (controller.overviewData.value.unclaimedRewards == null || controller.overviewData.value.unclaimedRewards == 0)
                          ? true
                          : false),
                ],
              )).paddingOnly(left: 16, right: 16, top: 16),
          // Padding(
          //   padding: EdgeInsets.only(left: 20, right: 20, bottom: 16 ),
          //   child: Row(
          //     children: [
          //       const Spacer(),
          //       InkWell(
          //         onTap: () {
          //           controller.claimController.getListHistoryClaim();
          //         },
          //         child: Container(
          //           padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          //           decoration: BoxDecoration(
          //             borderRadius: BorderRadius.circular(24),
          //             color: const Color(0xFF2D2C3B),
          //           ),
          //           child: Row(
          //             children: [
          //               const Text('Claim History ', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Color(0xFFDEDEDE))),
          //               Obx(
          //                 () => Text('(${controller.totalHistoryClaim.value})',
          //                     style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: AppColor ighLightColor300)),
          //               ),
          //             ],
          //           ),
          //         ),
          //       )
          //     ],
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget detailRewards({required String title, required String price, double? paddingBottom, bool isGreenColor = false, bool isClaimNeu4 = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: Get.width * 0.5),
              child: Text(title,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isGreenColor ? AppColor.primaryGreen600 : AppColor.neutral400,
                  )),
            ),
            const Icon(
              Icons.info,
              color: Color(0xFF383846),
              size: 16,
            ).paddingOnly(left: 4),
          ],
        ),
        Expanded(
            child: Row(
          children: [
            Expanded(
                child: Text(
              price,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.right,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isClaimNeu4 ? AppColor.neutral400 : AppColor.neutral200,
              ),
            )),
            Image.asset(
              AppImages.logo,
              width: 20,
            ).paddingOnly(left: 6),
          ],
        )),
      ],
    ).paddingOnly(bottom: paddingBottom ?? 0);
  }

  Widget inforWallet(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: const LinearGradient(
            colors: [
              Color(0xFF2D2C3B),
              Color(0xFF1D1C2B),
            ],
            begin: FractionalOffset(1.0, 1.0),
            end: FractionalOffset(1.0, 0.0),
            stops: [0.0, 1.0],
            tileMode: TileMode.clamp),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Obx(() => Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      // const Icon(
                      //   Icons.remove,
                      //   color: Color(0xFF383846),
                      //   size: 14,
                      // ),
                      controller.checkNullWalletAddress()
                          ? SvgPicture.asset(
                              AppImages.icon_unconnected_wallet,
                              width: 20,
                            )
                          : SvgPicture.asset(
                              AppImages.icon_wallet,
                              width: 20,
                            ),
                      const SizedBox(
                        width: 4,
                      ),
                      ConstrainedBox(
                        constraints: BoxConstraints(maxWidth: Get.width * 0.3),
                        child: Text(
                            controller.checkNullWalletAddress()
                                ? LocaleKeys.unconnected_wallet.tr
                                : (CustomHideTex.hideMiddleText(controller.userDetailModel.value.user?.withdrawalAddr) ?? ''),
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: controller.checkNullWalletAddress() ? AppColor.neutral300 : AppColor.neutral200)),
                      ),
                    ],
                  ),
                  controller.checkNullWalletAddress()
                      ? CostomRaisedButtom(
                          //   widthButtom: 80,
                          name: LocaleKeys.connect.tr,
                          function: () {
                            CustomBottomSheet.showModalNotFullScreenWithHeight(
                              context,
                              const EnterU2UWallet(),
                              height: MediaQuery.of(context).size.height * 0.9,
                            );
                          }).withWidth(120).withHeight(40)
                      : Row(
                          children: [
                            InkWell(
                              onTap: () {
                                Clipboard.setData(ClipboardData(text: controller.userDetailModel.value.user?.withdrawalAddr ?? '')).then((value) {
                                  Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4),
                                  color: AppColor.neutral500,
                                ),
                                child: const Icon(
                                  Icons.copy_sharp,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                            const SizedBox(
                              width: 12,
                            ),
                            InkWell(
                              onTap: () {
                                controller.changeWalletAddress.value.text = '';
                                controller.changeHintButtonChangeWalletAddress();
                                CustomBottomSheet.showModalNotFullScreenWithHeight(
                                  context,
                                  const ChangeU2UWallet(),
                                  height: MediaQuery.of(context).size.height * 0.9,
                                );
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4),
                                  color: AppColor.neutral500,
                                ),
                                child: const Icon(
                                  Icons.drive_file_rename_outline,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ],
                        )
                ],
              )),
          Row(
            children: List.generate(
                150 ~/ 3,
                (index) => Expanded(
                      child: Container(
                        color: index % 2 == 0 ? Colors.transparent : AppColor.neutral500,
                        height: 0.5,
                      ),
                    )),
          ).paddingSymmetric(vertical: 12),
          CustomText(
            text: LocaleKeys.total_rewards.tr,
            fontWeight: FontWeight.w500,
            color: AppColor.neutral400,
          ),
          Obx(() => CustomPopupInfo(
              value: controller.overviewData.value.getTotalRewards,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: Get.width * 0.65),
                    child: Text(
                      controller.overviewData.value.getTotalRewards,
                      textAlign: TextAlign.left,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: 30,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Image.asset(
                    AppImages.logo,
                    width: 40,
                  ),
                ],
              ))),
          const SizedBox(
            height: 20,
          ),
          Center(
            child: Container(
              width: 48,
              height: 6,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: const Color(0xFF171423),
              ),
            ),
          )
        ],
      ),
    );
  }

  itemRow(
      {required String text,
      String value = '',
      Color? colorText,
      Color? colorValue,
      bool showIconInfo = true,
      bool showLogo = false,
      Widget? widgetValue}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              CustomText(text: text, fontWeight: FontWeight.w500, color: colorText ?? AppColor.neutral400),
              if (showIconInfo) ...[
                const SizedBox(
                  width: 4,
                ),
                const Icon(
                  Icons.info,
                  color: Color(0xFF383846),
                  size: 16,
                ),
              ]
            ],
          ),
          widgetValue ??
              Row(
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: Get.width * 0.35),
                    child: CustomText(
                      text: value,
                      textAlign: TextAlign.right,
                      fontWeight: FontWeight.w500,
                      color: colorValue ?? AppColor.neutral200,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (showLogo) ...[
                    const SizedBox(
                      width: 4,
                    ),
                    Image.asset(
                      AppImages.logo,
                      width: 18,
                    ),
                  ]
                ],
              ),
        ],
      ),
    ).paddingOnly(bottom: 16);
  }

  // dialogRewardClaim(BuildContext context) {
  //   ProgressDialog.showDialogNotification(
  //       saveTitle: LocaleKeys.claim_now.tr,
  //       isRow: true,
  //       loadbutton: controller.isLoadButtonClaim,
  //       onPressed: () async {
  //         controller.isLoadButtonClaim.value = true;
  //         try {
  //           var res = await ClaimHistoryController().claim(context);
  //           controller.isLoadButtonClaim.value = false;
  //           if (res != null) {
  //             controller.resetBalance.value = true;
  //             Get.back();
  //             await dialogTransactionsIsCompleted();

  //             controller.onRefresh;
  //             await Future.delayed(const Duration(seconds: 60), () {
  //               controller.resetBalance.value = false;
  //               controller.onRefresh;
  //             });
  //           }
  //           // ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Claim Error')));
  //         } catch (e) {
  //           controller.isLoadButtonClaim.value = false;
  //         }
  //         // Future.delayed(Duration(seconds: 2), () {
  //         //   Get.back();
  //         //   dialogTransactionsIsCompleted();
  //         //   controller.isLoadButtonClaim.value = false;
  //         // });
  //       },
  //       isSwitch: true,
  //       paddingHoz: 0,
  //       margin: 20,
  //       content: Obx(() => Column(
  //             children: [
  //               Image.asset(
  //                 AppImages.claim_rewards,
  //                 height: 104,
  //                 width: 104,
  //               ),
  //               CustomText(
  //                 text: LocaleKeys.claim_rewards.tr,
  //                 fontSize: 20,
  //                 fontWeight: FontWeight.w500,
  //               ).paddingSymmetric(vertical: 24),
  //               itemRow(
  //                 showIconInfo: false,
  //                 text: LocaleKeys.network.tr,
  //                 widgetValue: Container(
  //                   padding: const EdgeInsets.all(8),
  //                   decoration: BoxDecoration(
  //                     borderRadius: BorderRadius.circular(10),
  //                     color: AppColor.neutral600,
  //                   ),
  //                   child:
  //                       CustomTextLineGradient(text: LocaleKeys.u2U_chain.tr),
  //                 ),
  //               ),
  //               itemRow(
  //                   text: LocaleKeys.fee.tr,
  //                   value: Utils.convertSzaboToU2U(0),
  //                   showIconInfo: false,
  //                   showLogo: true),
  //               itemRow(
  //                   text: LocaleKeys.claimable.tr,
  //                   value: controller.overviewData.value.getUnclaimedRewards,
  //                   showIconInfo: false,
  //                   showLogo: true,
  //                   colorText: AppColor.primaryGreen600),
  //               itemRow(
  //                   text: LocaleKeys.destination_address.tr,
  //                   value: CustomHideTex.hideMiddleText(controller
  //                           .userDetailModel.value.user?.withdrawalAddr) ??
  //                       '',
  //                   showIconInfo: false)
  //             ],
  //           )));
  // }

  // dialogTransactionsIsCompleted() {
  //   ProgressDialog.showDialogNotification(
  //       saveTitle: LocaleKeys.done.tr,
  //       canTitle: LocaleKeys.view_claim_history.tr,
  //       isRow: false,
  //       loadbutton: controller.isLoadButtonClaim,
  //       onPressed: () {
  //         Get.back();
  //         controller.onRefresh();
  //       },
  //       onPressedCancell: () {
  //         controller.claimController.getListHistoryClaim();
  //         Get.back();
  //         Get.toNamed(Routes.CLAIM_HISTORY);
  //       },
  //       paddingHoz: 0,
  //       margin: 20,
  //       content: Column(
  //         crossAxisAlignment: CrossAxisAlignment.center,
  //         children: [
  //           Image.asset(
  //             AppImages.claim_rewards_done,
  //             height: 104,
  //             width: 104,
  //           ),
  //           CustomText(
  //             text: LocaleKeys.claim_processing.tr,
  //             fontSize: 20,
  //             fontWeight: FontWeight.w500,
  //             textAlign: TextAlign.center,
  //           ).paddingSymmetric(vertical: 24),
  //           Row(
  //             mainAxisAlignment: MainAxisAlignment.center,
  //             mainAxisSize: MainAxisSize.min,
  //             children: [
  //               ConstrainedBox(
  //                 constraints: BoxConstraints(maxWidth: Get.width * 0.6),
  //                 child: CustomText(
  //                   text: controller.overviewData.value.getUnclaimedRewards,
  //                   textAlign: TextAlign.center,
  //                   fontSize: 48,
  //                   overflow: TextOverflow.ellipsis,
  //                 ),
  //               ),
  //               Image.asset(
  //                 AppImages.logo,
  //                 width: 30,
  //               ).paddingOnly(left: 12),
  //             ],
  //           ),
  //           CustomText(
  //             textAlign: TextAlign.center,
  //             maxLine: 4,
  //             text:
  //                 '${LocaleKeys.the_claim_processing.tr} ${DateFormat('dd/MM/yyyy - HH:mm').format(DateTime.now())}',
  //             color: AppColor.neutral300,
  //             fontWeight: FontWeight.w500,
  //           ).paddingSymmetric(vertical: 20, horizontal: 10)
  //         ],
  //       ).paddingSymmetric(horizontal: 16));
  // }
}
