import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/rewards/controllers/rewards_controller.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/CustomContainerShadow.dart';
import 'package:u2u_dpn/widget/custom_popup_info.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

class BalanceView extends GetView<RewardsController> {
  const BalanceView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Colors.transparent,
      //appBar: CustomAppbarWithLeading(context, controller.isBalanceU2U.value ? '\$U2U' : '\$U2DPN'),
      body: Container(
        width: Get.width,
        height: Get.height,
        padding: const EdgeInsets.all(16.0),
        decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AppImages.background), fit: BoxFit.fill)),
        child: Column(
          children: [
            Row(
              children: [
                const Icon(
                  Icons.arrow_back_rounded,
                  color: AppColor.neutral100,
                ).paddingRight(16),
                CustomText(
                  text: controller.isBalanceU2U.value ? '\$U2U' : '\$U2DPN',
                  overflow: TextOverflow.ellipsis,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColor.neutral100,
                ),
              ],
            ).paddingSymmetric(vertical: 24).paddingTop(16).onTap(() => Get.back()),
            CustomContainerShadow(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(
                    text: controller.isBalanceU2U.value ? ' ${LocaleKeys.total_rewards.tr} U2U' : '${LocaleKeys.total_rewards.tr} U2DPN',
                    overflow: TextOverflow.ellipsis,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColor.neutral300,
                  ).paddingOnly(bottom: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Image.asset(controller.isBalanceU2U.value ? AppImages.token_u2u : AppImages.token_u2dpn).paddingOnly(right: 12),
                          CustomPopupInfo(
                              value: controller.isBalanceU2U.value
                                  ? controller.overviewData.value.getTotalRewardsv2
                                  : controller.overviewData.value.getTotalRewards,
                              child: ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: Get.width * 0.65),
                                child: Text(
                                  controller.isBalanceU2U.value
                                      ? controller.overviewData.value.getTotalRewardsv2
                                      : controller.overviewData.value.getTotalRewards,
                                  textAlign: TextAlign.left,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                    fontSize: 30,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white,
                                  ),
                                ),
                              ))
                        ],
                      ),
                    ],
                  ),
                  // const CustomText(
                  //   text: '≈ \$261.402  (1 U2U ≈ \$0.0006)',
                  //   overflow: TextOverflow.ellipsis,
                  //   fontSize: 14,
                  //   fontWeight: FontWeight.w500,
                  //   color: AppColor.neutral300,
                  // ).paddingTop(8),
                  const Divider(
                    color: AppColor.neutral700,
                    thickness: 1, // độ dày
                    height: 20, // khoảng cách trên dưới
                  ).paddingTop(16),
                  Obx(() => Column(
                        children: [
                          // SettingConfig.showsTask
                          //     ? detailRewards(title: LocaleKeys.task_rewards.tr, price: controller.overviewData.value.getTotalTaskRewards, paddingBottom: 16 )
                          //     : SizedBox(),

                          detailRewards(
                              isu2u: controller.isBalanceU2U.value,
                              title: LocaleKeys.network_share_rewards.tr,
                              price: controller.isBalanceU2U.value
                                  ? controller.overviewData.value.getTotalNetworkRewardsv2
                                  : controller.overviewData.value.getTotalNetworkRewards,
                              paddingBottom: 16),

                          detailRewards(
                              isu2u: controller.isBalanceU2U.value,
                              title: LocaleKeys.referrals_rewards.tr,
                              price: controller.isBalanceU2U.value
                                  ? controller.overviewData.value.getTotalReferralRewardsv2
                                  : controller.overviewData.value.getTotalReferralRewards,
                              paddingBottom: 16),
                          detailRewards(
                              title: LocaleKeys.claimable.tr,
                              price: controller.isBalanceU2U.value
                                  ? controller.overviewData.value.getUnclaimedRewardsv2
                                  : controller.overviewData.value.getUnclaimedRewards,
                              //paddingBottom: 16,
                              isGreenColor: true,
                              isu2u: controller.isBalanceU2U.value,
                              isClaimNeu4:
                                  (controller.overviewData.value.unclaimedRewards == null || controller.overviewData.value.unclaimedRewards == 0)
                                      ? true
                                      : false),
                        ],
                      )).paddingOnly(top: 16),
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText(
                  text: LocaleKeys.introduction.tr,
                  overflow: TextOverflow.ellipsis,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColor.neutral100,
                ),
                Obx(
                  () => SvgPicture.asset(
                    controller.isExpanded.value ? AppImages.arrow_up : AppImages.arrow_down,
                    width: 24,
                  ),
                )
              ],
            ).paddingTop(24).onTap(() {
              controller.toggleExpanded();
            }),
            Obx(
              () => Expanded(
                child: SingleChildScrollView(
                  child: CustomText(
                    text: controller.isExpanded.value
                        ? LocaleKeys.u2_DPN_is_a_decentralized.tr
                        : (LocaleKeys.u2_DPN_is_a_decentralized.tr.length > 100
                            ? '${LocaleKeys.u2_DPN_is_a_decentralized.tr.substring(0, 100)}...'
                            : LocaleKeys.u2_DPN_is_a_decentralized.tr),
                    //overflow: TextOverflow.ellipsis,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColor.neutral300,
                  ).paddingTop(24),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget detailRewards(
      {required String title,
      required String price,
      double? paddingBottom,
      bool isGreenColor = false,
      bool isClaimNeu4 = false,
      bool isu2u = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: Get.width * 0.5),
              child: Text(title,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isGreenColor ? AppColor.primaryGreen600 : AppColor.neutral400,
                  )),
            ),
          ],
        ),
        Expanded(
            child: Row(
          children: [
            Expanded(
                child: Text(
              price,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.right,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isClaimNeu4 ? AppColor.neutral400 : AppColor.neutral200,
              ),
            )),
            Image.asset(
              isu2u ? AppImages.token_u2u : AppImages.token_u2dpn,
              width: 20,
            ).paddingOnly(left: 6),
          ],
        )),
      ],
    ).paddingOnly(bottom: paddingBottom ?? 0);
  }
}
