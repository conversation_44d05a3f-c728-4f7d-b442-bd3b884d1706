import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:u2u_dpn/app/modules/rewards/controllers/rewards_controller.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/custom_appbar.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
import 'package:url_launcher/url_launcher.dart';

class InstallU2U extends GetView<RewardsController> {
  const InstallU2U({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1B1A28),
      appBar: CustomAppBarTitle(context),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: LocaleKeys.step_by_step_to_install.tr,
                fontSize: 32,
              ).paddingOnly(bottom: 32),
              RichText(
                text: TextSpan(
                    text: LocaleKeys.step_1.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColor.primaryGreen700,
                      fontWeight: FontWeight.w400,
                    ),
                    children: [
                      TextSpan(
                          text: LocaleKeys.install_U2U_super_app.tr,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ))
                    ]),
              ).paddingOnly(bottom: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () => launchUrl(
                      Uri.parse("https://apps.apple.com/vn/app/u2u-super-app/id6471394866"),
                      mode: LaunchMode.externalApplication,
                    ),
                    child: Image.asset(
                      AppImages.download_ios,
                      width: Get.width * 0.44,
                      fit: BoxFit.contain,
                    ),
                  ),
                  InkWell(
                    onTap: () => launchUrl(
                      Uri.parse("https://play.google.com/store/apps/details?id=com.u2umobilewallet&pcampaignid=web_share"),
                      mode: LaunchMode.externalApplication,
                    ),
                    child: Image.asset(
                      AppImages.download_chplay,
                      width: Get.width * 0.44,
                      fit: BoxFit.contain,
                    ),
                  ),
                ],
              ),
              RichText(
                text: TextSpan(
                    text: LocaleKeys.step_2.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColor.primaryGreen700,
                      fontWeight: FontWeight.w400,
                    ),
                    children: [
                      TextSpan(
                          text: LocaleKeys.create_new_wallet.tr,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ))
                    ]),
              ).paddingOnly(bottom: 16, top: 32),
              Center(
                child: Image.asset(
                  AppImages.step2,
                  height: 386,
                  fit: BoxFit.contain,
                ),
              ),
              RichText(
                text: TextSpan(
                    text: LocaleKeys.step_3.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColor.primaryGreen700,
                      fontWeight: FontWeight.w400,
                    ),
                    children: [
                      TextSpan(
                          text: LocaleKeys.after_creating_your_first_wallet.tr,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ))
                    ]),
              ).paddingOnly(bottom: 16, top: 32),
              Center(
                child: Image.asset(
                  AppImages.step3,
                  height: 386,
                  fit: BoxFit.contain,
                ),
              ),
              RichText(
                text: TextSpan(
                    text: LocaleKeys.step_4.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColor.primaryGreen700,
                      fontWeight: FontWeight.w400,
                    ),
                    children: [
                      TextSpan(
                          text: LocaleKeys.open_U2DPN_provider_app.tr,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ))
                    ]),
              ).paddingOnly(bottom: 16, top: 32),
              Center(
                child: Image.asset(
                  AppImages.step4,
                  height: 386,
                  fit: BoxFit.contain,
                ),
              ),
              RichText(
                text: TextSpan(
                    text: LocaleKeys.step_5.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColor.primaryGreen700,
                      fontWeight: FontWeight.w400,
                    ),
                    children: [
                      TextSpan(
                          text: LocaleKeys.paste_your_wallet_address.tr,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ))
                    ]),
              ).paddingOnly(bottom: 16, top: 32),
              Center(
                child: Image.asset(
                  AppImages.step5,
                  height: 386,
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(
                height: 40,
              )
            ],
          ),
        ),
      ),
    );
  }
}
