import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/claim_history/controllers/claim_history_controller.dart';
import 'package:u2u_dpn/app/modules/rewards/controllers/rewards_controller.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/CustomContainerShadow.dart';
import 'package:u2u_dpn/widget/custom_bottom_sheet.dart';
import 'package:u2u_dpn/widget/custom_hide_text.dart';
import 'package:u2u_dpn/widget/custom_raisebutton.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

class ConfirmWithdrawBottomShett extends GetView<RewardsController> {
  const ConfirmWithdrawBottomShett({super.key});

  @override
  Widget build(BuildContext context) {

    return SingleChildScrollView(
      child: Container(
        height: MediaQuery.of(context).size.height * 0.55 > 450 ? MediaQuery.of(context).size.height * 0.55 : 450,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const SizedBox(),
                InkWell(
                  onTap: () {
                    Get.back();
                  },
                  child: SvgPicture.asset(AppImages.icon_close),
                )
              ],
            ),
            const SizedBox(
              height: 24,
            ),
            CustomText(
              text: LocaleKeys.confirm_withdraw.tr,
              overflow: TextOverflow.ellipsis,
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: AppColor.neutral100,
            ).paddingOnly(bottom: 12),
            CustomText(
              text: LocaleKeys.confirm_withdraw_description.tr,
              fontSize: 14,
              textAlign: TextAlign.center,
              fontWeight: FontWeight.w500,
              color: AppColor.neutral300,
            ).paddingOnly(bottom: 12),
            IntrinsicWidth(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                      color: AppColor.neutral500, // Set border color
                      width: 1.0),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      AppImages.icon_wallet,
                      width: 20,
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: Get.width * 0.3),
                      child: Obx(
                        () => Text(
                            controller.checkNullWalletAddress()
                                ? LocaleKeys.unconnected_wallet.tr
                                : (CustomHideTex.hideMiddleText(controller.userDetailModel.value.user?.withdrawalAddr) ?? ''),
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: controller.checkNullWalletAddress() ? AppColor.neutral300 : AppColor.neutral200)),
                      ),
                    ),
                  ],
                ),
              ).paddingBottom(24),
            ),
            CustomContainerShadow(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppImages.token_u2dpn,
                        width: 48,
                      ),
                      Obx(() =>  ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: Get.width * 0.65),
                            child: CustomText(
                              text:controller.resetBalance.value?'0': controller.overviewData.value.getUnclaimedRewards,
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: AppColor.neutral100,
                            ),
                          )).paddingBottom(8),
                      const CustomText(
                        text: '\$U2DPN',
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColor.neutral300,
                      )
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppImages.token_u2u,
                        width: 48,
                      ),
                      Obx(() =>  ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: Get.width * 0.65),
                            child: CustomText(
                              text:controller.resetBalance.value?'0': controller.overviewData.value.getUnclaimedRewardsv2,
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: AppColor.neutral100,
                            ),
                          )).paddingBottom(8),
                      const CustomText(
                        text: '\$U2U',
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColor.neutral300,
                      )
                    ],
                  ),
                ],
              ),
            ).paddingBottom(16),
            const Spacer(),
            Obx(() => CostomRaisedButtom(
                //   widthButtom: 80,
                isHintButton: controller.resetBalance.value||
                    controller.overviewData.value.getUnclaimedRewardsv2 == '0.0' && controller.overviewData.value.getUnclaimedRewards == '0.0',
                name: LocaleKeys.confirm.tr,
                function: () async {
                  Get.back();
                  ClaimHistoryController().claim(context);
                  //controller.isClaim.value = false;
                               controller.resetBalance.value = true;
                  controller.onRefresh;
                  CustomBottomSheet.showModalNotFullScreenWithHeight(
                    context,
                    withdrawProcessing(context),
                    height: MediaQuery.of(context).size.height * 0.45,
                  );
                      await Future.delayed(const Duration(seconds: 60), () {
                controller.resetBalance.value = false;
                controller.onRefresh;
              });
                  // await Future.delayed(const Duration(seconds: 60));
                  // controller.isClaim.value = true;
                })).paddingBottom(16)
          ],
        ),
      ),
    );
  }

  Widget withdrawProcessing(BuildContext context) {
    return SingleChildScrollView(
        child: Container(
      // height: MediaQuery.of(context).size.height * 0.55 > 450 ? MediaQuery.of(context).size.height * 0.55 : 450,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const SizedBox(),
              InkWell(
                onTap: () {
                  Get.back();
                },
                child: SvgPicture.asset(AppImages.icon_close),
              )
            ],
          ),
          const SizedBox(
            height: 24,
          ),
          Image.asset(
            AppImages.claim_rewards_done,
          ),
          const SizedBox(
            height: 24,
          ),
          CustomText(
            text: LocaleKeys.withdraw_processing.tr,
            overflow: TextOverflow.ellipsis,
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: AppColor.neutral100,
          ).paddingOnly(bottom: 12),
          CustomText(
            text: LocaleKeys.withdraw_note.tr,
            fontSize: 14,
            textAlign: TextAlign.center,
            fontWeight: FontWeight.w500,
            color: AppColor.neutral300,
          ).paddingOnly(bottom: 12),
          const SizedBox(
            height: 24,
          ),
          CostomRaisedButtom(
              colorsGradient: const [
                AppColor.neutral600,
                AppColor.neutral600,
              ],
              colorText: AppColor.neutral100,
              name: LocaleKeys.view_claim_history.tr,
              function: () {
                Get.back();
                controller.claimController.getListHistoryClaim();
              }).paddingBottom(16)
        ],
      ),
    ));
  }
}
