// ignore_for_file: use_build_context_synchronously

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/model/claim_history_model.dart';
import 'package:u2u_dpn/repository/api_repository.dart';
import '../../../../widget/ultil.dart';
import '../../../routes/app_pages.dart';

class ClaimHistoryController extends GetxController {
  // List<String> listStatus = ['Pending', 'Success', 'Failed', 'Pending', 'Success', 'Failed', 'Success', 'Failed'];
  var listHistoryClaim = [].obs;
  var isloading = false.obs;
  ClaimHistoryModel selectClaimHistory = ClaimHistoryModel();
  ApiRepository apiRepository = ApiRepository();

  Future getListHistoryClaim({bool isRoute = true}) async {
    try {
      isloading = true.obs;
      var res = await apiRepository.claimHistory();
      if (res != null && res.data != null) {
        // logger.d(res.data.map((e) => ClaimHistoryModel.fromJson(e)).toList());
        listHistoryClaim.value = (res.data as List).map((e) => ClaimHistoryModel.fromJson(e)).toList();
        logger.d(listHistoryClaim.length);
        if (isRoute) Get.toNamed(Routes.CLAIM_HISTORY);
      }
      isloading = false.obs;
    } catch (e) {
      isloading = false.obs;
      logger.d(e);
    }
  }

  Future claim(BuildContext context) async {
    try {
      var res = await apiRepository.claim();
      if (res != null && (res.statusCode == 200 || res.statusCode == 201)) {
        return true;
        // logger.d(res);
        // return res.data;
        // if (res != null) {
        //   // claim success
        // }
      } else {
        Utils.showSnackbar(context, res?.data['err_msg'] ?? 'null', isError: true);
        return false;
      }
    } catch (e) {
      logger.d(e);
      Utils.showSnackbar(context, 'Error: $e', isError: true);
      return false;
    }
  }
}
