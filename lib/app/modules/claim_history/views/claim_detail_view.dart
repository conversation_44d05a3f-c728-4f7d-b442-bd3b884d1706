import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:u2u_dpn/app/modules/claim_history/controllers/claim_history_controller.dart';
import 'package:u2u_dpn/data/api_docs.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/cell_text.dart';
import 'package:u2u_dpn/widget/custom_popup_info.dart';
import 'package:u2u_dpn/widget/custom_raisebutton.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
import 'package:u2u_dpn/widget/ultil.dart';
import 'package:url_launcher/url_launcher.dart';

class ClaimDetailView extends GetView<ClaimHistoryController> {
  const ClaimDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1B1A28),
      body: Container(
        width: Get.width,
        height: Get.height,
        padding: const EdgeInsets.all(16.0),
        decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AppImages.background), fit: BoxFit.fill)),
        child: Column(
          children: [
            Row(
              children: [
                const Icon(
                  Icons.arrow_back_rounded,
                  color: AppColor.neutral100,
                ).paddingRight(16),
                CustomText(
                  text: LocaleKeys.claim_detail.tr,
                  overflow: TextOverflow.ellipsis,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColor.neutral100,
                ),
              ],
            ).paddingSymmetric(vertical: 24).paddingTop(16).onTap(() => Get.back()),
            const SizedBox(
              height: 16,
            ),
            cellText(
              LocaleKeys.transaction_hash.tr,
              Row(
                children: [
                  CustomText(
                    text: Utils.shortenUsername(controller.selectClaimHistory.txHash ?? '...', 4, showlastString: true),
                    textAlign: TextAlign.center,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColor.neutral100,
                  ).onTap(() async {
                    Clipboard.setData(ClipboardData(text: controller.selectClaimHistory.txHash ?? '')).then((value) {
                      // ignore: use_build_context_synchronously
                      Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
                    });
                  }),
                  const Icon(
                    Icons.copy,
                    color: AppColor.neutral300,
                    size: 15,
                  ).onTap(() async {
                    Clipboard.setData(ClipboardData(text: controller.selectClaimHistory.txHash ?? '')).then((value) {
                      // ignore: use_build_context_synchronously
                      Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
                    });
                  }).paddingLeft(5)
                ],
              ),
            ).paddingOnly(bottom: 8),

            cellText(
              'Onchain ${LocaleKeys.transaction_hash.tr}',
              Row(
                children: [
                  CustomText(
                    text: Utils.shortenUsername(controller.selectClaimHistory.chainTxHash ?? '...', 4, showlastString: true),
                    textAlign: TextAlign.center,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColor.neutral100,
                  ).onTap(() async {
                  launchUrl(
                      Uri.parse(ApiDocs.u2uscan( controller.selectClaimHistory.chainTxHash ?? '')),
                      mode: LaunchMode.externalApplication,
                    );
                  }),
                  const Icon(
                    Icons.copy,
                    color: AppColor.neutral300,
                    size: 15,
                  ).onTap(() async {
                    Clipboard.setData(ClipboardData(text: controller.selectClaimHistory.chainTxHash ?? '')).then((value) {
                      // ignore: use_build_context_synchronously
                      Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
                    });
                  }).paddingLeft(5)
                ],
              ),
            ).paddingOnly(bottom: 8),

            cellText(
              LocaleKeys.from.tr,
              Row(
                children: [
                  CustomText(
                    text: Utils.shortenUsername(controller.selectClaimHistory.fromAddr ?? '...', 4, showlastString: true),
                    textAlign: TextAlign.center,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColor.neutral100,
                  ).onTap(() async {
                    Clipboard.setData(ClipboardData(text: controller.selectClaimHistory.fromAddr ?? '')).then((value) {
                      // ignore: use_build_context_synchronously
                      Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
                    });
                  }),
                  const Icon(
                    Icons.copy,
                    color: AppColor.neutral300,
                    size: 15,
                  ).onTap(() async {
                    Clipboard.setData(ClipboardData(text: controller.selectClaimHistory.fromAddr ?? '')).then((value) {
                      // ignore: use_build_context_synchronously
                      Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
                    });
                  }).paddingLeft(5)
                ],
              ),
            ).paddingOnly(bottom: 8),
            cellText(
              LocaleKeys.to.tr,
              Row(
                children: [
                  Text(
                    Utils.shortenUsername(controller.selectClaimHistory.toAddr ?? '...', 4, showlastString: true),
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColor.neutral100,
                    ),
                  ).onTap(() async {
                    Clipboard.setData(ClipboardData(text: controller.selectClaimHistory.toAddr ?? '')).then((value) {
                      // ignore: use_build_context_synchronously
                      Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
                    });
                  }),
                  const Icon(
                    Icons.copy,
                    color: AppColor.neutral300,
                    size: 15,
                  ).onTap(() async {
                    Clipboard.setData(ClipboardData(text: controller.selectClaimHistory.toAddr ?? '')).then((value) {
                      // ignore: use_build_context_synchronously
                      Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
                    });
                  }).paddingLeft(5)
                ],
              ),
            ).paddingOnly(bottom: 8),
            cellText(
              LocaleKeys.network.tr,
              const CustomText(
                text: 'U2U',
                textAlign: TextAlign.center,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColor.neutral100,
              ),
            ).paddingOnly(bottom: 8),
            controller.selectClaimHistory.txType == 'Withdrawal'
                ? cellText(
                    '\$U2DPN ${LocaleKeys.amount.tr}',
                    balanceCell(AppImages.token_u2dpn, controller.selectClaimHistory.getAmount),
                  ).paddingOnly(bottom: 8)
                : cellText(
                    '\$U2U ${LocaleKeys.amount.tr}',
                    balanceCell(AppImages.token_u2u, controller.selectClaimHistory.getAmount),
                  ).paddingOnly(bottom: 8),
            // cellText(
            //   LocaleKeys.network_fee.tr,
            //   balanceCell(AppImages.token_u2u, '0.0'),
            // ).paddingOnly(bottom: 8),
            cellText(
                LocaleKeys.date.tr,
                CustomText(
                  text:
                      '${Utils.convertTimestamptoDate(controller.selectClaimHistory.createdAt ?? 0)} - ${Utils.convertTimestamptoTime(controller.selectClaimHistory.createdAt ?? 0)}',
                  fontSize: 16,
                  color: AppColor.neutral100,
                  fontWeight: FontWeight.w500,
                )).paddingOnly(bottom: 8),
            cellText(
              LocaleKeys.status.tr,
              GradientText(
                controller.selectClaimHistory.txStatus?.toLowerCase().tr ?? '',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                gradientType: GradientType.linear,
                gradientDirection: GradientDirection.rtl,
                colors: ((controller.selectClaimHistory.txStatus == 'Success')
                    ? [AppColor.primaryGreen500, AppColor.primaryBlue500]
                    : ((controller.selectClaimHistory.txStatus == 'Failed')
                        ? [
                            AppColor.ErrorColor300,
                            AppColor.ErrorColor300,
                          ]
                        : [AppColor.highLightColor300, AppColor.highLightColor300])),
              ),
            ).paddingOnly(bottom: 8),
            const Spacer(),
            CostomRaisedButtom(
                name: LocaleKeys.back.tr,
                colorsGradient: const [
                  AppColor.neutral600,
                  AppColor.neutral600,
                ],
                colorText: AppColor.neutral100,
                function: () {
                  Get.back();
                }).paddingBottom(16)
          ],
        ),
      ),
    );
  }

  Widget balanceCell(String img, String data) {
    return Row(
      children: [
        CustomPopupInfo(
            value: data,
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: Get.width * 0.65),
              child: CustomText(
                text: data,
                textAlign: TextAlign.left,
                overflow: TextOverflow.ellipsis,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            )).paddingOnly(right: 12),
        Image.asset(
          img,
          width: 15,
        ),
      ],
    );
  }
}
