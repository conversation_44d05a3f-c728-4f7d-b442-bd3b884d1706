import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:u2u_dpn/app/modules/claim_history/views/claim_detail_view.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/loading.dart';
import 'package:u2u_dpn/widget/ultil.dart';
import 'package:u2u_dpn/widget/widget.dart';

import '../../../../model/claim_history_model.dart';
import '../controllers/claim_history_controller.dart';

class ClaimHistoryView extends GetView<ClaimHistoryController> {
  ClaimHistoryView({Key? key}) : super(key: key);
  final ScrollController _scrollController = ScrollController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1B1A28),
      // appBar: CustomAppBarTitle(context),
      // appBar: AppBar(
      //   // title: const Text(''),
      //   backgroundColor: const Color(0xFF1B1A28),
      //   // centerTitle: true,
      //   elevation: 0,
      // ),
      body: Container(
        width: Get.width,
        height: Get.height,
        //  padding: const EdgeInsets.all(16.0),
        decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AppImages.background), fit: BoxFit.fill)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // IconButton(
            //     onPressed: () {
            //       Get.back();
            //     },
            //     icon: Icon(
            //       Icons.arrow_back_outlined,
            //       color: AppColor.neutral300,
            //     )).paddingOnly(top: 35.h),
            Row(
              children: [
                const Icon(
                  Icons.arrow_back_rounded,
                  color: AppColor.neutral100,
                ).paddingRight(16),
                CustomText(
                  text: LocaleKeys.claim_history.tr,
                  overflow: TextOverflow.ellipsis,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColor.neutral100,
                ),
              ],
            ).paddingAll(16).paddingTop(42).onTap(() => Get.back()),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 16.0, right: 16, bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Text(
                    //   LocaleKeys.claim_history.tr,
                    //   textAlign: TextAlign.center,
                    //   style: const TextStyle(
                    //     fontSize: 32,
                    //     overflow: TextOverflow.ellipsis,
                    //     fontWeight: FontWeight.w500,
                    //     color: Color(0xFFFFFFFF),
                    //   ),
                    // ),
                    // const SizedBox(
                    //   height: 16,
                    // ),
                    Obx(() => controller.isloading.value
                        ? LoadingScreen(height: Get.height - 300)
                        : controller.listHistoryClaim.isNotEmpty
                            ? Expanded(
                                child: ListView.builder(
                                    itemCount: controller.listHistoryClaim.length,
                                    controller: _scrollController,
                                    shrinkWrap: true,
                                    padding: const EdgeInsets.all(0),
                                    physics: const AlwaysScrollableScrollPhysics(),
                                    itemBuilder: (BuildContext context, int index) {
                                      logger.d('listHistoryClaim.value: ${controller.listHistoryClaim.length}');
                                      return inforCell(
                                        context,
                                        claimHistoryModel: controller.listHistoryClaim[index],
                                      );
                                    }),
                              )
                            : Column(
                                children: [
                                  CustomNodata(textLine2: '').paddingTop(50),
                                  // CostomRaisedButtom(
                                  //     name: LocaleKeys.claim_now.tr,
                                  //     function: () {
                                  //       Get.back();
                                  //     }).paddingAll(20)
                                ],
                              )),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget inforCell(BuildContext context, {required ClaimHistoryModel claimHistoryModel}) {
    return Container(
      padding: const EdgeInsets.only(top: 16, left: 16, right: 16),
      margin: const EdgeInsets.only(top: 16),
      decoration: BoxDecoration(
          color: AppColor.neutral700,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [BoxShadow(color: AppColor.neutral700, blurRadius: 2)]),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomText(
                text: LocaleKeys.claim_reward.tr,
                fontSize: 16,
              ),
              GradientText(
                claimHistoryModel.txStatus?.toLowerCase().tr ?? '',
                style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                gradientType: GradientType.linear,
                gradientDirection: GradientDirection.rtl,
                colors: ((claimHistoryModel.txStatus == 'Success')
                    ? [AppColor.primaryGreen500, AppColor.primaryBlue500]
                    : ((claimHistoryModel.txStatus == 'Failed')
                        ? [
                            AppColor.ErrorColor300,
                            AppColor.ErrorColor300,
                          ]
                        : [AppColor.highLightColor300, AppColor.highLightColor300])),
              )
              // CustomText(
              //   text:
              //       '${Utils.convertTimestamptoDate(claimHistoryModel.createdAt ?? 0)} - ${Utils.convertTimestamptoTime(claimHistoryModel.createdAt ?? 0)}',
              //   fontSize: 14,
              //   color: AppColor.neutral400,
              //   fontWeight: FontWeight.w400,
              // )
            ],
          ).paddingBottom(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Image.asset(
                    claimHistoryModel.txType == 'Withdrawal' ? AppImages.token_u2dpn : AppImages.token_u2u,
                    width: 16,
                  ).paddingRight(8),
                  CustomText(text: claimHistoryModel.getAmount).paddingRight(4),
                ],
              ),
              // GradientText(
              //   claimHistoryModel.txStatus ?? '',
              //   style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
              //   gradientType: GradientType.linear,
              //   gradientDirection: GradientDirection.rtl,
              //   colors: ((claimHistoryModel.txStatus == 'Success')
              //       ? [AppColor.primaryGreen500, AppColor.primaryBlue500]
              //       : ((claimHistoryModel.txStatus == 'Failed')
              //           ? [
              //               AppColor.ErrorColor300,
              //               AppColor.ErrorColor300,
              //             ]
              //           : [AppColor.highLightColor300, AppColor.highLightColor300])),
              // )
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Row(
              //   children: [
              //     Image.asset(
              //       AppImages.token_u2dpn,
              //       width: 16,
              //     ).paddingRight(8),
              //     CustomText(text: claimHistoryModel.getAmount)
              //   ],
              // ),
              CustomText(
                text:
                    '${Utils.convertTimestamptoDate(claimHistoryModel.createdAt ?? 0)} - ${Utils.convertTimestamptoTime(claimHistoryModel.createdAt ?? 0)}',
                fontSize: 14,
                color: AppColor.neutral400,
                fontWeight: FontWeight.w400,
              ),
              const Icon(
                Icons.arrow_forward,
                color: AppColor.neutral400,
              )
            ],
          ).paddingSymmetric(vertical: 8),
        ],
      ),
    ).onTap(() {
      controller.selectClaimHistory = claimHistoryModel;
      Get.to(const ClaimDetailView());
    });
  }

  // Widget inforCell(BuildContext context, {required ClaimHistoryModel claimHistoryModel}) {
  //   return SizedBox(
  //     width: Get.width,
  //     child: Row(
  //       children: [
  //         const CircleAvatar(
  //           backgroundColor: Color(0xff242332),
  //           child: Icon(
  //             Icons.arrow_upward,
  //             color: Color(0xffA7A4A4),
  //           ),
  //         ),
  //         const SizedBox(
  //           width: 16,
  //         ),
  //         SizedBox(
  //           width: Get.width * 0.75,
  //           child: Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               const SizedBox(
  //                 height: 24,
  //               ),
  //               OntapOpenLink(
  //                 value: claimHistoryModel.toAddr ?? '',
  //                 isAddress: true,
  //                 child: Text(
  //                   '${Utils.convertSzaboToU2U(claimHistoryModel.amount ?? 0)} U2DPN to ${CustomHideTex.hideMiddleText(claimHistoryModel.toAddr) ?? ''}',
  //                   textAlign: TextAlign.center,
  //                   style: const TextStyle(
  //                     fontSize: 16,
  //                     fontWeight: FontWeight.w500,
  //                     color: Color(0xFFFFFFFF),
  //                   ),
  //                   maxLines: 1,
  //                   overflow: TextOverflow.ellipsis,
  //                 ),
  //               ),
  //               const SizedBox(
  //                 height: 8,
  //               ),
  //               Row(
  //                 children: [
  //                   OntapOpenLink(
  //                       value: claimHistoryModel.txHash ?? "",
  //                       child: Text(
  //                         'Tx Hash: ${CustomHideTex.hideMiddleText(claimHistoryModel.txHash, toIndexText: 5) ?? '- -'}',
  //                         textAlign: TextAlign.center,
  //                         style: const TextStyle(
  //                           fontSize: 16,
  //                           fontWeight: FontWeight.w500,
  //                           color: Color(0xFF65636F),
  //                         ),
  //                       )).paddingRight(6),
  //                   IconCopy(value: claimHistoryModel.txHash ?? '')
  //                 ],
  //               ),
  //               Text(
  //                 '${Utils.convertTimestamptoDate(claimHistoryModel.createdAt ?? 0)} - ${Utils.convertTimestamptoTime(claimHistoryModel.createdAt ?? 0)}',
  //                 textAlign: TextAlign.center,
  //                 style: const TextStyle(
  //                   fontSize: 16,
  //                   fontWeight: FontWeight.w500,
  //                   color: Color(0xFF65636F),
  //                 ),
  //               ),
  //               const SizedBox(
  //                 height: 8,
  //               ),
  //               Container(
  //                 padding: const EdgeInsets.all(6),
  //                 decoration: BoxDecoration(
  //                   borderRadius: BorderRadius.circular(6),
  //                   color: const Color(0xFF2D2C3B),
  //                 ),
  //                 child: GradientText(
  //                   claimHistoryModel.txStatus ?? '',
  //                   style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
  //                   gradientType: GradientType.linear,
  //                   gradientDirection: GradientDirection.rtl,
  //                   colors: ((claimHistoryModel.txStatus == 'Success')
  //                       ? [AppColor.primaryGreen500, AppColor.primaryBlue500]
  //                       : ((claimHistoryModel.txStatus == 'Failed')
  //                           ? [
  //                               AppColor.ErrorColor300,
  //                               AppColor.ErrorColor300,
  //                             ]
  //                           : [AppColor.highLightColor300, AppColor.highLightColor300])),
  //                 ),
  //               )
  //             ],
  //           ),
  //         )
  //       ],
  //     ),
  //   );
  // }
}
