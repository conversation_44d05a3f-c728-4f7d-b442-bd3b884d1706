// ignore_for_file: use_build_context_synchronously, library_prefixes

import 'package:desktop_webview_auth/desktop_webview_auth.dart';
import 'package:desktop_webview_auth/google.dart';
import 'package:firebase_messaging/firebase_messaging.dart' as firebaseMessaging;
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart' as GoogleWithMobile;
import 'package:permission_handler/permission_handler.dart';
import 'package:the_apple_sign_in/the_apple_sign_in.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:u2u_dpn/app/routes/app_pages.dart';
import 'package:u2u_dpn/app/routes/navigate_keys.dart';

import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/model/device_android_model.dart';
import 'package:u2u_dpn/repository/api_repository.dart';

import 'package:u2u_dpn/widget/dialog/process_dialog.dart';
import 'package:u2u_dpn/widget/ultil.dart';

class SignInController extends GetxController {
  //GoogleSignIn googleSignIn = GoogleSignIn();

  // final GoogleSignIn googleSignIn = GoogleSignIn(
  //   params: const GoogleSignInParams(
  //     clientId: '1061383137649-apdr7f9kqhjup6pghlsqku7s0kde2jq8.apps.googleusercontent.com',
  //     clientSecret: '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',
  //   ),
  // );

  final googleSignInArgs = GoogleSignInArgs(
    clientId: '1061383137649-5516jq8ct44dn0fs06mgrs7h5tip2lt1.apps.googleusercontent.com',
    redirectUri: 'https://react-native-firebase-testing.firebaseapp.com/__/auth/handler',
    scope: 'email',
  );

  static GoogleWithMobile.GoogleSignIn googleSignInwithMobile = GoogleWithMobile.GoogleSignIn();

  ApiRepository apiRepository = ApiRepository();
  final count = 0.obs;
  var isChecked = false.obs;
  var isLoading = false.obs;

  @override
  void onInit() {
    checkNotification();
    super.onInit();
  }

  Future<void> checkNotification() async {
    PermissionStatus status = await Permission.notification.status;
    if (!status.isGranted) {
      Permission.notification.request();
    }
  }

  void increment() => count.value++;

  String email = '<EMAIL>'; //res?.email ?? ''

  // Future loginWithGoogle(BuildContext context) async {
  //   try {
  //     String accessToken = '';
  //     var res = await googleSignIn.signIn();
  //     accessToken = res?.accessToken ?? '';

  //     if (accessToken != '') {
  //       loginSSO(context, {'Google': GoogleSSOModel(id: '', displayName: '', email: '', photoUrl: '', token: accessToken).toJson()});
  //     } else {
  //       Utils.showSnackbar(context, LocaleKeys.login_fail.tr, isError: true);
  //       isLoading.value = false;
  //     }
  //     isLoading.value = false;
  //     // Get.offAllNamed(Routes.DASH_BOARD);
  //   } catch (error) {
  //     logger.d(error);
  //     isLoading.value = false;
  //     Utils.showSnackbar(context, "error: $error", isError: true);
  //   }
  // }

  Future loginWithGoogle(BuildContext context) async {
    try {
      String accessToken = '';
      final res = await DesktopWebviewAuth.signIn(googleSignInArgs);
      //var res = await googleSignIn.signIn();
      accessToken = res?.accessToken ?? '';

      if (accessToken != '') {
        loginSSO(context, {'Google': GoogleSSOModel(id: '', displayName: '', email: '', photoUrl: '', token: accessToken).toJson()});
      } else {
        Utils.showSnackbar(context, LocaleKeys.login_fail.tr, isError: true);
        isLoading.value = false;
      }
      isLoading.value = false;
      // Get.offAllNamed(Routes.DASH_BOARD);
    } catch (error) {
      logger.d(error);
      isLoading.value = false;
      Utils.showSnackbar(context, "error: $error", isError: true);
    }
  }

  Future loginWithGoogleMobile(BuildContext context) async {
    try {
      String accessToken = '';
      var res = await googleSignInwithMobile.signIn();

      await res?.authentication.then((value) {
        accessToken = value.accessToken ?? '';
        logger.d('accessToken: ${value.accessToken}');
      });

      // logger.d('userName: ${res!.displayName!}');
      // logger.d('photoUrl: ${res.photoUrl!}');
      // logger.d('email: ${res.email}');
      // logger.d('id: ${res.id}');
      if (accessToken != '') {
        SettingConfig.user_email = res?.email ?? (res?.displayName ?? '...');
        loginSSO(context, {
          'Google': GoogleSSOModel(
                  id: res?.id ?? '', displayName: res?.displayName ?? '', email: res?.email ?? '', photoUrl: res?.photoUrl ?? '', token: accessToken)
              .toJson()
        });
      }
      isLoading.value = false;
      // Get.offAllNamed(Routes.DASH_BOARD);
    } catch (error) {
      logger.d(error);
      isLoading.value = false;
      //Utils.showSnackbar(context, "error: $error", isError: true);
      // ProgressDialog.showDialogNotification(
      //     content: RichText(
      //   text: TextSpan(
      //     text: "Error msg: Login failed: platform error. ",
      //     style: const TextStyle(fontSize: 14, color: AppColor.neutral100),
      //     children: [
      //       TextSpan(
      //         text: "Please fire a bug report",
      //         style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14, color: Colors.blue),
      //         recognizer: TapGestureRecognizer()
      //           ..onTap = () {
      //             launchUrl(
      //               Uri.parse("https://forms.gle/LXUHGxQURqmxbxGR9"),
      //               mode: LaunchMode.externalApplication,
      //             );
      //           },
      //       ),
      //     ],
      //   ),
      // ));
    }
  }

  /// Signs out the user from Google.
  ///
  /// This method will handle the sign-out process for a user who is currently
  /// signed in with their Google account. It performs any necessary cleanup
  /// and ensures that the user is properly signed out.
  ///
  /// Returns a [Future] that completes when the sign-out process is finished.
  static Future<void> signOutGoogle() async {
    await googleSignInwithMobile.signOut();
  }

  Future loginWhitApple(BuildContext context) async {
    AuthorizationResult authorizationResult = await TheAppleSignIn.performRequests([
      const AppleIdRequest(requestedScopes: [Scope.email, Scope.fullName])
    ]);

    switch (authorizationResult.status) {
      case AuthorizationStatus.authorized:
        try {
          AppleIdCredential? appleCredentials = authorizationResult.credential;
          final appleIdCredential = authorizationResult.credential;
          OAuthProvider oAuthProvider = OAuthProvider("apple.com");
          OAuthCredential oAuthCredential = oAuthProvider.credential(
              idToken: String.fromCharCodes(appleCredentials!.identityToken!),
              accessToken: String.fromCharCodes(appleCredentials.authorizationCode!));

          // logger.d('email: ${appleCredentials.email}');
          // logger.d('fullName: ${appleCredentials.fullName}');
          // logger.d('code:  ${String.fromCharCodes(appleIdCredential!.authorizationCode!)}');
          // logger.d('token: ${String.fromCharCodes(appleIdCredential.identityToken!)}');

          UserCredential userCredential = await FirebaseAuth.instance.signInWithCredential(oAuthCredential);
          if (userCredential.user != null) {
            SettingConfig.user_email = userCredential.user!.displayName ?? userCredential.user!.email ?? "...";
            loginSSO(context, {
              'Apple': AppleSSOModel(
                      email: userCredential.user?.email ?? '',
                      fullName: userCredential.user!.displayName ?? userCredential.user!.email,
                      token: appleIdCredential?.identityToken != null ? String.fromCharCodes(appleIdCredential!.identityToken!) : '',
                      code: appleIdCredential?.identityToken != null ? String.fromCharCodes(appleIdCredential!.authorizationCode!) : '')
                  .toJson()
            });
            logger.d("login apple: ${userCredential.user}");
            isLoading.value = false;
          }
        } catch (e) {
          logger.d("apple auth failed $e");
          isLoading.value = false;
        }

        break;
      case AuthorizationStatus.error:
        logger.d("error: ${authorizationResult.error}");
        isLoading.value = false;
        Utils.showSnackbar(context, "error${authorizationResult.error}", isError: true);

        break;
      case AuthorizationStatus.cancelled:
        logger.d("cancelled");
        isLoading.value = false;
        Utils.showSnackbar(context, "cancelled", isError: true);
        break;
    }
  }

  Future loginSSO(BuildContext context, dynamic payload) async {
    try {
      ProgressDialog.show(context);
      logger.d(payload);
      var res = await apiRepository.loginBySSO(payload);
      if (res != null && res.statusCode == 201 || res != null && res.statusCode == 200) {
        SettingConfig.token = res.data['access_token'];
        SettingConfig.refreshToken = res.data['refresh_token'];
        SettingConfig.user_id = res.data['user_id'];
        // final config = PeerNodeConfig(
        //   accessToken: SettingConfig.token,
        //   refreshToken: SettingConfig.refreshToken,
        //   adminAddr: AppConfig.instance.adminAddr,
        //   wsHost: AppConfig.instance.wsHost,
        //   wsPort: AppConfig.instance.wsPort,
        // );
        // Isolate.spawn<PeerNodeConfig>(runPeerNode, config);
        // SocketApp.connectSocket();
        //SocketAppSession.connectSocketSession();
        Get.offAllNamed(Routes.DASH_BOARD);
        Utils.showSnackbar(NavigateKeys.navigationKey.currentContext!, LocaleKeys.login_successfully.tr);

        var token = await firebaseMessaging.FirebaseMessaging.instance.getToken();
        if (token != null) {
          await ApiRepository().postTokenFCM(token, await Utils().deviceInfo());
        }
      } else {
        Utils.showSnackbar(NavigateKeys.navigationKey.currentContext!, LocaleKeys.login_fail.tr, isError: true);
      }
      isLoading.value = false;
      ProgressDialog.hide(context);
    } catch (e) {
      ProgressDialog.hide(context);
      isLoading.value = false;
      Utils.showSnackbar(NavigateKeys.navigationKey.currentContext!, "error: $e", isError: true);
    }
  }
}
