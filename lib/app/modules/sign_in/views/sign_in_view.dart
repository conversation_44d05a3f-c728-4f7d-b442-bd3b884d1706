// ignore_for_file: prefer_const_constructors, deprecated_member_use

import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_utils.dart';
import 'package:u2u_dpn/widget/widget.dart';
import 'package:url_launcher/url_launcher.dart';

import '../controllers/sign_in_controller.dart';

// ignore: must_be_immutable
class SignInView extends GetView<SignInController> {
  SignInView({Key? key}) : super(key: key);

  DateTime? currentBackPressTime;

  Future<bool> onWillPop(BuildContext context) {
    var now = DateTime.now();
    if (currentBackPressTime == null || now.difference(currentBackPressTime!) > Duration(seconds: 2)) {
      currentBackPressTime = now;
      //Utils.showSnackbar(context, LocaleKeys.press_again_to_exit.tr);
      Get.snackbar(
        '',
        '',
        titleText: SizedBox(height: 3),
        messageText: Text(
          LocaleKeys.press_again_to_exit.tr,
          textAlign: TextAlign.center,
          style: const TextStyle(color: Colors.white),
        ),
        padding: EdgeInsets.only(right: 16, left: 16, bottom: 8),
        margin: EdgeInsets.only(right: 16, left: 16, bottom: 32),
        backgroundColor: Colors.grey.withOpacity(0.6),
        snackPosition: SnackPosition.BOTTOM,
      );
      return Future.value(false);
    }
    SystemNavigator.pop();
    return Future.value(true);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () => onWillPop(context),
        child: Scaffold(
          backgroundColor: AppColor.neutralBlack,
          body: Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  AppImages.bg_sign_in,
                ),
                fit: BoxFit.fitWidth,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Expanded(
                  child: Center(
                    child: Image.asset(
                      AppImages.logo_login,
                      width: 190,
                    ),
                  ),
                ),
                Column(
                  children: [
                    CustomText(
                      text: LocaleKeys.connect_to_u2dpn.tr,
                      fontSize: 32,
                      textAlign: TextAlign.center,
                      paddingHorizontal: 70,
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: Divider(
                            color: AppColor.neutral400,
                            thickness: 0.5,
                            endIndent: 16,
                            indent: 16,
                          ),
                        ),
                        CustomText(
                          text: LocaleKeys.sign_in.tr.toUpperCase(),
                          fontSize: 16,
                          color: AppColor.neutral400,
                          fontWeight: FontWeight.w600,
                        ),
                        Expanded(
                          child: Divider(
                            color: AppColor.neutral400,
                            thickness: 0.5,
                            endIndent: 16,
                            indent: 16,
                          ),
                        ),
                      ],
                    ).paddingTop(20),
                    Obx(() => controller.isLoading.value
                        ? CircularProgressIndicator(
                            color: AppColor.primaryGreen700,
                          ).paddingOnly(top: 80, bottom: 20)
                        : Column(
                            children: [
                              CostomRaisedButtom(
                                name: LocaleKeys.sign_in_with_google.tr,
                                // isHintButton: !controller.isChecked.value,
                                function: () {
                                  controller.isLoading.value = true;

                                  if (!kIsWeb) {
                                    Platform.isWindows ? controller.loginWithGoogle(context) : controller.loginWithGoogleMobile(context);
                                  } else {
                                    controller.loginWithGoogleMobile(context);
                                  }
                                },
                                iconLeft: false,
                                iconButton: SvgPicture.asset(AppImages.icon_gmail, color: AppColor.neutral600),
                                paddingIconStar: 10,
                                // colorsGradient: controller.isChecked.value == false
                                //     ? [
                                //         AppColor.neutral600.withOpacity(0.6),
                                //         AppColor.neutral600.withOpacity(0.6)
                                //       ]
                                //     : [AppColor.neutral600, AppColor.neutral600],
                                // colorText: controller.isChecked.value == false
                                //     ? Colors.grey
                                //     : Colors.white,
                              ).paddingOnly(top: 32, bottom: 16, left: 16, right: 16),
                              if (!kIsWeb)
                                if (Platform.isIOS)
                                  CostomRaisedButtom(
                                    // isHintButton: !controller.isChecked.value,
                                    name: LocaleKeys.sign_in_with_apple.tr,
                                    function: () {
                                      controller.isLoading.value = true;

                                      controller.loginWhitApple(context);
                                    },
                                    iconLeft: false,
                                    iconButton: SvgPicture.asset(AppImages.icon_apple, color: AppColor.neutral600),
                                    paddingIconStar: 10,
                                    // colorsGradient: controller.isChecked.value == false
                                    //     ? [
                                    //         AppColor.neutral600.withOpacity(0.6),
                                    //         AppColor.neutral600.withOpacity(0.6)
                                    //       ]
                                    //     : [AppColor.neutral600, AppColor.neutral600],
                                    // colorText: controller.isChecked.value == false
                                    //     ? Colors.grey
                                    //     : Colors.white,
                                  ).paddingOnly(bottom: 16, left: 16, right: 16),
                            ],
                          )),
                    Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Obx(() => Checkbox(
                          //     activeColor: AppColor.primaryBlue500,
                          //     checkColor: Colors.black,
                          //     side: BorderSide(
                          //       color: Colors.white,
                          //     ),
                          //     value: controller.isChecked.value,
                          //     onChanged: (bool? value) {
                          //       controller.isChecked.value = value!;
                          //     })),
                          ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: Get.width * 0.6),
                            child: RichText(
                              textAlign: TextAlign.center,
                              text: TextSpan(
                                text: LocaleKeys.i_accept_all.tr,
                                style: TextStyle(fontSize: 14, color: AppColor.neutral100),
                                children: [
                                  TextSpan(
                                    text: LocaleKeys.privacy_policy.tr,
                                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14, color: Colors.blue),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () {
                                        launchUrl(
                                          Uri.parse("https://docs.u2dpn.xyz/u2dpn-privacy-policy"),
                                          mode: LaunchMode.externalApplication,
                                        );
                                      },
                                  ),
                                ],
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 26,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }
}
