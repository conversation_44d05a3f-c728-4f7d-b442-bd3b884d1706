import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class OtpController extends GetxController {
  var isHideButton = true.obs;
  var controllerTextField = TextEditingController().obs;
  final count = 0.obs;

  @override
  void onClose() {
    controllerTextField.value.dispose();
    super.onClose();
  }

  void increment() => count.value++;

  void insertText(String myText) {
    final text = controllerTextField.value.text;
    final textSelection = controllerTextField.value.selection;
    final newText = text.replaceRange(
      textSelection.start,
      textSelection.end,
      myText,
    );
    final myTextLength = myText.length;
    if (controllerTextField.value.text.length < 6) {
      controllerTextField.value.text = newText;
      controllerTextField.value.selection = textSelection.copyWith(
        baseOffset: textSelection.start + myTextLength,
        extentOffset: textSelection.start + myTextLength,
      );
    }
  }

  void backspace() {
    final text = controllerTextField.value.text;
    final textSelection = controllerTextField.value.selection;
    final selectionLength = textSelection.end - textSelection.start;

    // There is a selection.
    if (selectionLength > 0) {
      final newText = text.replaceRange(
        textSelection.start,
        textSelection.end,
        '',
      );
      if (controllerTextField.value.text.length < 6) {
        controllerTextField.value.text = newText;
        controllerTextField.value.selection = textSelection.copyWith(
          baseOffset: textSelection.start,
          extentOffset: textSelection.start,
        );
      }
      return;
    }

    // The cursor is at the beginning.
    if (textSelection.start == 0) {
      return;
    }

    // Delete the previous character
    final previousCodeUnit = text.codeUnitAt(textSelection.start - 1);
    final offset = isUtf16Surrogate(previousCodeUnit) ? 2 : 1;
    final newStart = textSelection.start - offset;
    final newEnd = textSelection.start;
    final newText = text.replaceRange(
      newStart,
      newEnd,
      '',
    );
    controllerTextField.value.text = newText;
    controllerTextField.value.selection = textSelection.copyWith(
      baseOffset: newStart,
      extentOffset: newStart,
    );
  }

  bool isUtf16Surrogate(int value) {
    return value & 0xF800 == 0xD800;
  }

  void checkHideButton() {
    if (controllerTextField.value.text.isEmpty) {
      isHideButton.value = true;
    } else {
      isHideButton.value = false;
    }
  }
}
