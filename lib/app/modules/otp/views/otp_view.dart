import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:pinput/pinput.dart';
import 'package:u2u_dpn/app/modules/connection/views/custom_keyboard.dart';
import 'package:u2u_dpn/utils/app_utils.dart';
import 'package:u2u_dpn/widget/widget.dart';

import '../controllers/otp_controller.dart';

class OtpView extends GetView<OtpController> {
  const OtpView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBarTitle(context),
        backgroundColor: AppColor.neutral800,
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CustomText(text: 'Enter the 4 digit- code', fontSize: 32),
                  const CustomText(
                    text: 'We sent the security code in your email',
                    fontSize: 14,
                    color: AppColor.neutral300,
                  ),
                  const CustomText(
                    text: ' <EMAIL>',
                    fontSize: 14,
                    color: AppColor.neutral200,
                  ),
                  Obx(() => Pinput(
                        controller: controller.controllerTextField.value,
                      ))
                  // Container(
                  //   margin: EdgeInsets.symmetric(vertical: 24 ),
                  //   width: Get.width,
                  //     child: CustomOTPTextField(controller: OtpFieldController(), onChange: (value){}))
                ],
              ),
              Column(
                children: [
                  CustomKeyboard(
                    onTextInput: (myText) {
                      controller.insertText(myText);
                      controller.checkHideButton();
                    },
                    onBackspace: () {
                      controller.backspace();
                      controller.checkHideButton();
                    },
                  ),
                  Obx(() => CostomRaisedButtom(
                        name: 'Save',
                        function: () {},
                        isHintButton: controller.isHideButton.value,
                      ).paddingOnly(top: 16, bottom: 24))
                ],
              )
            ],
          ),
        ));
  }
}
