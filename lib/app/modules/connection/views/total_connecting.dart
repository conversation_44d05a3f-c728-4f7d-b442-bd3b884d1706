// ignore_for_file: deprecated_member_use, must_be_immutable

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/app/modules/connection/views/detail_active_session.dart';
import 'package:u2u_dpn/model/active_connection_model.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
import 'package:u2u_dpn/widget/ultil.dart';

class TotalConnecting extends GetView<ConnectionController> {
  const TotalConnecting({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
  
        Obx(() => controller.listActiveConnection.isNotEmpty
            ? ListView.builder(
                shrinkWrap: true,
                
                padding: const EdgeInsets.only(bottom: 5),
                physics: NeverScrollableScrollPhysics(),
                itemCount: controller.listActiveConnection.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                      onTap: () {
                        // Get.toNamed(Routes.TIMER_DEMO);
                        controller.showTotal.value = false;
                        Get.to(SessionDetailPage(
                          hash: controller.listActiveConnection[index].sessionHash,
                        ));
                      },
                      child: SessionCard( activeConnectionModel:  controller.listActiveConnection[index]).paddingOnly(bottom: 16));
                }).paddingTop(10)
            : 
                  SizedBox(height:0,)
               
                
              )
      ],
    );
  }


  // Widget sessionCards(ActiveConnectionModel activeConnectionModel) {

  //   return Container(
  //     padding: const EdgeInsets.all(16),
  //     decoration: BoxDecoration(
  //         color: AppColor.neutralBlack,
  //         borderRadius: BorderRadius.circular(16),
  //         boxShadow: const [BoxShadow(color: AppColor.neutral700, blurRadius: 2)]),
  //     child: Row(
  //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //       children: [
  //         Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Row(
  //               children: [
  //                 CustomText(
  //                   text: activeConnectionModel.status?.toLowerCase().tr,
  //                   fontSize: 16,
  //                   color: AppColor.primaryGreen600,
  //                 ),
  //                 Container(
  //                     margin: const EdgeInsets.only(left: 6),
  //                     padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
  //                     decoration: BoxDecoration(borderRadius: BorderRadius.circular(6), color: AppColor.neutral500),
  //                     child: CustomText(
  //                       text: Utils().convertTimefromMicro(activeConnectionModel.handshakeAt ?? 0),
  //                       fontSize: 12,
  //                       color: AppColor.neutral300,
  //                     ))
  //               ],
  //             ),
  //             Row(
  //               children: [
  //                 Image.asset(
  //                   AppImages.icon_router,
  //                   width: 16,
  //                 ).paddingRight(8),
  //                 CustomText(text: activeConnectionModel.getBandwidthUsage)
  //               ],
  //             ).paddingTop(8),
  //             Row(
  //               children: [
  //                 Image.asset(
  //                   AppImages.logo,
  //                   width: 16,
  //                 ).paddingRight(8),
  //                 CustomText(text: activeConnectionModel.gettotal)
  //               ],
  //             ).paddingSymmetric(vertical: 8),
  //             Row(
  //               children: [
  //                 Image.asset(
  //                   AppImages.logoU2U,
  //                   width: 16,
  //                 ).paddingRight(8),
  //                 CustomText(text: activeConnectionModel.gettotalV2)
  //               ],
  //             ),
  //             // const CustomText(
  //             //   text: "21/01/2023 - 13:30",
  //             //   fontSize: 14,
  //             //   color: AppColor.neutral400,
  //             //   fontWeight: FontWeight.w400,
  //             // )
  //           ],
  //         ),
  //         const Icon(
  //           Icons.arrow_forward_ios_rounded,
  //           color: AppColor.neutral400,
  //         )
  //       ],
  //     ),
  //   );
  // }

}

class SessionCard extends StatefulWidget {
  ActiveConnectionModel activeConnectionModel;
   SessionCard({super.key ,required this.activeConnectionModel});

  @override
  State<SessionCard> createState() => _SessionCardState();
}

class _SessionCardState extends State<SessionCard> {
    final RxInt elapsedSeconds = 0.obs;
  Timer? _timer;

  @override
  void initState() {
  
    super.initState();
     final start = (widget.activeConnectionModel.handshakeAt ?? 0) ;
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      elapsedSeconds.value = now - start;

      _timer = Timer.periodic(const Duration(seconds: 1), (_) {
        elapsedSeconds.value++;
        
      });
  }
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return  Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
          color: AppColor.neutralBlack,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [BoxShadow(color: AppColor.neutral700, blurRadius: 2)]),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CustomText(
                    text: widget.activeConnectionModel.status?.toLowerCase().tr,
                    fontSize: 16,
                    color: AppColor.primaryGreen600,
                  ),
                Obx(() =>   Container(
                      margin: const EdgeInsets.only(left: 6),
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(6), color: AppColor.neutral500),
                      child: CustomText(
                        text: Utils.formattedTimeDuration(elapsedSeconds.value),
                        fontSize: 12,
                        color: AppColor.neutral300,
                      )))
                ],
              ),
              Row(
                children: [
                  Image.asset(
                    AppImages.icon_router,
                    width: 16,
                  ).paddingRight(8),
                  CustomText(text: widget.activeConnectionModel.getBandwidthUsage)
                ],
              ).paddingTop(8),
              Row(
                children: [
                  Image.asset(
                    AppImages.logo,
                    width: 16,
                  ).paddingRight(8),
                  CustomText(text: widget.activeConnectionModel.gettotal)
                ],
              ).paddingSymmetric(vertical: 8),
              Row(
                children: [
                  Image.asset(
                    AppImages.logoU2U,
                    width: 16,
                  ).paddingRight(8),
                  CustomText(text: widget.activeConnectionModel.gettotalV2)
                ],
              ),
              // const CustomText(
              //   text: "21/01/2023 - 13:30",
              //   fontSize: 14,
              //   color: AppColor.neutral400,
              //   fontWeight: FontWeight.w400,
              // )
            ],
          ),
          const Icon(
            Icons.arrow_forward_ios_rounded,
            color: AppColor.neutral400,
          )
        ],
      ),
    );
      }
}