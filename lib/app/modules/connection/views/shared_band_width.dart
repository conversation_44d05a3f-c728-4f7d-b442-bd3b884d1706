// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';

// import 'package:get/get.dart';
// import 'package:nb_utils/nb_utils.dart';
// import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
// import 'package:u2u_dpn/locales/locales.g.dart';
// import 'package:u2u_dpn/model/connection_history_model.dart';
// import 'package:u2u_dpn/utils/app_color.dart';
// import 'package:u2u_dpn/utils/app_images.dart';
// import 'package:u2u_dpn/widget/cell_text.dart';
// import 'package:u2u_dpn/widget/custom_appbar.dart';
// import 'package:u2u_dpn/widget/custom_text.dart';
// import 'package:u2u_dpn/widget/ultil.dart';

// class SharedBandwidth extends GetView<ConnectionController> {
//   const SharedBandwidth({super.key, required this.connectionHistoryModel});
//   final ConnectionHistoryModel? connectionHistoryModel;

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       extendBodyBehindAppBar: true,
//       appBar: CustomAppbarWithLeading(context, LocaleKeys.session_detail.tr),
//       backgroundColor: const Color(0xFF120E21),
//       body: Column(
//         children: [
//           SizedBox(
//             height: Get.height * 0.4,
//             child: Stack(
//               children: [
//                 Image.asset(
//                   AppImages.bg_session_detail,
//                   width: Get.width,
//                   fit: BoxFit.fitWidth,
//                 ),
//                 Positioned(
//                   bottom: 65,
//                   left: Get.width * 0.25,
//                   child: Center(
//                     child: Column(
//                       children: [
//                         CustomText(
//                           text:
//                               connectionHistoryModel?.getsharedBandwidth ?? Utils.kbToGb(controller.activeConnectionModel.value.bandwidthUsage ?? 0),
//                           fontSize: 24,
//                           fontWeight: FontWeight.w500,
//                         ).paddingBottom(8),
//                         Text(
//                           '${LocaleKeys.last_updated.tr}: ${Utils.convertTimestamptoDatetime((DateTime.now().millisecondsSinceEpoch) ~/ 1000)}',
//                           textAlign: TextAlign.center,
//                           style: const TextStyle(
//                             fontSize: 14,
//                             fontWeight: FontWeight.w500,
//                             color: Color(0xFF65636F),
//                           ),
//                         )
//                       ],
//                     ),
//                   ),
//                   // child: Center(
//                   //   child: RichText(
//                   //     text: TextSpan(
//                   //       text: Utils.kbToGb((activeConnectionModel.bandwidthUsage??0).toDouble()).toString(),
//                   //       style: TextStyle(fontSize: 32, fontWeight: FontWeight.w500, color: Color(0xff1AF7A9)),
//                   //     ),
//                   //   ),
//                   // ),
//                 )
//               ],
//             ),
//           ),
//           Expanded(
//             child: Container(
//               decoration: const BoxDecoration(
//                   color: AppColor.neutralBlack, borderRadius: BorderRadius.only(topLeft: Radius.circular(15), topRight: Radius.circular(15))),
//               child: Column(
//                 children: [
//                   Padding(
//                     padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
//                     child: cellText(
//                       LocaleKeys.session_hash.tr,
//                       Row(
//                         children: [
//                           Text(
//                             Utils.shortenUsername(
//                                 connectionHistoryModel?.session_hash.toString() ?? controller.activeConnectionModel.value.sessionHash.toString(), 4,
//                                 showlastString: true),
//                             textAlign: TextAlign.center,
//                             style: const TextStyle(
//                               fontSize: 16,
//                               fontWeight: FontWeight.w500,
//                               color: Color(0xFFDEDEDE),
//                             ),
//                           ),
//                           const Icon(
//                             Icons.copy,
//                             color: AppColor.neutral300,
//                             size: 15,
//                           ).onTap(() async {
//                             Clipboard.setData(ClipboardData(
//                                     text: connectionHistoryModel?.session_hash.toString() ??
//                                         controller.activeConnectionModel.value.sessionHash.toString()))
//                                 .then((value) {
//                               // ignore: use_build_context_synchronously
//                               Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
//                             });
//                           }).paddingLeft(5)
//                         ],
//                       ),
//                     ),
//                   ).paddingOnly(bottom: 8),
//                   Padding(
//                     padding: const EdgeInsets.symmetric(horizontal: 16),
//                     child: cellText(
//                       LocaleKeys.started_at.tr,
//                       Text(
//                         connectionHistoryModel?.getStarted ?? controller.activeConnectionModel.value.getStarted.toString(),
//                         textAlign: TextAlign.center,
//                         style: const TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.w500,
//                           color: Color(0xFFDEDEDE),
//                         ),
//                       ),
//                     ),
//                   ).paddingOnly(bottom: 8),
//                   if (connectionHistoryModel != null)
//                     Padding(
//                       padding: const EdgeInsets.symmetric(horizontal: 16),
//                       child: cellText(
//                         LocaleKeys.ended_at.tr,
//                         Text(
//                           connectionHistoryModel?.getEndTime ?? controller.activeConnectionModel.value.getStarted.toString(),
//                           textAlign: TextAlign.center,
//                           style: const TextStyle(
//                             fontSize: 16,
//                             fontWeight: FontWeight.w500,
//                             color: Color(0xFFDEDEDE),
//                           ),
//                         ),
//                       ),
//                     ).paddingOnly(bottom: 8),
//                   if (connectionHistoryModel != null)
//                     Padding(
//                       padding: const EdgeInsets.symmetric(horizontal: 16),
//                       child: cellText(
//                           LocaleKeys.duration.tr,
//                           CustomText(
//                               text: connectionHistoryModel?.getDuration,
//                               // text: Utils().convertTimefromMicro(
//                               //   controller.activeConnectionModel.value.handshakeAt ?? 0,
//                               // ),
//                               fontSize: 14)),
//                     ).paddingOnly(bottom: 8),
//                   // if (controller.activeConnectionModel.value.ratePerSecond != null)
//                   if (connectionHistoryModel?.ratePerSecond != null && connectionHistoryModel?.ratePerSecond != 0)
//                     Padding(
//                       padding: const EdgeInsets.symmetric(horizontal: 16),
//                       child: cellText(
//                         LocaleKeys.selling_price_per_hour.tr,
//                         Row(
//                           children: [
//                             ConstrainedBox(
//                               constraints: BoxConstraints(maxWidth: Get.width * 0.25),
//                               child: Text(
//                                 connectionHistoryModel?.getRatePerHour ?? controller.activeConnectionModel.value.getRatePerHour,
//                                 maxLines: 1,
//                                 overflow: TextOverflow.ellipsis,
//                                 textAlign: TextAlign.right,
//                                 style: const TextStyle(
//                                   fontSize: 14,
//                                   fontWeight: FontWeight.w500,
//                                   color: Color(0xFFDEDEDE),
//                                 ),
//                               ),
//                             ),
//                             const SizedBox(
//                               width: 4,
//                             ),
//                             Image.asset(
//                               AppImages.logo,
//                               width: 20,
//                             ),
//                             Text(
//                               ' /${LocaleKeys.hour.tr}',
//                               textAlign: TextAlign.center,
//                               style: const TextStyle(
//                                 fontSize: 14,
//                                 fontWeight: FontWeight.w500,
//                                 color: AppColor.neutral400,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ).paddingOnly(bottom: 8),
//                     ),
//                   // if (controller.activeConnectionModel.value.ratePerKb != null)

//                   Padding(
//                     padding: const EdgeInsets.symmetric(horizontal: 16),
//                     child: cellText(
//                       LocaleKeys.selling_price_per_gb.tr,
//                       Row(
//                         children: [
//                           ConstrainedBox(
//                             constraints: BoxConstraints(maxWidth: Get.width * 0.25),
//                             child: Text(
//                               connectionHistoryModel?.getRatePerGb ?? controller.activeConnectionModel.value.getRatePerGb,
//                               maxLines: 1,
//                               overflow: TextOverflow.ellipsis,
//                               textAlign: TextAlign.right,
//                               style: const TextStyle(
//                                 fontSize: 14,
//                                 fontWeight: FontWeight.w500,
//                                 color: Color(0xFFDEDEDE),
//                               ),
//                             ),
//                           ),
//                           const SizedBox(
//                             width: 4,
//                           ),
//                           Image.asset(
//                             AppImages.logo,
//                             width: 20,
//                           ),
//                           const Text(
//                             ' / GB',
//                             textAlign: TextAlign.center,
//                             style: TextStyle(
//                               fontSize: 14,
//                               fontWeight: FontWeight.w500,
//                               color: AppColor.neutral400,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ).paddingOnly(bottom: 8),
//                   ),

//                   Padding(
//                     padding: const EdgeInsets.symmetric(horizontal: 16),
//                     child: cellText(
//                       LocaleKeys.selling_price_per_gb.tr,
//                       Row(
//                         children: [
//                           ConstrainedBox(
//                             constraints: BoxConstraints(maxWidth: Get.width * 0.25),
//                             child: Text(
//                               connectionHistoryModel?.getRatePerKbV2 ?? controller.activeConnectionModel.value.getRatePerGbV2,
//                               maxLines: 1,
//                               overflow: TextOverflow.ellipsis,
//                               textAlign: TextAlign.right,
//                               style: const TextStyle(
//                                 fontSize: 14,
//                                 fontWeight: FontWeight.w500,
//                                 color: Color(0xFFDEDEDE),
//                               ),
//                             ),
//                           ),
//                           const SizedBox(
//                             width: 4,
//                           ),
//                           Image.asset(
//                             AppImages.logoU2U,
//                             width: 20,
//                           ),
//                           const Text(
//                             ' / GB',
//                             textAlign: TextAlign.center,
//                             style: TextStyle(
//                               fontSize: 14,
//                               fontWeight: FontWeight.w500,
//                               color: AppColor.neutral400,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ).paddingOnly(bottom: 8),
//                   ),
//                   // Padding(
//                   //   padding: const EdgeInsets.symmetric(horizontal: 16),
//                   //   child: cellText(
//                   //     'Selling price per hour',
//                   //     Row(
//                   //       children: [
//                   //         Row(
//                   //           children: [
//                   //             ConstrainedBox(
//                   //               constraints: BoxConstraints(maxWidth: Get.width * 0.25),
//                   //               child: Obx(() => CustomText(
//                   //                     text: controller.activeConnectionModel.value.getDurationFee,
//                   //                     textAlign: TextAlign.right,
//                   //                     overflow: TextOverflow.ellipsis,
//                   //                     fontSize: 14,
//                   //                   )),
//                   //             ),
//                   //             const SizedBox(
//                   //               width: 4,
//                   //             ),
//                   //             Image.asset(
//                   //               AppImages.logo,
//                   //               width: 20,
//                   //             ),
//                   //           ],
//                   //         ),
//                   //         const CustomText(
//                   //           text: ' / hour',
//                   //           color: AppColor.neutral400,
//                   //         )
//                   //       ],
//                   //     ),
//                   //   ),
//                   // ).paddingOnly(bottom: 8),
//                   // Padding(
//                   //   padding: const EdgeInsets.symmetric(horizontal: 16),
//                   //   child: cellText(
//                   //     'Selling price per GB',
//                   //     Row(
//                   //       children: [
//                   //         Row(
//                   //           children: [
//                   //             ConstrainedBox(
//                   //               constraints: BoxConstraints(maxWidth: Get.width * 0.25),
//                   //               child: Obx(() => CustomText(
//                   //                     text: controller.activeConnectionModel.value.getDurationFee,
//                   //                     textAlign: TextAlign.right,
//                   //                     overflow: TextOverflow.ellipsis,
//                   //                     fontSize: 14,
//                   //                   )),
//                   //             ),
//                   //             const SizedBox(
//                   //               width: 4,
//                   //             ),
//                   //             Image.asset(
//                   //               AppImages.logo,
//                   //               width: 20,
//                   //             ),
//                   //           ],
//                   //         ),
//                   //         const CustomText(
//                   //           text: ' / GB',
//                   //           color: AppColor.neutral400,
//                   //         )
//                   //       ],
//                   //     ),
//                   //   ),
//                   // ).paddingOnly(bottom: 8),
//                   Padding(
//                     padding: const EdgeInsets.symmetric(horizontal: 16),
//                     child: cellText(
//                       '${LocaleKeys.gross_amount.tr} U2U',
//                       Row(
//                         children: [
//                           Row(
//                             children: [
//                               ConstrainedBox(
//                                 constraints: BoxConstraints(maxWidth: Get.width * 0.25),
//                                 child: CustomText(
//                                   text: connectionHistoryModel?.getTotalFeeV2 ?? controller.activeConnectionModel.value.gettotalV2,
//                                   textAlign: TextAlign.right,
//                                   overflow: TextOverflow.ellipsis,
//                                   fontSize: 14,
//                                 ),
//                               ),
//                               const SizedBox(
//                                 width: 4,
//                               ),
//                               Image.asset(
//                                 AppImages.logoU2U,
//                                 width: 20,
//                               ),
//                             ],
//                           ),
//                         ],
//                       ),
//                     ),
//                   ).paddingOnly(bottom: 8),

//                   Obx(() => Column(
//                         children: [
//                           cellText(
//                             '${LocaleKeys.gross_amount.tr} U2DPN',
//                             Row(
//                               children: [
//                                 ConstrainedBox(
//                                   constraints: BoxConstraints(maxWidth: Get.width * 0.25),
//                                   child: Text(
//                                     connectionHistoryModel?.getTotalFee ?? controller.activeConnectionModel.value.gettotal.toString(),
//                                     textAlign: TextAlign.right,
//                                     overflow: TextOverflow.ellipsis,
//                                     style: const TextStyle(
//                                       fontSize: 14,
//                                       fontWeight: FontWeight.w500,
//                                       color: Color(0xFFDEDEDE),
//                                     ),
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   width: 4,
//                                 ),
//                                 Image.asset(
//                                   AppImages.logo,
//                                   width: 20,
//                                 ),
//                                 Container(
//                                   margin: const EdgeInsets.only(left: 6),
//                                   decoration: BoxDecoration(color: AppColor.neutral600, borderRadius: BorderRadius.circular(4)),
//                                   child: Icon(
//                                     controller.showTotal.value ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
//                                     color: AppColor.neutral300,
//                                   ),
//                                 )
//                               ],
//                             ),
//                           ).onTap(() {
//                             controller.showTotal.value = !controller.showTotal.value;
//                           }).paddingOnly(bottom: 8),
//                           if (controller.showTotal.value) ...[
//                             Container(
//                                 padding: const EdgeInsets.symmetric(horizontal: 8),
//                                 decoration: const BoxDecoration(color: AppColor.neutral700, borderRadius: BorderRadius.all(Radius.circular(15))),
//                                 child: Column(
//                                   children: [
//                                     cellText(
//                                       LocaleKeys.net_amount.tr,
//                                       Row(
//                                         children: [
//                                           ConstrainedBox(
//                                             constraints: BoxConstraints(maxWidth: Get.width * 0.25),
//                                             child: Text(
//                                               connectionHistoryModel
//                                                       ?.getNetAmount(controller.appController.userDetailModel.value.userTier?.tier ?? "") ??
//                                                   controller.activeConnectionModel.value
//                                                       .getNetAmount(controller.appController.userDetailModel.value.userTier?.tier ?? ""),
//                                               textAlign: TextAlign.right,
//                                               overflow: TextOverflow.ellipsis,
//                                               style: const TextStyle(
//                                                 fontSize: 14,
//                                                 fontWeight: FontWeight.w500,
//                                                 color: Color(0xFFDEDEDE),
//                                               ),
//                                             ),
//                                           ),
//                                           const SizedBox(
//                                             width: 4,
//                                           ),
//                                           Image.asset(
//                                             AppImages.logo,
//                                             width: 20,
//                                           ),
//                                         ],
//                                       ),
//                                     ).paddingOnly(top: 8),
//                                     cellText(
//                                       LocaleKeys.platform_fee.tr,
//                                       Row(
//                                         children: [
//                                           ConstrainedBox(
//                                             constraints: BoxConstraints(maxWidth: Get.width * 0.25),
//                                             child: Text(
//                                               connectionHistoryModel
//                                                       ?.getPlatformFee(controller.appController.userDetailModel.value.userTier?.tier ?? "") ??
//                                                   controller.activeConnectionModel.value
//                                                       .getPlatformFee(controller.appController.userDetailModel.value.userTier?.tier ?? ""),
//                                               textAlign: TextAlign.right,
//                                               overflow: TextOverflow.ellipsis,
//                                               style: const TextStyle(
//                                                 fontSize: 14,
//                                                 fontWeight: FontWeight.w500,
//                                                 color: Color(0xFFDEDEDE),
//                                               ),
//                                             ),
//                                           ),
//                                           const SizedBox(
//                                             width: 4,
//                                           ),
//                                           Image.asset(
//                                             AppImages.logo,
//                                             width: 20,
//                                           ),
//                                         ],
//                                       ),
//                                     ).paddingOnly(top: 8),
//                                     cellText(
//                                       LocaleKeys.referral_fee.tr,
//                                       Row(
//                                         children: [
//                                           ConstrainedBox(
//                                             constraints: BoxConstraints(maxWidth: Get.width * 0.25),
//                                             child: Text(
//                                               connectionHistoryModel
//                                                       ?.getReferralFee(controller.appController.userDetailModel.value.userTier?.tier ?? "") ??
//                                                   controller.activeConnectionModel.value
//                                                       .getReferralFee(controller.appController.userDetailModel.value.userTier?.tier ?? ""),
//                                               textAlign: TextAlign.right,
//                                               overflow: TextOverflow.ellipsis,
//                                               style: const TextStyle(
//                                                 fontSize: 14,
//                                                 fontWeight: FontWeight.w500,
//                                                 color: Color(0xFFDEDEDE),
//                                               ),
//                                             ),
//                                           ),
//                                           const SizedBox(
//                                             width: 4,
//                                           ),
//                                           Image.asset(
//                                             AppImages.logo,
//                                             width: 20,
//                                           ),
//                                         ],
//                                       ),
//                                     ).paddingOnly(top: 8, bottom: 8)
//                                   ],
//                                 )),
//                           ],
//                         ],
//                       )).paddingSymmetric(horizontal: 16),
//                   // const Spacer(),
//                   // CostomRaisedButtom(
//                   //   name: LocaleKeys.back.tr,
//                   //   function: () {
//                   //     Get.back();
//                   //   },
//                   //   colorText: Colors.white,
//                   //   colorsGradient: const [Color(0xFF2D2C3B), Color(0xFF2D2C3B)],
//                   // ),
//                   const SizedBox(
//                     height: 16,
//                   ),
//                 ],
//               ),
//             ),
//           )
//         ],
//       ),
//     );
//   }
// }
