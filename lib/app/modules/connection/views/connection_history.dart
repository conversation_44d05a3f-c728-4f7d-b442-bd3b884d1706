// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/app/modules/connection/views/detail_active_session.dart';
import 'package:u2u_dpn/app/modules/connection/views/total_connecting.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/model/connection_history_model.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/CustomContainerShadow.dart';
import 'package:u2u_dpn/widget/ultil.dart';

import '../../../../widget/widget.dart';

class ConnectionHistory extends GetView<ConnectionController> {
  const ConnectionHistory({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: AppColor.neutralBlack,
        appBar: CustomAppbarWithLeading(
            context,
            bgColor: AppColor.neutral900,
            LocaleKeys.sessions.tr,
            actions: [
              // CustomText(
              //   text: LocaleKeys.today_boost.tr,
              //   fontSize: 12,
              //   color: AppColor.neutral300,
              // ),
              // Container(
              //   padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
              //   decoration: BoxDecoration(color: AppColor.neutral400, borderRadius: BorderRadius.circular(8)),
              //   child: const CustomText(text: "0%"),
              // ).paddingOnly(left: 4, right: 16)
            ]),
        body: SmartRefresher(
          onRefresh: controller.onRefreshHistory,
          controller: controller.refreshHistoryController,
          enablePullDown: true,
          child: SingleChildScrollView(
            child: Container(
              width: Get.width,
              height: Get.height > 800 ? Get.height : 800,
              decoration: const BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage(AppImages.background),
                      fit: BoxFit.fill)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: kToolbarHeight + 40), //24 + 16 padding
                  bannerSessions(),
                  //     locationBonus(),
                  CustomText(
                    text: LocaleKeys.all_sessions.tr.toUpperCase(),
                    fontSize: 12,
                    color: AppColor.neutral300,
                  ).paddingSymmetric(vertical: 16),
                  // CustomNodata(),

                  Obx(() {
                    if (controller.listConnectionHistoryData.isNotEmpty) {
                      return Expanded(
                          child: ListView.separated(
                        shrinkWrap: true,
                        padding: const EdgeInsets.only(bottom: 16),
                        itemCount: controller.listConnectionHistoryData.length,
                        itemBuilder: (BuildContext context, int index) {
                          return 
                           Column(
                             children: [
 if( index==0) const TotalConnecting(),
                               sessionCards(controller.listConnectionHistoryData[index], () {
                                // Get.toNamed(Routes.SHARED_BANDWIDTH);
                                Get.to(
                                  () => SessionDetailPage(
                                    connectionHistoryModel: controller
                                        .listConnectionHistoryData[index],
                                  ),
                                  transition: Transition.rightToLeft,
                                );
                                // print(controller.listConnectionHistoryData[index].bandwidthUsage);
                              }),
                            ],
                          );
                        },
                        separatorBuilder: (BuildContext context, int index) {
                          return const SizedBox(height: 16);
                        },
                      ));
                    } else {
                      if( controller.listActiveConnection.isNotEmpty) {
                        return const Expanded(
                          child: TotalConnecting(),
                        );
                      }
                      return CustomNodata();
                    }
                  })
                ],
              ).paddingSymmetric(horizontal: 16),
            ),
          ),
        ));
  }

  Widget locationBonus() {
    return CustomContainerShadow(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            text: LocaleKeys.location_bonus.tr,
            fontSize: 12,
            color: AppColor.neutral300,
          ).paddingBottom(16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            width: Get.width,
            child: Row(mainAxisSize: MainAxisSize.min, mainAxisAlignment: MainAxisAlignment.center, children: [
              Image.asset(AppImages.icon_location, width: 20, fit: BoxFit.cover).paddingOnly(right: 8),
              Obx(() {
                return controller.ipLocationBonusModel.value.countryName != null
                    ? Row(
                        children: [
                          CustomText(
                            text: controller.ipLocationBonusModel.value.countryName,
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ).paddingOnly(left: 8),
                          controller.ipLocationBonusModel.value.bonusInfo != null
                              ? Container(
                                  decoration: BoxDecoration(
                                    gradient: AppColor.customLineGradientGreen,
                                    borderRadius: BorderRadius.circular(999),
                                    border: Border.all(color: AppColor.neutral500),
                                  ),
                                  margin: const EdgeInsets.only(left: 4),
                                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                  child: CustomText(
                                    text: '+${controller.ipLocationBonusModel.value.bonusInfo?.bonusAmountString}',
                                    color: Colors.black,
                                  ),
                                )
                              : const SizedBox()
                        ],
                      )
                    : const SizedBox();
              }),
               const Spacer(flex: 1),
              // const Icon(Icons.arrow_forward_ios_rounded, color: AppColor.neutral400, size: 14),
            ]),
          ).onTap(() {
            // Get.toNamed(Routes.LOCATION_BONUS);
          }),
        ],
      ),
    ).paddingSymmetric(vertical: 16);
  }

  Widget bannerSessions() {
    return Obx(() => CustomContainerShadow(
            child: Column(
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 1,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        text: LocaleKeys.bandwidth_shared.tr,
                        fontSize: 12,
                        color: AppColor.neutral300,
                      ),
                      CustomText(
                        text: controller.connectionOverviewModel.value
                            .getTotalBandwidthUsages,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ).paddingTop(8)
                    ],
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        text: LocaleKeys.total_online_time.tr,
                        fontSize: 12,
                        color: AppColor.neutral300,
                      ),
                      CustomText(
                              text: Utils()
                                  .formatDurationWithDayHourMinute(
                                      controller.totalTime.value)
                                  .toString())
                          .paddingTop(8)
                    ],
                  ),
                )
              ],
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: Divider(
                color: AppColor.neutral700,
                height: 0,
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: LocaleKeys.rewards_from_sharing.tr,
                  fontSize: 12,
                  color: AppColor.neutral300,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Image.asset(
                          AppImages.logo,
                          width: 20,
                        ).paddingRight(8),
                        CustomText(
                          text: controller
                              .connectionOverviewModel.value.getTotalRewards
                              .toString(),
                          fontSize: 16,
                        )
                      ],
                    ),
                    const CustomText(
                      text: "U2DPN",
                      fontSize: 16,
                      color: AppColor.neutral400,
                    )
                  ],
                ).paddingSymmetric(vertical: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Image.asset(
                          AppImages.logoU2U,
                          width: 20,
                        ).paddingRight(8),
                        CustomText(
                          text: controller
                              .connectionOverviewModel.value.getTotalRewardsV2
                              .toString(),
                          fontSize: 16,
                        )
                      ],
                    ),
                    const CustomText(
                      text: "U2U",
                      fontSize: 16,
                      color: AppColor.neutral400,
                    )
                  ],
                )
              ],
            )
          ],
        ))).paddingSymmetric(vertical: 16);
  }

  Widget sessionCards(
      ConnectionHistoryModel listConnectionHistoryData, Function() onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
            color: AppColor.neutralBlack,
            borderRadius: BorderRadius.circular(16),
            boxShadow: const [
              BoxShadow(color: AppColor.neutral700, blurRadius: 2)
            ]),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: listConnectionHistoryData.getStatus,
                  fontSize: 16,
                ),
                Row(
                  children: [
                    Image.asset(
                      AppImages.icon_router,
                      width: 16,
                    ).paddingRight(8),
                    CustomText(
                        text: listConnectionHistoryData.getsharedBandwidth)
                  ],
                ).paddingTop(8),
                Row(
                  children: [
                    Image.asset(
                      AppImages.logo,
                      width: 16,
                    ).paddingRight(8),
                    CustomText(text: listConnectionHistoryData.getTotalFee)
                  ],
                ).paddingSymmetric(vertical: 8),
                Row(
                  children: [
                    Image.asset(
                      AppImages.logoU2U,
                      width: 16,
                    ).paddingRight(8),
                    CustomText(text: listConnectionHistoryData.gettotalV2)
                  ],
                ),
                // const CustomText(
                //   text: "21/01/2023 - 13:30",
                //   fontSize: 14,
                //   color: AppColor.neutral400,
                //   fontWeight: FontWeight.w400,
                // )
              ],
            ),
            const Icon(
              Icons.arrow_forward_ios_rounded,
              color: AppColor.neutral400,
            )
          ],
        ),
      ),
    );
  }

  // Container bannerSession() {
  //   return Container(
  //     margin: const EdgeInsets.symmetric(vertical: 16),
  //     decoration: const BoxDecoration(
  //       boxShadow: [BoxShadow(color: Color(0xff323247), offset: Offset(0, 6))],
  //       borderRadius: BorderRadius.all(Radius.circular(16)),
  //       gradient: LinearGradient(
  //         colors: [Color(0xff2F2F47), Color(0xff72729A)],
  //         begin: Alignment.topCenter,
  //         end: Alignment.bottomCenter,
  //       ),
  //     ),
  //     child: Padding(
  //       padding: const EdgeInsets.all(1),
  //       child: Container(
  //           padding: const EdgeInsets.all(16),
  //           decoration: const BoxDecoration(
  //             borderRadius: BorderRadius.all(Radius.circular(16)),
  //             color: Color(0xFF1B1A28),
  //           ),
  //           child: Column(
  //             // crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               Row(
  //                 // mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                 children: [
  //                   Expanded(
  //                     flex: 1,
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       children: [
  //                         CustomText(
  //                           text: LocaleKeys.bandwidth_shared.tr,
  //                           fontSize: 12,
  //                           color: AppColor.neutral300,
  //                         ),
  //                         CustomText(
  //                           text: "",
  //                           fontSize: 16,
  //                         )
  //                       ],
  //                     ),
  //                   ),
  //                   Expanded(
  //                     flex: 1,
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       children: [
  //                         CustomText(
  //                           text: LocaleKeys.total_online_time.tr,
  //                           fontSize: 12,
  //                           color: AppColor.neutral300,
  //                         ),
  //                         CustomText(
  //                           text: "",
  //                           fontSize: 16,
  //                         )
  //                       ],
  //                     ),
  //                   )
  //                 ],
  //               ),
  //               const Padding(
  //                 padding: EdgeInsets.symmetric(vertical: 16),
  //                 child: Divider(
  //                   color: AppColor.neutral700,
  //                   height: 0,
  //                 ),
  //               ),
  //               Column(
  //                 crossAxisAlignment: CrossAxisAlignment.start,
  //                 children: [
  //                   CustomText(
  //                     text: LocaleKeys.rewards_from_sharing.tr,
  //                     fontSize: 12,
  //                     color: AppColor.neutral300,
  //                   ),
  //                   Row(
  //                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                     children: [
  //                       Row(
  //                         children: [
  //                           Image.asset(
  //                             AppImages.logo,
  //                             width: 20,
  //                           ).paddingRight(8),
  //                           const CustomText(
  //                             text: "583.12",
  //                             fontSize: 16,
  //                           )
  //                         ],
  //                       ),
  //                       const CustomText(
  //                         text: "U2DPN",
  //                         fontSize: 16,
  //                         color: AppColor.neutral400,
  //                       )
  //                     ],
  //                   ).paddingSymmetric(vertical: 12),
  //                   Row(
  //                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                     children: [
  //                       Row(
  //                         children: [
  //                           Image.asset(
  //                             AppImages.logoU2U,
  //                             width: 20,
  //                           ).paddingRight(8),
  //                           CustomText(
  //                             text: "0.00",
  //                             fontSize: 16,
  //                           )
  //                         ],
  //                       ),
  //                       CustomText(
  //                         text: "U2U",
  //                         fontSize: 16,
  //                         color: AppColor.neutral400,
  //                       )
  //                     ],
  //                   )
  //                 ],
  //               )
  //             ],
  //           )),
  //     ),
  //   );
  // }

  // Widget historyCell(
  //     BuildContext context, ConnectionHistoryModel connectionHistoryData) {
  //   return InkWell(
  //     onTap: () {
  //       controller.showTotal.value = false;
  //       CustomBottomSheet.showModalNotFullScreenWithHeight(
  //           context, modalInforConnection(connectionHistoryData, context),
  //           height: Get.height * 0.9);
  //     },
  //     child: Padding(
  //       padding: const EdgeInsets.only(top: 20),
  //       child: Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Image.asset(
  //             connectionHistoryData.getImage,
  //             width: 56,
  //           ),
  //           SizedBox(
  //             width: Get.width * 0.6,
  //             child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Text(
  //                   '${LocaleKeys.shared_bandwidth.tr} ${connectionHistoryData.getsharedBandwidth}',
  //                   textAlign: TextAlign.left,
  //                   style: const TextStyle(
  //                     fontSize: 16,
  //                     fontWeight: FontWeight.w500,
  //                     color: Color(0xFFFFFFFF),
  //                   ),
  //                 ),
  //                 RichText(
  //                   text: TextSpan(
  //                       text: '${connectionHistoryData.getTotalFee} U2DPN ',
  //                       style: const TextStyle(
  //                         fontSize: 14,
  //                         color: AppColor.neutral200,
  //                         fontWeight: FontWeight.w400,
  //                       ),
  //                       children: [
  //                         TextSpan(
  //                             text:
  //                                 '(${connectionHistoryData.getRatePerGb} U2DPN / GB)',
  //                             style:
  //                                 const TextStyle(color: AppColor.neutral300))
  //                       ]),
  //                 ).paddingOnly(top: 2, bottom: 8),
  //                 // Row(
  //                 //   children: [
  //                 //     Text(
  //                 //       '${Utils.convertSzaboToU2U((connectionHistoryData.totalFee??0).toDouble())} U2U ',
  //                 //       textAlign: TextAlign.center,
  //                 //       style: const TextStyle(
  //                 //         fontSize: 14,
  //                 //         overflow: TextOverflow.ellipsis,
  //                 //         fontWeight: FontWeight.w500,
  //                 //         color: AppColor.neutral200,
  //                 //       ),
  //                 //     ),
  //                 //     Text(
  //                 //       '(${Utils.convertSzaboToU2U((connectionHistoryData.ratePerKb??0).toDouble()) ?? '...'} U2U / GB)',
  //                 //       textAlign: TextAlign.center,
  //                 //       overflow: TextOverflow.ellipsis,
  //                 //       style: const TextStyle(
  //                 //         fontSize: 14,
  //                 //         fontWeight: FontWeight.w500,
  //                 //         color: Color(0xFFA7A4A4),
  //                 //       ),
  //                 //     ),
  //                 //   ],
  //                 // ),
  //                 Text(
  //                   connectionHistoryData.getStarTime,
  //                   textAlign: TextAlign.center,
  //                   style: const TextStyle(
  //                     fontSize: 14,
  //                     fontWeight: FontWeight.w500,
  //                     color: Color(0xFF767695),
  //                   ),
  //                 ),
  //                 const SizedBox(
  //                   height: 8,
  //                 ),
  //                 Container(
  //                   padding: const EdgeInsets.all(6),
  //                   decoration: BoxDecoration(
  //                     borderRadius: BorderRadius.circular(6),
  //                     color: const Color(0xFF2D2C3B),
  //                   ),
  //                   child: GradientText(
  //                     connectionHistoryData.getStatus.tr,
  //                     style: const TextStyle(
  //                         fontSize: 12, fontWeight: FontWeight.w500),
  //                     gradientType: GradientType.linear,
  //                     gradientDirection: GradientDirection.rtl,
  //                     colors: connectionHistoryData.getColorStatus,
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),
  //           SvgPicture.asset(
  //             AppImages.icon_arrow_left,
  //             width: 26,
  //           )
  //         ],
  //       ),
  //     ),
  //   );
  // }

  // Widget modalInforConnection(
  //     ConnectionHistoryModel connectionHistoryData, BuildContext context) {
  //   return Padding(
  //     padding: const EdgeInsets.all(16.0),
  //     child: Column(
  //       children: [
  //         Expanded(
  //           child: SingleChildScrollView(
  //             child: Column(
  //               children: [
  //                 const SizedBox(
  //                   height: 24,
  //                 ),
  //                 Image.asset(
  //                   connectionHistoryData.getImage,
  //                   width: 104,
  //                 ),
  //                 const SizedBox(
  //                   height: 24,
  //                 ),
  //                 RichText(
  //                   text: TextSpan(
  //                     text: connectionHistoryData.getsharedBandwidth,
  //                     style: const TextStyle(
  //                         fontSize: 32,
  //                         fontWeight: FontWeight.w500,
  //                         color: Color(0xffFFFFFF)),
  //                     // children: <TextSpan>[
  //                     //   TextSpan(
  //                     //     text: '/ 12.01 GB',
  //                     //     style: TextStyle(fontSize: 32, fontWeight: FontWeight.w500, color: Color(0xff65636F)),
  //                     //   ),
  //                     // ],
  //                   ),
  //                 ),
  //                 Text(
  //                   connectionHistoryData.status == 'Rejected'
  //                       ? 'Request Shared Bandwidth'
  //                       : LocaleKeys.shared_bandwidth.tr,
  //                   textAlign: TextAlign.center,
  //                   style: const TextStyle(
  //                     fontSize: 14,
  //                     fontWeight: FontWeight.w500,
  //                     color: Color(0xFF65636F),
  //                   ),
  //                 ),
  //                 const SizedBox(
  //                   height: 24,
  //                 ),
  //                 cellText(
  //                   LocaleKeys.session_hash.tr,
  //                   Row(
  //                     children: [
  //                       Text(
  //                         Utils.shortenUsername(
  //                             connectionHistoryData.session_hash.toString(), 4,
  //                             showlastString: true),
  //                         textAlign: TextAlign.center,
  //                         style: const TextStyle(
  //                           fontSize: 16,
  //                           fontWeight: FontWeight.w500,
  //                           color: Color(0xFFDEDEDE),
  //                         ),
  //                       ),
  //                       const Icon(
  //                         Icons.copy,
  //                         color: AppColor.neutral300,
  //                         size: 15,
  //                       ).paddingLeft(5)
  //                     ],
  //                   ).onTap(() async {
  //                     Clipboard.setData(ClipboardData(
  //                             text: connectionHistoryData.session_hash
  //                                 .toString()))
  //                         .then((value) {
  //                       Utils.showSnackbar(
  //                           context, LocaleKeys.copied_successfully.tr);
  //                     });
  //                   }),
  //                 ).paddingBottom(16),
  //                 if (connectionHistoryData.handshakeAt != null)
  //                   cellText(
  //                     LocaleKeys.start_in.tr,
  //                     Text(
  //                       connectionHistoryData.getStarTime,
  //                       textAlign: TextAlign.center,
  //                       style: const TextStyle(
  //                         fontSize: 14,
  //                         fontWeight: FontWeight.w500,
  //                         color: Color(0xFFDEDEDE),
  //                       ),
  //                     ),
  //                   ).paddingOnly(bottom: 16),
  //                 if (connectionHistoryData.endAt != null)
  //                   cellText(
  //                     LocaleKeys.end_in.tr,
  //                     Text(
  //                       connectionHistoryData.getEndTime,
  //                       textAlign: TextAlign.center,
  //                       style: const TextStyle(
  //                         fontSize: 14,
  //                         fontWeight: FontWeight.w500,
  //                         color: Color(0xFFDEDEDE),
  //                       ),
  //                     ),
  //                   ).paddingOnly(bottom: 16),
  //                 if (connectionHistoryData.handshakeAt != null)
  //                   cellText(
  //                     LocaleKeys.duration.tr,
  //                     Text(
  //                       connectionHistoryData.getDuration,
  //                       textAlign: TextAlign.center,
  //                       style: const TextStyle(
  //                         fontSize: 14,
  //                         fontWeight: FontWeight.w500,
  //                         color: Color(0xFFDEDEDE),
  //                       ),
  //                     ),
  //                   ).paddingOnly(bottom: 16),

  //                 if (connectionHistoryData.ratePerSecond != null)
  //                   cellText(
  //                     LocaleKeys.selling_price_per_hour.tr,
  //                     Row(
  //                       children: [
  //                         ConstrainedBox(
  //                           constraints:
  //                               BoxConstraints(maxWidth: Get.width * 0.25),
  //                           child: Text(
  //                             connectionHistoryData.getRatePerHour,
  //                             maxLines: 1,
  //                             overflow: TextOverflow.ellipsis,
  //                             textAlign: TextAlign.right,
  //                             style: const TextStyle(
  //                               fontSize: 14,
  //                               fontWeight: FontWeight.w500,
  //                               color: Color(0xFFDEDEDE),
  //                             ),
  //                           ),
  //                         ),
  //                         const SizedBox(
  //                           width: 4,
  //                         ),
  //                         Image.asset(
  //                           AppImages.logo,
  //                           width: 20,
  //                         ),
  //                         Text(
  //                           ' /${LocaleKeys.hour.tr}',
  //                           textAlign: TextAlign.center,
  //                           style: const TextStyle(
  //                             fontSize: 14,
  //                             fontWeight: FontWeight.w500,
  //                             color: Color(0xFFDEDEDE),
  //                           ),
  //                         ),
  //                       ],
  //                     ),
  //                   ).paddingOnly(bottom: 16),
  //                 if (connectionHistoryData.ratePerKb != null)
  //                   cellText(
  //                     LocaleKeys.selling_price_per_gb.tr,
  //                     Row(
  //                       children: [
  //                         ConstrainedBox(
  //                           constraints:
  //                               BoxConstraints(maxWidth: Get.width * 0.25),
  //                           child: Text(
  //                             connectionHistoryData.getRatePerGb,
  //                             maxLines: 1,
  //                             overflow: TextOverflow.ellipsis,
  //                             textAlign: TextAlign.right,
  //                             style: const TextStyle(
  //                               fontSize: 14,
  //                               fontWeight: FontWeight.w500,
  //                               color: Color(0xFFDEDEDE),
  //                             ),
  //                           ),
  //                         ),
  //                         const SizedBox(
  //                           width: 4,
  //                         ),
  //                         Image.asset(
  //                           AppImages.logo,
  //                           width: 20,
  //                         ),
  //                         const Text(
  //                           ' /GB',
  //                           textAlign: TextAlign.center,
  //                           style: TextStyle(
  //                             fontSize: 14,
  //                             fontWeight: FontWeight.w500,
  //                             color: Color(0xFFDEDEDE),
  //                           ),
  //                         ),
  //                       ],
  //                     ),
  //                   ).paddingOnly(bottom: 16),

  //                 if (connectionHistoryData.status != null)
  //                   cellText(
  //                     LocaleKeys.status.tr,
  //                     Container(
  //                       padding: const EdgeInsets.all(6),
  //                       decoration: BoxDecoration(
  //                         borderRadius: BorderRadius.circular(6),
  //                         color: const Color(0xFF2D2C3B),
  //                       ),
  //                       child: GradientText(
  //                         connectionHistoryData.getStatus.tr,
  //                         style: const TextStyle(
  //                             fontSize: 12, fontWeight: FontWeight.w500),
  //                         gradientType: GradientType.linear,
  //                         gradientDirection: GradientDirection.rtl,
  //                         colors: connectionHistoryData.getColorStatus,
  //                       ),
  //                     ),
  //                   ).paddingOnly(bottom: 16),
  //                 if (connectionHistoryData.totalFee != null)
  //                   Obx(() => Column(
  //                         children: [
  //                           cellText(
  //                             LocaleKeys.gross_amount.tr,
  //                             Row(
  //                               children: [
  //                                 ConstrainedBox(
  //                                   constraints: BoxConstraints(
  //                                       maxWidth: Get.width * 0.25),
  //                                   child: Text(
  //                                     connectionHistoryData.getTotalFee
  //                                         .toString(),
  //                                     textAlign: TextAlign.right,
  //                                     overflow: TextOverflow.ellipsis,
  //                                     style: const TextStyle(
  //                                       fontSize: 14,
  //                                       fontWeight: FontWeight.w500,
  //                                       color: Color(0xFFDEDEDE),
  //                                     ),
  //                                   ),
  //                                 ),
  //                                 const SizedBox(
  //                                   width: 4,
  //                                 ),
  //                                 Image.asset(
  //                                   AppImages.logo,
  //                                   width: 20,
  //                                 ),
  //                                 Icon(
  //                                   controller.showTotal.value
  //                                       ? Icons.keyboard_arrow_up
  //                                       : Icons.keyboard_arrow_down,
  //                                   color: AppColor.neutral300,
  //                                 )
  //                               ],
  //                             ),
  //                           ).onTap(() {
  //                             controller.showTotal.value =
  //                                 !controller.showTotal.value;
  //                           }).paddingOnly(bottom: 8),
  //                           if (controller.showTotal.value) ...[
  //                             cellText(
  //                               LocaleKeys.net_amount.tr,
  //                               Row(
  //                                 children: [
  //                                   ConstrainedBox(
  //                                     constraints: BoxConstraints(
  //                                         maxWidth: Get.width * 0.25),
  //                                     child: Text(
  //                                       connectionHistoryData.getNetAmount(
  //                                           controller
  //                                                   .appController
  //                                                   .userDetailModel
  //                                                   .value
  //                                                   .userTier
  //                                                   ?.tier ??
  //                                               ""),
  //                                       textAlign: TextAlign.right,
  //                                       overflow: TextOverflow.ellipsis,
  //                                       style: const TextStyle(
  //                                         fontSize: 14,
  //                                         fontWeight: FontWeight.w500,
  //                                         color: Color(0xFFDEDEDE),
  //                                       ),
  //                                     ),
  //                                   ),
  //                                   const SizedBox(
  //                                     width: 4,
  //                                   ),
  //                                   Image.asset(
  //                                     AppImages.logo,
  //                                     width: 20,
  //                                   ),
  //                                 ],
  //                               ),
  //                             ).paddingAll(8),
  //                             cellText(
  //                               LocaleKeys.platform_fee.tr,
  //                               Row(
  //                                 children: [
  //                                   ConstrainedBox(
  //                                     constraints: BoxConstraints(
  //                                         maxWidth: Get.width * 0.25),
  //                                     child: Text(
  //                                       connectionHistoryData.getPlatformFee(
  //                                           controller
  //                                                   .appController
  //                                                   .userDetailModel
  //                                                   .value
  //                                                   .userTier
  //                                                   ?.tier ??
  //                                               ""),
  //                                       textAlign: TextAlign.right,
  //                                       overflow: TextOverflow.ellipsis,
  //                                       style: const TextStyle(
  //                                         fontSize: 14,
  //                                         fontWeight: FontWeight.w500,
  //                                         color: Color(0xFFDEDEDE),
  //                                       ),
  //                                     ),
  //                                   ),
  //                                   const SizedBox(
  //                                     width: 4,
  //                                   ),
  //                                   Image.asset(
  //                                     AppImages.logo,
  //                                     width: 20,
  //                                   ),
  //                                 ],
  //                               ),
  //                             ).paddingAll(8),
  //                             cellText(
  //                               LocaleKeys.referral_fee.tr,
  //                               Row(
  //                                 children: [
  //                                   ConstrainedBox(
  //                                     constraints: BoxConstraints(
  //                                         maxWidth: Get.width * 0.25),
  //                                     child: Text(
  //                                       connectionHistoryData.getReferralFee(
  //                                           controller
  //                                                   .appController
  //                                                   .userDetailModel
  //                                                   .value
  //                                                   .userTier
  //                                                   ?.tier ??
  //                                               ""),
  //                                       textAlign: TextAlign.right,
  //                                       overflow: TextOverflow.ellipsis,
  //                                       style: const TextStyle(
  //                                         fontSize: 14,
  //                                         fontWeight: FontWeight.w500,
  //                                         color: Color(0xFFDEDEDE),
  //                                       ),
  //                                     ),
  //                                   ),
  //                                   const SizedBox(
  //                                     width: 4,
  //                                   ),
  //                                   Image.asset(
  //                                     AppImages.logo,
  //                                     width: 20,
  //                                   ),
  //                                 ],
  //                               ),
  //                             ).paddingAll(8)
  //                           ]
  //                         ],
  //                       )),
  //                 //const Spacer(),
  //               ],
  //             ),
  //           ),
  //         ),
  //         CostomRaisedButtom(
  //           name: LocaleKeys.close.tr,
  //           function: () {
  //             Get.back();
  //             // ProgressDialog.showDialogNotification(
  //             //   content: dialogCustomerCloseConnection(),
  //             //   saveTitle: 'Claim Rewards',
  //             //   canTitle: 'Close',
  //             //   onPressed: () {},
  //             //   isRow: false,
  //             //   isSwitch: true,
  //             // );
  //           },
  //           colorText: Colors.white,
  //           colorsGradient: const [Color(0xFF2D2C3B), Color(0xFF2D2C3B)],
  //         )
  //       ],
  //     ),
  //   );
  // }

  // Widget infoTotal() {
  //   return Obx(() => Container(
  //         padding: const EdgeInsets.all(16),
  //         decoration: BoxDecoration(
  //           borderRadius: BorderRadius.circular(10),
  //           border: Border.all(color: AppColor.neutral500, width: 1),
  //         ),
  //         child: Row(
  //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //           crossAxisAlignment: CrossAxisAlignment.center,
  //           children: [
  //             // itemTotal(
  //             //     icon: AppImages.clock,
  //             //     title: LocaleKeys.tapPoint.tr,
  //             //     value: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
  //             //       Obx(() => CustomText(
  //             //             text: controller.rewardsController.userDetailModel.value.userTier?.getPoint,
  //             //             overflow: TextOverflow.ellipsis,
  //             //             fontSize: 16,
  //             //           )),
  //             //     ])),
  //             itemTotal(
  //                 icon: AppImages.inactive_price_bandwith,
  //                 title: '${LocaleKeys.price.tr} / GB',
  //                 value: Row(
  //                     mainAxisAlignment: MainAxisAlignment.center,
  //                     children: [
  //                       Obx(() => CustomText(
  //                             text: controller
  //                                     .bandwidthPriceData.value.ratePerKb ??
  //                                 '0',
  //                             overflow: TextOverflow.ellipsis,
  //                             fontSize: 16,
  //                           )),
  //                       Image.asset(
  //                         AppImages.logo,
  //                         width: 16,
  //                       ).paddingOnly(left: 6),
  //                     ])),
  //             itemTotal(
  //                 icon: AppImages.inactive_bandwidth_connection,
  //                 title: LocaleKeys.bandwidth.tr,
  //                 value: Utils.kbToGb(controller
  //                         .connectionOverviewModel.value.totalBandwidthUsages ??
  //                     0),
  //                 valueShow: Utils.kbToGb(controller
  //                         .connectionOverviewModel.value.totalBandwidthUsages ??
  //                     0)),
  //             itemTotal(
  //                 icon: AppImages.inactive_rewards_connection,
  //                 title: LocaleKeys.rewards.tr,
  //                 value: controller
  //                     .connectionOverviewModel.value.getTotalRewardsKMB
  //                     .toString(),
  //                 valueShow: controller
  //                     .connectionOverviewModel.value.getTotalRewards
  //                     .toString(),
  //                 showIcon: true),
  //           ],
  //         ),
  //       )).paddingSymmetric(vertical: 10, horizontal: 6);
  // }

  // Widget itemTotal(
  //     {required String icon,
  //     String? title,
  //     dynamic value,
  //     dynamic valueShow,
  //     bool showIcon = false}) {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.center,
  //     children: [
  //       Image.asset(
  //         icon,
  //         width: 24,
  //         color: Colors.white,
  //       ),
  //       Row(
  //         children: [
  //           CustomText(
  //             text: title,
  //             fontSize: 12,
  //             color: AppColor.neutral300,
  //             textAlign: TextAlign.center,
  //           ).paddingSymmetric(vertical: 5),
  //       title!.contains('Price')?   SvgPicture.asset(
  //             AppImages.icon_warning,
  //             width: 16,
  //           ).onTap(() {
  //             infoPrice(Get.context!);
  //           }).paddingLeft(6):const SizedBox(),
  //         ],
  //       ),
  //       valueShow is String
  //           ? CustomPopupInfo(
  //               value: valueShow,
  //               child: Row(
  //                 mainAxisAlignment: MainAxisAlignment.center,
  //                 children: [
  //                   ConstrainedBox(
  //                     constraints: BoxConstraints(maxWidth: Get.width * 0.25),
  //                     child: Text(
  //                       value,
  //                       overflow: TextOverflow.ellipsis,
  //                       textAlign: TextAlign.right,
  //                       style: const TextStyle(
  //                           fontSize: 16,
  //                           fontWeight: FontWeight.w500,
  //                           color: Colors.white),
  //                     ),
  //                   ),
  //                   if (showIcon)
  //                     Image.asset(
  //                       AppImages.logo,
  //                       width: 20,
  //                     ).paddingOnly(left: 6),
  //                 ],
  //               ))
  //           : value,
  //     ],
  //   );
  // }

  // void infoPrice(BuildContext context) {
  //   return CustomBottomSheet.showModalNotFullScreen(
  //     context,
  //     Padding(
  //       padding: const EdgeInsets.all(16.0),
  //       child: Column(
  //         children: [
  //           Row(
  //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //             children: [
  //               ConstrainedBox(
  //                 constraints: BoxConstraints(maxWidth: Get.width * 0.6),
  //                 child: Text(
  //                     LocaleKeys.bandwidth_price.tr,

  //                     overflow: TextOverflow.ellipsis,
  //                     style: const TextStyle(
  //                       fontSize: 20,
  //                       fontWeight: FontWeight.w500,
  //                       color: Color(0xFFFFFFFF),
  //                     )),
  //               ),
  //               InkWell(
  //                 onTap: () => Navigator.pop(context),
  //                 child: SvgPicture.asset(AppImages.icon_close),
  //               )
  //             ],
  //           ),
  //           const SizedBox(
  //             height: 16,
  //           ),
  //           Image.asset(
  //             AppImages.infoPrice,

  //           ).paddingSymmetric(vertical: 32),
  //           CustomText(
  //             text: LocaleKeys.each_country_has_a_different.tr,
  //             fontSize: 14,
  //             fontWeight: FontWeight.w500,
  //             color: AppColor.neutral300,
  //           ).paddingBottom(40)
  //         ],
  //       ),
  //     ),
  //     //   height: MediaQuery.of(context).size.height * 0.9,
  //   );
  // }
  // Widget dialogCustomerCloseConnection() {
  //   return Column(
  //     children: [
  //       const SizedBox(
  //         height: 24,
  //       ),
  //       SvgPicture.asset(AppImages.icon_customer_close_connection),
  //       const SizedBox(
  //         height: 24,
  //       ),
  //       RichText(
  //         text: const TextSpan(
  //           text: '8 GB',
  //           style: TextStyle(fontSize: 32, fontWeight: FontWeight.w500, color: Color(0xffFFFFFF)),
  //           children: <TextSpan>[
  //             TextSpan(
  //               text: '/ 12.01 GB',
  //               style: TextStyle(fontSize: 32, fontWeight: FontWeight.w500, color: Color(0xff65636F)),
  //             ),
  //           ],
  //         ),
  //       ),
  //       const Text(
  //         'Shared Bandwidth',
  //         textAlign: TextAlign.center,
  //         style: TextStyle(
  //           fontSize: 14,
  //           fontWeight: FontWeight.w500,
  //           color: Color(0xFF65636F),
  //         ),
  //       ),
  //       const SizedBox(
  //         height: 24,
  //       ),
  //       cellText(
  //         'Approve in',
  //         const Text(
  //           '12/01/2023 - 12:30',
  //           textAlign: TextAlign.center,
  //           style: TextStyle(
  //             fontSize: 14,
  //             fontWeight: FontWeight.w500,
  //             color: Color(0xFFDEDEDE),
  //           ),
  //         ),
  //       ),
  //       const SizedBox(
  //         height: 16,
  //       ),
  //       cellText(
  //         'Time close',
  //         const Text(
  //           '12/01/2023 - 12:30',
  //           textAlign: TextAlign.center,
  //           style: TextStyle(
  //             fontSize: 14,
  //             fontWeight: FontWeight.w500,
  //             color: Color(0xFFDEDEDE),
  //           ),
  //         ),
  //       ),
  //       const SizedBox(
  //         height: 16,
  //       ),
  //       cellText(
  //           'Total rewards',
  //           Row(
  //             children: [
  //               const Text(
  //                 '493.21 U2DPN',
  //                 textAlign: TextAlign.center,
  //                 maxLines: 2,
  //                 style: TextStyle(
  //                   fontSize: 14,
  //                   fontWeight: FontWeight.w500,
  //                   color: Color(0xFFDEDEDE),
  //                 ),
  //               ),
  //               const SizedBox(
  //                 width: 4,
  //               ),
  //               Image.asset(
  //                 AppImages.logo,
  //                 width: 20,
  //               ),
  //             ],
  //           ),
  //           icon: Icons.info),
  //       const SizedBox(
  //         height: 16,
  //       ),
  //     ],
  //   );
  // }
}
