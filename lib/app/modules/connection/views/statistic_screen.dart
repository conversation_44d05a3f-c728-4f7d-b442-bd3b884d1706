import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/custom_appbar.dart';
import 'package:u2u_dpn/widget/custom_popup_info.dart';
import 'package:u2u_dpn/widget/custom_raisebutton.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

import '../../../../widget/ultil.dart';

class StatisticConnection extends GetView<ConnectionController> {
  const StatisticConnection({super.key});

  @override
  Widget build(BuildContext context) {
  
    return Scaffold(
      backgroundColor: AppColor.neutral800,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          backIcon(),
          CustomText(
            text: LocaleKeys.statistic.tr,
            fontSize: 32,
            textAlign: TextAlign.left,
          ).paddingSymmetric(vertical: 15),
          inforCard(context),
          CostomRaisedButtom(
              name: LocaleKeys.go_to_account.tr,
              function: () {
                controller.dashboardController.selectIndex.value = SettingConfig.showsTask ? 2 : 2;
                Get.back();
              }).paddingTop(30),
          // CostomRaisedButtom(
          //   name: 'How to claim rewards',
          //   colorText: Colors.white,
          //   colorsGradient: const [Color(0xFF2D2C3B), Color(0xFF2D2C3B)],
          //   function: () {},
          // ).paddingTop(20)
        ],
      ).paddingAll(16).paddingTop(50),
    );
  }

  Widget inforCard(BuildContext context) {
    return Obx(() => Container(
          padding: const EdgeInsets.only(left: 8.0, right: 8, bottom: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            color: AppColor.neutral900,
          ),
          child: Column(
            children: [
              //  referralCode(context),
              inforTotal().paddingOnly(top: 8, bottom: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: Get.width * 0.4),
                      child: Text(
                        LocaleKeys.total_bandwidth_shared.tr,
                        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Color(0xFF65636F)),
                      ),
                    ),
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: Get.width * 0.3),
                      child: Text(
                        Utils.kbToGb(controller.connectionOverviewModel.value.totalBandwidthUsages ?? 0),
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: AppColor.neutral200),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: Get.width * 0.4),
                      child: Text(LocaleKeys.rewards_claimable.tr,
                          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Color(0xFF13D4A3))),
                    ),
                    Row(
                      children: [
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: Get.width * 0.25),
                          child: Text(
                            controller.connectionOverviewModel.value.getTotalRewards.toString(),
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.right,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: AppColor.neutral400,
                            ),
                          ),
                        ),
                        Image.asset(
                          AppImages.logo,
                          width: 20,
                        ).paddingOnly(left: 6),
                      ],
                    )
                  ],
                ),
              ),
              // Padding(
              //   padding: EdgeInsets.symmetric(horizontal: 12),
              //   child: Row(s
              //     children: [
              //       const Spacer(),
              //       InkWell(
              //         onTap: () {
              //           controller.getConnectionHistory();
              //           Get.toNamed(Routes.CONNECTION_HISTORY);
              //         },
              //         child: Container(
              //           padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              //           decoration: BoxDecoration(
              //             borderRadius: BorderRadius.circular(24),
              //             color: AppColor.borderContainerDarkmMode,
              //           ),
              //           child: Text('Connection History', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: AppColor.neutral200)),
              //         ),
              //       )
              //     ],
              //   ),
              // ),
            ],
          ),
        ));
  }

  // Widget referralCode(BuildContext context) {
  //   return Container(
  //     padding: const EdgeInsets.only(left: 14, right: 14, top: 14, bottom: 14),
  //     decoration: BoxDecoration(
  //       border: Border.all(
  //         color: AppColor.neutral700,
  //         width: 1,
  //       ),
  //       borderRadius: BorderRadius.circular(16),
  //       color: AppColor.neutral800,
  //     ),
  //     child: Row(
  //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //       children: [
  //         Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Text('Bandwidth price', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: AppColor.neutral400)),
  //             SizedBox(
  //               height: 4 ,
  //             ),
  //             Row(
  //               children: [
  //                 CustomText(
  //                   text: controller.bandwidthPriceData.value.ratePerKb ?? '0',
  //                   overflow: TextOverflow.ellipsis,
  //                   fontSize: 20,
  //                 ),
  //                 Image.asset(
  //                   AppImages.logo,
  //                   width: 20,
  //                 ).paddingOnly(left: 6)
  //               ],
  //             )
  //           ],
  //         ),

  //         // Showcase.withWidget(
  //         //   key: one,
  //         //   height: 130 ,
  //         //   width: 270,
  //         //   container: buildshowCase(context, 'To start sharing bandwidth, set your desired bandwidth sharing rate.', () {
  //         //     ShowCaseWidget.of(context).next();
  //         //   }, number: '1'),
  //         //   child:
  //         // InkWell(
  //         //   onTap: () {
  //         //     controller.controllerTextField.value.text = '';
  //         //     controller.changeHintButton();
  //         //     controller.getSuggestBandwidth();
  //         //     // editPrice(context);
  //         //   },
  //         //   child: Container(
  //         //     padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8 ),
  //         //     decoration: BoxDecoration(
  //         //       borderRadius: BorderRadius.circular(24),
  //         //       // gradient: AppColor.customLineGradientGreen
  //         //       color: AppColor.neutral600,
  //         //     ),
  //         //     child: Text('Edit Price',
  //         //         style: TextStyle(
  //         //           fontSize: 14,
  //         //           fontWeight: FontWeight.w500,
  //         //           color: AppColor.neutral200,
  //         //         )),
  //         //   ),
  //         // ),
  //         //),
  //         // InkWell(
  //         //   onTap: () {
  //         //     controller.controllerTextField.value.text = '';
  //         //     controller.changeHintButton();
  //         //     controller.getSuggestBandwidth();
  //         //     editPrice(context);
  //         //   },
  //         //   child: Container(
  //         //     padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8 ),
  //         //     decoration: BoxDecoration(
  //         //       borderRadius: BorderRadius.circular(24),
  //         //       // gradient: AppColor.customLineGradientGreen
  //         //       color: AppColor.neutral600,
  //         //     ),
  //         //     child: Text('Edit Price',
  //         //         style: TextStyle(
  //         //           fontSize: 14,
  //         //           fontWeight: FontWeight.w500,
  //         //           color: AppColor.neutral200,
  //         //         )),
  //         //   ),
  //         // ),
  //       ],
  //     ),
  //   );
  // }

  Widget inforTotal() {
    return Container(
      padding: const EdgeInsets.only(left: 14, right: 14, top: 14, bottom: 14),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColor.neutral700,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(16),
        color: AppColor.neutral800,
      ),
      child: IntrinsicHeight(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  AppImages.icon_total_referees,
                  width: 18,
                  fit: BoxFit.contain,
                ),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: Get.width * 0.35),
                  child: Text(LocaleKeys.total_sessions.tr,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppColor.neutral400,
                      )).paddingSymmetric(vertical: 2),
                ),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: Get.width * 0.25),
                  child: Text(
                    (controller.connectionOverviewModel.value.totalRewards ?? 0).toString(),
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: AppColor.neutral200,
                    ),
                  ),
                ),
              ],
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: VerticalDivider(
                thickness: 1,
                color: AppColor.neutral400,
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  AppImages.icon_total_commission,
                  width: 18,
                  fit: BoxFit.contain,
                ),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: Get.width * 0.35),
                  child: Text(LocaleKeys.total_rewards.tr,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppColor.neutral400,
                      )).paddingSymmetric(vertical: 2),
                ),
                Row(
                  children: [
                    ConstrainedBox(
                        constraints: BoxConstraints(maxWidth: Get.width * 0.25),
                        child: CustomPopupInfo(
                          value: controller.connectionOverviewModel.value.getTotalRewards.toString(),
                          child: CustomText(
                            text: controller.connectionOverviewModel.value.getTotalRewards.toString(),
                            overflow: TextOverflow.ellipsis,
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                            color: AppColor.neutral200,
                          ),
                        )),
                    Image.asset(
                      AppImages.logo,
                      width: 22,
                    ).paddingOnly(left: 6),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
