// import 'package:auto_size_text_field/auto_size_text_field.dart';
// ignore_for_file: deprecated_member_use, use_build_context_synchronously

import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:u2u_dpn/app/modules/connection/views/custom_keyboard.dart';
import 'package:u2u_dpn/app_state.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/build_show_case.dart';
import 'package:u2u_dpn/widget/dialog/process_dialog.dart';
import 'package:u2u_dpn/widget/widget.dart';

import '../controllers/connection_controller.dart';

class EditPrice extends StatelessWidget {
  const EditPrice({super.key});

  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
      disableBarrierInteraction: true,
      builder: (context) => const EditPriceView(),
    );
  }
}

class EditPriceView extends StatefulWidget {
  const EditPriceView({Key? key}) : super(key: key);

  @override
  State<EditPriceView> createState() => _EditPriceViewState();
}

class _EditPriceViewState extends State<EditPriceView> {
  final controller = Get.find<ConnectionController>();

  final GlobalKey one = GlobalKey();
  final GlobalKey two = GlobalKey();
  final GlobalKey three = GlobalKey();

  @override
  void initState() {
    if (AppState.instance.settingBox.read(SettingType.isStartEditPrice.toString()) == null ||
        AppState.instance.settingBox.read(SettingType.isStartEditPrice.toString()) == true) {
      AppState.instance.settingBox.write(SettingType.isStartEditPrice.toString(), false);
      ambiguate(WidgetsBinding.instance)?.addPostFrameCallback(
        (_) => Future.delayed(const Duration(milliseconds: 100), () {
          ShowCaseWidget.of(context).startShowCase([
            one,
            two,
            three,
          ]);
        }),
      );
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: Get.width * 0.8),
                  child: Text(LocaleKeys.edit_price.tr, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: Color(0xFFFFFFFF)))),
              InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppImages.icon_close),
              )
            ],
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      Showcase.withWidget(
                        key: one,
                        height: 150,
                        width: 280,
                        container: buildshowCase(
                            context, '''Enter your desired bandwidth sharing rate. Higher rates offer better customer referral ratios.''', () {
                          ShowCaseWidget.of(context).next();
                        }, number: '1'),
                        child: Obx(() => TextFormField(
                              autofocus: true,
                              showCursor: true,
                              readOnly: true,
                              controller: controller.controllerTextField.value,
                              cursorColor: AppColor.primaryGreen500,
                              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: white),
                              maxLines: null,
                              onChanged: (value) {
                                controller.changeHintButton();
                              },
                              decoration: InputDecoration(
                                hintText: '0.00',
                                counterText: '',
                                hintStyle: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: AppColor.neutral500),
                                enabledBorder: const OutlineInputBorder(
                                  borderRadius: BorderRadius.all(Radius.circular(8)),
                                  borderSide: BorderSide(color: AppColor.neutralBlack, width: 0.6),
                                ),
                                focusedBorder: const OutlineInputBorder(
                                  borderRadius: BorderRadius.all(Radius.circular(8)),
                                  borderSide: BorderSide(color: AppColor.neutralBlack),
                                ),
                                contentPadding: const EdgeInsets.symmetric(vertical: 17, horizontal: 24),
                                filled: true,
                                fillColor: AppColor.neutralBlack,
                                suffixIcon: SizedBox(
                                  width: Get.width * 0.3,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Image.asset(
                                        AppImages.logo,
                                        width: 20,
                                      ),
                                      const CustomText(
                                        text: ' / GB',
                                        fontSize: 20,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ],
                                  ).paddingOnly(right: 16),
                                ),
                              ),
                            ).paddingSymmetric(vertical: 16)),
                      ),

                      // AutoSizeTextField(
                      //   controller: controller.controllerTextField.value,
                      //   cursorColor: const Color(0xFFFFFFFF),
                      //   style: const TextStyle(fontSize: 48, fontWeight: FontWeight.w500, color: Color(0xFFFFFFFF)),
                      //   decoration: InputDecoration(
                      //     hintText: '0.0',
                      //     hintStyle: TextStyle(fontSize: 48, fontWeight: FontWeight.w500, color: Color(0xFF383846)),
                      //     border: InputBorder.none,
                      //     focusedBorder: InputBorder.none,
                      //     enabledBorder: InputBorder.none,
                      //     errorBorder: InputBorder.none,
                      //     disabledBorder: InputBorder.none,
                      //     contentPadding: EdgeInsets.only(top: 25, bottom: 25, right: 45),
                      //     suffixIcon: Image.asset(
                      //       AppImages.logo,
                      //       width: 32,
                      //     ),
                      //   ),
                      //   fullwidth: false,
                      //   readOnly: true,
                      //   autofocus: true,
                      //   showCursor: true,
                      //   minFontSize: 24,
                      //   minWidth: 130,
                      //   //textAlign: TextAlign.center,
                      // ),

                      // const Text('- Our suggested is ~10 U2U -', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Color(0xFF65636F))),
                      // const Text('This is price 1 GB of bandwidth',
                      //     style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Color(0xFF0DB198))),
                      // const SizedBox(
                      //   height: 12,
                      // ),
                      Showcase.withWidget(
                        key: two,
                        height: 150,
                        width: 270,
                        container: buildshowCase(context, LocaleKeys.the_suggested_bandwidth_sharing_rate.tr, () {
                          ShowCaseWidget.of(context).next();
                        }, number: '2'),
                        child: GestureDetector(
                          onTap: () {
                            controller.controllerTextField.value.text = controller.suggestBandwidth.value;
                            controller.changeHintButton();
                            // logger.d(controller.controllerTextField.value.text);
                          },
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(14),
                            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: AppColor.neutral600),
                            child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(6),
                                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: const Color(0xff383846)),
                                    child: SvgPicture.asset(
                                      AppImages.icon_thunder,
                                      // AppImages.icon_wifi,
                                      width: 16,
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 12,
                                  ),
                                  Text(LocaleKeys.our_suggestion.tr,
                                      style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Colors.white))
                                ],
                              ),
                              Row(
                                children: [
                                  Obx(() => Text(controller.suggestBandwidth.value,
                                      maxLines: 2,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: AppColor.neutral200,
                                      ))),
                                  Image.asset(
                                    AppImages.logo,
                                    width: 20,
                                  ).paddingOnly(left: 6),
                                  const CustomText(
                                    text: ' / GB',
                                    fontWeight: FontWeight.w500,
                                  ),
                                ],
                              ),
                            ]),
                          ),
                        ),
                      ),

                      const SizedBox(
                        height: 12,
                      ),
                      SizedBox(
                        width: Get.width - 32,
                        // child: const Row(
                        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        //   children: [
                        // Icon(
                        //   Icons.brightness_1,
                        //   size: 4,
                        //   color: Color(0xFF65636F),
                        // ),
                        // SizedBox(
                        //   width: 16,
                        // ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomText(
                              text: LocaleKeys.the_cost_for_each.tr,
                              color: AppColor.neutral400,
                              textHeight: 1.5,
                            ),
                            Column(
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const CustomText(
                                      text: '1.  ',
                                      color: AppColor.neutral400,
                                      textHeight: 1.5,
                                    ),
                                    Expanded(
                                      child: CustomText(
                                        text: LocaleKeys.time_based_fee.tr,
                                        color: AppColor.neutral400,
                                        textHeight: 1.5,
                                      ),
                                    )
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const CustomText(
                                      text: '2. ',
                                      color: AppColor.neutral400,
                                      textHeight: 1.5,
                                    ),
                                    Expanded(
                                        child: CustomText(
                                      text: LocaleKeys.shared_bandwidth_fee.tr,
                                      textHeight: 1.5,
                                      color: AppColor.neutral400,
                                    ))
                                  ],
                                )
                              ],
                            ).paddingOnly(left: 5)
                          ],
                        ),
                      ),
                    ],
                  ),
                  //custom keyboard
                ],
              ),
            ),
          ),
          Column(
            children: [
              const SizedBox(
                height: 12,
              ),
              CustomKeyboard(
                onTextInput: (myText) {
                  controller.insertText(myText);
                  controller.changeHintButton();
                },
                onBackspace: () {
                  controller.backspace();
                  controller.changeHintButton();
                },
              ),
              const SizedBox(
                height: 16,
              ),
              Showcase.withWidget(
                key: three,
                height: 150,
                width: 270,
                container: buildshowCase(context, LocaleKeys.click_here_save_sharing_price_information.tr, () {
                  ShowCaseWidget.of(context).dismiss();
                }, number: '3', textButton: LocaleKeys.done.tr),
                child: Obx(() => CostomRaisedButtom(
                      isHintButton: controller.isHintButton.value,
                      name: LocaleKeys.save.tr,
                      function: () {
                        Get.back();
                        ProgressDialog.showDialogNotification(
                          content: dialogSellingPrice(),
                          onPressed: () {
                            Get.back();
                            controller.setBandwidthPrice(context, controller.controllerTextField.value.text.toDouble());

                            // ProgressDialog.showDialogNotification(
                            //   content: dialogPriceWasUpdated(),
                            //   isShowCancel: false,
                            //   onPressed: () {
                            //     Get.back();
                            //     //Get.to(const SharedBandwidth());
                            //     // ProgressDialog.showDialogNotification(
                            //     //   content: dialogConnectionRequest(),
                            //     //   saveTitle: 'Approve',
                            //     //   canTitle: 'Reject',
                            //     //   onPressed: () {
                            //     //     Get.back();
                            //     //     Get.to(const SharedBandwidth());
                            //     //   },
                            //     // );
                            //   },
                            // );
                          },
                        );
                      },
                    )),
              )
            ],
          )
        ],
      ),
    );
  }

  Widget dialogSellingPrice() {
    return Column(
      children: [
        Text(
          LocaleKeys.are_you_sure_wanna_change_selling_price.tr,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color(0xFFFFFFFF),
          ),
        ),
        const SizedBox(
          height: 24,
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
          decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFF65636F),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(16),
              color: const Color(0xff383846)),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                controller.bandwidthPriceData.value.ratePerKb == null ? '0' : controller.bandwidthPriceData.value.ratePerKb.toString(),
                textAlign: TextAlign.center,
                maxLines: 2,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFFA7A4A4),
                ),
              ),
              const SizedBox(
                width: 6,
              ),
              Image.asset(
                AppImages.logo,
                width: 16,
              ),
              const SizedBox(
                width: 6,
              ),
              const Text(
                '/GB',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFFA7A4A4),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 8,
        ),
        const Text(
          '|',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFFA7A4A4),
          ),
        ),
        const SizedBox(
          height: 8,
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
          decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFF0DB198),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(16),
              color: const Color(0xff0DB198).withOpacity(0.2)),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                controller.controllerTextField.value.text.toString(),
                textAlign: TextAlign.center,
                maxLines: 2,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFFFFFFFF),
                ),
              ),
              const SizedBox(
                width: 6,
              ),
              Image.asset(
                AppImages.logo,
                width: 16,
              ),
              const SizedBox(
                width: 6,
              ),
              const Text(
                '/GB',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFFFFFFFF),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget dialogPriceWasUpdated() {
    return Column(
      children: [
        Image.asset(
          AppImages.claim_rewards_done,
          width: 104,
        ),
        const SizedBox(
          height: 16,
        ),
        Text(
          LocaleKeys.price_updated.tr,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color(0xFFFFFFFF),
          ),
        ),
      ],
    );
  }

  Widget dialogConnectionRequest() {
    return Column(
      children: [
        const Text(
          'You have a new connection request',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color(0xFFFFFFFF),
          ),
        ),
        const SizedBox(
          height: 24,
        ),
        Stack(
          children: [
            SvgPicture.asset(
              AppImages.icon_wifi_request,
              height: 96,
            ),
            Positioned(
              top: 35,
              left: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 9, vertical: 3),
                decoration: BoxDecoration(
                    border: Border.all(
                      color: const Color(0xFF0DB198),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    color: const Color(0xff2D2C3B)),
                child: GradientText(
                  '158.21 GB',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  gradientType: GradientType.linear,
                  gradientDirection: GradientDirection.rtl,
                  colors: const [
                    Color(0xFF1AF7A9),
                    AppColor.primaryBlue500,
                  ],
                ),
              ),
            ),
            Positioned(
              top: 35,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 9, vertical: 3),
                decoration: BoxDecoration(
                    border: Border.all(
                      color: const Color(0xFF65636F),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    color: const Color(0xff383846)),
                child: const Text(
                  '00:00:00',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF65636F),
                  ),
                ),
              ),
            ),
          ],
        )
      ],
    );
  }
}
