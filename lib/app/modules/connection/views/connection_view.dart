// ignore_for_file: deprecated_member_use, use_build_context_synchronously

import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background/flutter_background.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
// Removed unused import
import 'package:showcaseview/showcaseview.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/app/modules/notification/controllers/notification_controller.dart';
import 'package:u2u_dpn/app_state.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/socket_app.dart';
import 'package:u2u_dpn/widget/CustomContainerShadow.dart';

import 'package:u2u_dpn/widget/build_show_case.dart';
import 'package:u2u_dpn/widget/dialog/process_dialog.dart';
import 'package:u2u_dpn/widget/ultil.dart';
import 'package:u2u_dpn/widget/widget.dart';
import '../../../../utils/app_utils.dart';
import '../../../routes/app_pages.dart';

class ConnectionView extends StatelessWidget {
  const ConnectionView({super.key});

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      logger.d(jsonEncode({"Authorization": SettingConfig.token}).toString());
    }
    return ShowCaseWidget(
      disableBarrierInteraction: true,
      builder: (context) => const Connection(),
    );
  }
}

class Connection extends StatefulWidget {
  const Connection({Key? key}) : super(key: key);

  @override
  State<Connection> createState() => _ConnectionState();
}

class _ConnectionState extends State<Connection> with RouteAware {
  final controller = Get.find<ConnectionController>();
  final notificationController = Get.put<NotificationController>(NotificationController());

  //bool _enable = false;
  late GlobalKey one;
  late GlobalKey two;
  late GlobalKey three;
  late GlobalKey four;
  late GlobalKey five;
  late GlobalKey six;
  late GlobalKey seven;
  bool isProcessing = false;
  final androidConfig = FlutterBackgroundAndroidConfig(
    notificationTitle: "U2UDPN",
    notificationText: LocaleKeys.connected.tr,
    notificationImportance: AndroidNotificationImportance.max,
    notificationIcon: const AndroidResource(name: 'icon_app'),
  );

  Future<void> getNotificaion() async {
    await notificationController.getNotificaion();
    notificationController.checkPushNotificaion();
  }

  void checkAndStartShowcase() {
    final setting = AppState.instance.settingBox.read(SettingType.isStartConnection.toString());
    if (setting == null || setting == true) {
      AppState.instance.settingBox.write(SettingType.isStartConnection.toString(), false);

      ambiguate(WidgetsBinding.instance)?.addPostFrameCallback((_) {
        if (!mounted) return; // ✅ tránh crash khi widget đã dispose

        final showcase = ShowCaseWidget.of(context);
        // ignore: unnecessary_null_comparison
        if (showcase != null) {
          showcase.startShowCase([
            one,
            two,
            three,
            four,
            five,
            six,
            seven,
          ]);
        }
      });
    }
  }

  @override
  void initState() {
    one = GlobalKey();
    two = GlobalKey();
    three = GlobalKey();
    four = GlobalKey();
    five = GlobalKey();
    six = GlobalKey();
    seven = GlobalKey();
    checkAndStartShowcase();

    if (!kIsWeb) {
      if (Platform.isAndroid) {
        // ignore: unused_local_variable
        FlutterBackground asPermissions;
        FlutterBackground.initialize(androidConfig: androidConfig);
      }
    }

    getNotificaion();
    controller.getloactionBonus();
    controller.getTotalTime();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void didPopNext() {
    // được gọi khi quay lại màn hình này bằng Get.back()
    checkAndStartShowcase();
  }

  @override
  void dispose() {
    // controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //controller.connectionOverview();

   
    return Scaffold(
      // resizeToAvoidBottomInset: false,
      backgroundColor: AppColor.neutral800,
      body: Stack(children: [
        Obx(() {
          return controller.statusConnection.value == StateButtonConnect.start
              ? controller.listActiveConnection.isNotEmpty
                  ? Image.asset(
                      AppImages.background_connection_v2,
                      width: Get.width,
                      fit: BoxFit.cover,
                    )
                  : Image.asset(
                      AppImages.background_connection,
                      width: Get.width,
                      fit: BoxFit.cover,
                    )
              : Image.asset(
                  AppImages.bg_white,
                  width: Get.width,
                  fit: BoxFit.cover,
                );
        }),
        SingleChildScrollView(
          child: Container(
            width: Get.width,
            height: Get.height * 0.9 > 600 ? Get.height * 0.9 : 600,
            padding: const EdgeInsets.only(left: 14, right: 14, top: 40),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                titleDpn(),

                connectionTime(),
                const Spacer(),
                connectionButton(),
                const Spacer(),
                rank(),

                // buttomActive(),
                // const Spacer(),

                // Obx(() => Row(
                //       mainAxisAlignment: MainAxisAlignment.center,
                //       children: [
                //         Checkbox(
                //             activeColor: AppColor.primaryGreen600,
                //             checkColor: Colors.black,
                //             side: const BorderSide(
                //               color: Colors.white,
                //             ),
                //             value: controller.autoConnection.value,
                //             onChanged: (bool? value) {
                //               controller.autoConnection.value = !controller.autoConnection.value;
                //               SettingConfig.autoConnection = controller.autoConnection.value;
                //             }),
                //         CustomText(text: LocaleKeys.automatically_connect.tr).onTap(() {
                //           controller.autoConnection.value = !controller.autoConnection.value;
                //           SettingConfig.autoConnection = controller.autoConnection.value;
                //         })
                //       ],
                //     )),

                const SizedBox(
                  height: 16,
                )
              ],
            ),
          ),
        ),
      ]),
    );
  }

  Widget titleDpn() {
    return Row(
      children: [
        Expanded(
          child: const CustomText(
            text: 'DPN Subnet',
            fontSize: 30,
            fontFamily: 'Mona Sans Expanded',
            fontWeight: FontWeight.w600,
            color: AppColor.neutral100,
          ).paddingOnly(top: 16, bottom: 16),
        ),
        Showcase.withWidget(
          key: one,
          height: 130,
          width: 270,
          container: buildshowCase(context, LocaleKeys.refresh.tr, () {
            ShowCaseWidget.of(context).next();
          }, number: '1', numberEnd: '7'),
          child: SvgPicture.asset(
            AppImages.refresh,
            width: 40,
          ).onTap(() async {
            ProgressDialog.show(context);
            await controller.onRefresh();
            await Future.delayed(const Duration(milliseconds: 500));
            ProgressDialog.hide(context);
          }),
        ),
        const SizedBox(
          width: 10,
        ),
        Showcase.withWidget(
          key: two,
          height: 130,
          width: 270,
          container: buildshowCase(context, LocaleKeys.history_showcase.tr, () {
            ShowCaseWidget.of(context).next();
          }, number: '2', numberEnd: '7'),
          child: SvgPicture.asset(
            AppImages.session,
            width: 40,
          ).onTap(() async {
            Get.toNamed(Routes.CONNECTION_HISTORY);
            // await controller.getOnlineSession();
            await controller.connectionOverview();
            await controller.getConnectionHistory();
            controller.removeDuplicateSessions();
          }),
        ),
        const SizedBox(
          width: 10,
        ),
        Showcase.withWidget(
          key: three,
          height: 130,
          width: 270,
          container: buildshowCase(context, LocaleKeys.setting_showcase.tr, () {
            ShowCaseWidget.of(context).next();
          }, number: '3', numberEnd: '7'),
          child: SvgPicture.asset(
            AppImages.setting,
            width: 40,
          ).onTap(() {
            // Get.to(() => const StatisticConnection());
            Get.toNamed(Routes.SETTING);
          }),
        ),
        const SizedBox(
          width: 10,
        ),
        // Showcase.withWidget(
        //   key: three,
        //   height: 130 ,
        //   width: 270.w,
        //   container: buildshowCase(context, LocaleKeys.statistic_showcase.tr, () {
        //     ShowCaseWidget.of(context).next();
        //   }, number: '2', numberEnd: '5'),
        //child:
        // Image.asset(
        //   AppImages.icon_notification,
        //   width: 40.w,
        // ).onTap(() {
        //   Get.toNamed(Routes.NOTIFICATION);
        // }),
        // ),
      ],
    );
  }

  Widget rank() {
    return Obx(() => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Showcase.withWidget(
                    key: six,
                    height: 150,
                    width: 270,
                    container: buildshowCase(context, LocaleKeys.your_curent_rank_showcase.tr, () {
                      ShowCaseWidget.of(context).next();
                    }, number: '6', numberEnd: '7'),
                    child: Container(
                      padding: const EdgeInsets.only(top: 4, bottom: 4, left: 8, right: 8),
                      decoration: BoxDecoration(
                          gradient: const LinearGradient(
                              colors: [AppColor.neutral700, AppColor.neutral500], begin: Alignment.topCenter, end: Alignment.bottomCenter),
                          borderRadius: BorderRadius.circular(999),
                          border: Border.all(color: AppColor.neutral500)),
                      child: Row(
                        children: [
                          Image.asset(
                            controller.userRank.value.getImageRank,
                            width: 25,
                          ).paddingOnly(right: 4),
                          CustomText(
                            text: controller.userRank.value.tier,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: controller.userRank.value.getColor,
                          )
                        ],
                      ),
                    ).onTap(() {
                      //    Get.toNamed(Routes.MY_TIER);
                    }).paddingBottom(6)),
                Showcase.withWidget(
                    key: seven,
                    height: 180,
                    width: 270,
                    container: buildshowCase(context, LocaleKeys.your_current_point_showcase.tr, () {
                      ShowCaseWidget.of(context).next();
                    }, number: '7', numberEnd: '7'),
                    child: Row(
                      children: [
                        RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: NumberFormat.decimalPattern().format(controller.userRank.value.getPoint).toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                              TextSpan(
                                text: ' / ${controller.userRank.value.getPointNextRank} EXP',
                                style: const TextStyle(
                                  color: Colors.white54,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ).paddingSymmetric(horizontal: 6),
                        SvgPicture.asset(
                          AppImages.icon_warning,
                          width: 20,
                        ).onTap(() {
                          if (mounted) {
                            infoEXP(context,
                                image: AppImages.exp_point,
                                title: LocaleKeys.what_is_EXP.tr,
                                content: LocaleKeys.the_more_you_connect_exp.tr,
                                titleButton: LocaleKeys.view_all_tiers.tr, onTap: () {
                              Get.back();
                              Get.toNamed(Routes.RANK_BENEFITS);
                            });
                          }
                        }),
                      ],
                    ).paddingOnly(left: 8)),
              ],
            ),
            Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: SliderTheme(
                  data: const SliderThemeData(
                    thumbShape: RoundSliderThumbShape(enabledThumbRadius: 0),
                    overlayShape: RoundSliderOverlayShape(overlayRadius: 0),
                  ),
                  child: Obx(
                    () => Slider(
                        value: (controller.userRank.value.getPoint).toDouble(),
                        min: 0,
                        max: controller.userRank.value.getMaxPoints,
                        label: controller.userRank.value.getPoint.toString(),
                        onChanged: (value) {},
                        // onChanged: (double value) {
                        //   controller.currentSliderValue.value = value;
                        //   controller.activeColor();
                        // },
                        activeColor: controller.userRank.value.getColor,
                        inactiveColor: const Color(0xff1B1A28)),
                  ),
                )),
            Obx(() {
              if (controller.userRank.value.tier == 'Exclusive') {
                return const SizedBox();
              } else {
                return RichText(
                  text: TextSpan(
                    text: '${LocaleKeys.need.tr} ',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColor.neutral400,
                    ),
                    children: <TextSpan>[
                      TextSpan(
                          text: '${NumberFormat.decimalPattern().format(controller.userRank.value.needToUpTier.toDouble())} EXP',
                          style: const TextStyle(fontWeight: FontWeight.bold, color: AppColor.neutral100)),
                      TextSpan(text: ' ${LocaleKeys.loyalty_points_to_tier_up.tr}'),
                    ],
                  ),
                );
              }
            }).paddingTop(8),
          ],
        ).paddingTop(24));
  }

  Widget connectionButton() {
    return Center(
      child: Obx(() => Column(
            children: [
              Showcase.withWidget(
                key: five,
                height: 150,
                width: 270,
                container: buildshowCase(context, LocaleKeys.button_connected_showcase.tr, () {
                  ShowCaseWidget.of(context).next();
                }, number: '5', numberEnd: '7'),
                child: Container(
                    padding: const EdgeInsets.all(3),
                    decoration: BoxDecoration(
                      color: AppColor.neutral600,
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: controller.isConnecting.value
                            ? [const Color.fromARGB(255, 169, 169, 169), const Color.fromARGB(255, 169, 169, 169)]
                            : controller.statusConnection.value == StateButtonConnect.start
                                ? [const Color(0xFF00FFC8), const Color.fromARGB(255, 20, 49, 61)]
                                : [const Color.fromARGB(255, 169, 169, 169), const Color.fromARGB(255, 12, 28, 36)],
                      ),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: GestureDetector(
                      onTap: () async {
                        if (isProcessing) return; // Chặn nếu đang chạy

                        isProcessing = true;

                        try {
                          final val = !(controller.statusConnection.value != StateButtonConnect.stop);

                          if (controller.isConnecting.value) {
                            SocketApp.stopConnection();
                            return;
                          }

                          if (!val) {
                            controller.isConnecting.value = true;
                            controller.isShutdown.value = true;
                            if (controller.listActiveConnection.isNotEmpty) {
                              controller.listActiveConnectionProvisional.value = controller.listActiveConnection;
                              controller.getConnectionHistory();
                            }
                            SocketApp.stopConnection();
                            controller.listActiveConnection.clear();
                          } else {
                            controller.listActiveConnection.clear();
                            controller.isConnecting.value = true;
                            SocketApp.startConnection();

                            if (!kIsWeb) {
                              if ((AppState.instance.settingBox.read(SettingType.importantNote.toString()) ?? false) && Platform.isIOS) {
                                ProgressDialog.showDialogNotification(
                                  title: null,
                                  isShowCancel: false,
                                  onPressed: () => Get.back(),
                                  saveTitle: LocaleKeys.got_it.tr,
                                  content: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Image.asset(AppImages.ic_warning, width: 40, height: 40),
                                      CustomText(
                                        text: LocaleKeys.important_note.tr,
                                        fontSize: 20,
                                        fontWeight: FontWeight.w500,
                                      ).paddingSymmetric(vertical: 10),
                                      CustomText(
                                        text: LocaleKeys.you_should_open_u2dpn.tr,
                                        fontSize: 13,
                                        fontWeight: FontWeight.normal,
                                        color: AppColor.neutral300,
                                      ),
                                    ],
                                  ),
                                );
                                AppState.instance.settingBox.write(
                                  SettingType.importantNote.toString(),
                                  false,
                                );
                              }
                            }
                          }
                        } catch (e) {
                          logger.e("Error occurred: $e");
                        } finally {
                          // Chờ 2 giây để tránh spam
                          await Future.delayed(Duration(seconds: 4));
                          isProcessing = false;
                        }
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 400),
                        width: 160,
                        height: 95,
                        transformAlignment: controller.isConnecting.value
                            ? Alignment.centerLeft
                            : controller.statusConnection.value != StateButtonConnect.stop
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                        alignment: controller.isConnecting.value
                            ? Alignment.centerLeft
                            : controller.statusConnection.value != StateButtonConnect.stop
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(100),
                          gradient: LinearGradient(
                            colors: controller.isConnecting.value
                                ? [const Color(0xff0B0B00), const Color(0xff685000)]
                                : controller.statusConnection.value == StateButtonConnect.start
                                    ? [AppColor.primaryBlue500, const Color(0xFF1AF7A9)]
                                    : [AppColor.neutral600, AppColor.neutral500],
                          ),
                          border: Border.all(
                            color: AppColor.neutral700,
                            width: 5,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(6.0),
                          child: controller.statusConnection.value != StateButtonConnect.stop
                              ? Image.asset(
                                  AppImages.ic_active_connection,
                                )
                              : Image.asset(
                                  AppImages.ic_inactive_sessions,
                                ),
                        ),
                      ),
                    )),
              ),
              // controller.statusConnection.value == StateButtonConnect.stop
              //     ? Showcase.withWidget(
              //         key: six,
              //         height: 130 ,
              //         width: 270,
              //         container: buildshowCase(context, '''Click 'Stop sharing' button to end sharing bandwidth.''', () {
              //           ShowCaseWidget.of(context).dismiss();
              //         }, textButton: 'Done', showSkip: false),
              //         child: Image.asset(
              //           AppImages.active_connection,
              //           width: 159.w,
              //           height: 95 ,
              //         ).onTap(() {
              //           controller.statusConnection.value = StateButtonConnect.start;
              //           SocketApp.stopConnection();
              //         }),
              //       )
              //     : Showcase.withWidget(
              //         key: four,
              //         height: 130 ,
              //         width: 270.w,
              //         container: buildshowCase(context, '''Click 'Start sharing' button to begin sharing bandwidth.''', () {
              //           ShowCaseWidget.of(context).next();
              //         }, number: '4', numberEnd: '5'),
              //         child: Image.asset(
              //           AppImages.inactive_connection,
              //           width: 159.w,
              //           height: 95 ,
              //         ).onTap(() {
              //           if (!SocketApp.disableButton.value) {
              //             controller.statusConnection.value = StateButtonConnect.stop;
              //             ProgressDialog.showDialogNotification(
              //                 title: null,
              //                 isShowCancel: false,
              //                 onPressed: () {
              //                   Get.back();
              //                 },
              //                 saveTitle: 'Got it',
              //                 content: Column(
              //                   crossAxisAlignment: CrossAxisAlignment.start,
              //                   children: [
              //                     Image.asset(AppImages.ic_warning),
              //                     CustomText(
              //                       text: 'Important note',
              //                       fontSize: 20,
              //                       fontWeight: FontWeight.w500,
              //                     ).paddingSymmetric(vertical: 10 ),
              //                     CustomText(
              //                         fontWeight: FontWeight.normal,
              //                         color: AppColor.neutral300,
              //                         fontSize: 13,
              //                         text:
              //                             'You should open U2DPN app constantly to receive connections. The application does not run in the background, so make sure to not turn off your device.'),
              //                   ],
              //                 ));
              //             SocketApp().startConnection();
              //             if (AppState.instance.settingBox.read(SettingType.isStopSharing.toString()) == null ||
              //                 AppState.instance.settingBox.read(SettingType.isStopSharing.toString()) == true) {
              //               AppState.instance.settingBox.write(SettingType.isStopSharing.toString(), false);
              //               ShowCaseWidget.of(context).startShowCase([four]);
              //             }
              //           }
              //         }),
              //       ),
              CustomText(
                text: (controller.isConnecting.value
                        ? StateButtonConnect.connecting.text.tr
                        : controller.listActiveConnection.isNotEmpty && controller.statusConnection.value == StateButtonConnect.start
                            ? LocaleKeys.session_connected.tr
                            : controller.statusConnection.value.text.tr)
                    .toUpperCase(),
                fontSize: 16,
                letterSpacing: 2.2,
                textHeight: 1.5,
                fontWeight: FontWeight.w600,
                color: (controller.isConnecting.value)
                    ? const Color(0xffFFDE72)
                    : (controller.statusConnection.value == StateButtonConnect.start)
                        ? AppColor.primaryGreen600
                        : null,
              ).paddingSymmetric(vertical: 12),
            ],
          )),
    );
  }

  Widget bandwidthPrice() {
    return Column(
      children: [
        Text(LocaleKeys.bandwidth_price.tr, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: AppColor.neutral400)),
        const SizedBox(
          height: 4,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Obx(() => CustomText(
                  text: controller.bandwidthPriceData.value.ratePerKb ?? '0',
                  overflow: TextOverflow.ellipsis,
                  fontSize: 20,
                )),
            Image.asset(
              AppImages.logo,
              width: 20,
            ).paddingOnly(left: 6),
            const CustomText(
              text: ' / GB',
              overflow: TextOverflow.ellipsis,
              fontSize: 20,
            ),
            // Showcase.withWidget(
            //   key: one,
            //   height: 130 ,
            //   width: 270.w,
            //   container: buildshowCase(context, '''To start sharing bandwidth, set your desired bandwidth sharing rate.''', () {
            //     ShowCaseWidget.of(context).next();
            //   }, number: '1'),
            //   child: SvgPicture.asset(AppImages.icon_edit).paddingAll(8).onTap(() {
            //     controller.controllerTextField.value.text = '';
            //     controller.changeHintButton();
            //     controller.getSuggestBandwidth();
            //     editPrice(context);
            //   }),
            // )
          ],
        ),
      ],
    );
  }

  // void editPrice(BuildContext context) {
  //   return CustomBottomSheet.showModalNotFullScreenWithHeight(
  //     context,
  //     const EditPrice(),
  //     height: MediaQuery.of(context).size.height * 0.9,
  //   );
  // }

  void infoEXP(BuildContext context,
      {required String image, required String title, required String content, required String titleButton, required VoidCallback onTap}) {
    return CustomBottomSheet.showModalNotFullScreenWithHeight(
      context,
      Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.55 > 470 ? MediaQuery.of(context).size.height * 0.55 : 470,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                      onTap: () => Navigator.pop(context),
                      child: SvgPicture.asset(AppImages.icon_close),
                    )
                  ],
                ),
                const SizedBox(
                  height: 16,
                ),
                Image.asset(
                  image,
                  width: 182,
                  height: 120,
                ).paddingBottom(20),
                const Spacer(),
                CustomContainerShadow(
                    child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      text: title,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ).paddingSymmetric(vertical: 16),
                    CustomText(
                      text: content,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColor.neutral300,
                    ).paddingBottom(30),
                    CostomRaisedButtom(
                      name: titleButton,
                      function: onTap,
                      colorsGradient: const [AppColor.neutral500, AppColor.neutral500],
                      colorText: AppColor.neutral100,
                    )
                  ],
                )),
              ],
            ).paddingBottom(30),
          ),
        ),
      ),
      height: MediaQuery.of(context).size.height * 0.65,
    );
  }

  Widget connectionTime() {
    return Obx(() => Showcase.withWidget(
        key: four,
        height: 140,
        width: 270,
        container: buildshowCase(context, LocaleKeys.your_connection_time_showcase.tr, () {
          ShowCaseWidget.of(context).next();
        }, number: '4', numberEnd: '7'),
        child: Column(
          children: [
            CustomText(
              text: LocaleKeys.connection_time.tr.toUpperCase(),
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColor.neutral300,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomText(
                  text: Utils().formatDurationWithDayHourMinute(controller.totalTime.value).toString(),
                  overflow: TextOverflow.ellipsis,
                  fontSize: 32,
                  fontFamily: 'MonaSansExpanded',
                  fontWeight: FontWeight.w600,
                ),
              ],
            ).paddingSymmetric(vertical: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(50),
                border: Border.all(color: AppColor.neutral400),
              ),
              child: Row(mainAxisSize: MainAxisSize.min, mainAxisAlignment: MainAxisAlignment.center, children: [
                Image.asset(AppImages.icon_location, width: 16).paddingAll(4),
                Obx(() {
                  return controller.ipLocationBonusModel.value.countryName != null
                      ? Row(
                          children: [
                            CustomText(
                              text: controller.ipLocationBonusModel.value.countryName,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ).paddingOnly(left: 8),
                            controller.ipLocationBonusModel.value.bonusInfo != null
                                ? Container(
                                    decoration: BoxDecoration(
                                      gradient: AppColor.customLineGradientGreen,
                                      borderRadius: BorderRadius.circular(999),
                                      border: Border.all(color: AppColor.neutral500),
                                    ),
                                    margin: const EdgeInsets.only(left: 4),
                                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                    child: CustomText(
                                      text: '+${controller.ipLocationBonusModel.value.bonusInfo?.bonusAmountString}',
                                      color: Colors.black,
                                    ),
                                  )
                                : const SizedBox()
                          ],
                        )
                      : const SizedBox();
                }),
                SizedBox(width: 8),
                //  const Icon(Icons.arrow_forward_ios_rounded, color: AppColor.neutral400, size: 14),
              ]),
            ).onTap(() {
              // infoEXP(context,
              //     image: AppImages.location_bonus,
              //     title: LocaleKeys.what_is_location_bonus.tr,
              //     content: LocaleKeys.the_more_you_connect_location.tr,
              //     titleButton: LocaleKeys.view_all_location.tr, onTap: () {
              //   Get.back();
              //   Get.toNamed(Routes.LOCATION_BONUS);
              // });
            }),
          ],
        ).paddingTop(16)));
  }
}
