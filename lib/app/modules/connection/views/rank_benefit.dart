import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_utils.dart';
import 'package:u2u_dpn/widget/CustomContainerShadow.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

class RankBenefits extends GetView<ConnectionController> {
  const RankBenefits({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
          padding: const EdgeInsets.only(bottom: 00),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(AppImages.bg_white),
              fit: BoxFit.cover,
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  const Icon(Icons.arrow_back, color: Colors.white).onTap(() => Navigator.pop(context)).paddingRight(10),
                  CustomText(text: LocaleKeys.rank_tiers.tr, color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
                  // const Spacer(),
                  // InkWell(
                  //   onTap: () {
                  //     Get.toNamed(Routes.LEADER_BOARD);
                  //   },
                  //   child: Image.asset(
                  //     AppImages.rankUser,
                  //     width: 40,
                  //   ),
                  // )
                ],
              ).paddingOnly(top: 60, bottom: 20),
              Expanded(
                child: SingleChildScrollView(
                    child: CustomContainerShadow(
                        child: Column(
                  children: [
                    buildRank(
                        image: AppImages.member,
                        color: AppColor.member,
                        title: 'Member',
                        exp: '2k-20k EXP',
                        referral: '5%',
                        // reduction: '14.2%'),
                        reduction: '5%'),
                    const Divider(color: AppColor.neutral700).paddingSymmetric(horizontal: 16),
                    buildRank(image: AppImages.vip, color: AppColor.vip, title: 'VIP', exp: '20k-150k EXP', referral: '5%', reduction: '13%'),
                    const Divider(color: AppColor.neutral700).paddingSymmetric(horizontal: 16),
                    buildRank(image: AppImages.elite, color: AppColor.elite, title: 'Elite', exp: '150k-1M EXP', referral: '5%', reduction: '27%'),
                    const Divider(color: AppColor.neutral700).paddingSymmetric(horizontal: 16),
                    buildRank(
                        image: AppImages.premier, color: AppColor.premier, title: 'Premier', exp: '1M-5M EXP', referral: '5%', reduction: '40%'),
                    const Divider(color: AppColor.neutral700).paddingSymmetric(horizontal: 16),
                    buildRank(
                        image: AppImages.exclusive, color: AppColor.exclusive, title: 'Exclusive', exp: '>5M EXP', referral: '5%', reduction: '53%'),
                  ],
                )).paddingBottom(10)),
              ),
              const SizedBox(height: 20),
            ],
          ).paddingAll(16)),
    );
  }

  Widget buildRank({required String image, required Color color, String title = '', String exp = '', String referral = '', String reduction = ''}) {
    return Column(
      children: [
        Row(
          children: [
            Image.asset(
              image,
              width: 40,
              height: 40,
            ).paddingOnly(top: 16, bottom: 12, left: 16),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(text: title, fontSize: 16, color: AppColor.member),
                  CustomText(
                    text: exp,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  )
                ],
              ).paddingSymmetric(horizontal: 16, vertical: 4),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText(text: LocaleKeys.referral_bonus.tr, fontSize: 16, color: AppColor.neutral300),
            CustomText(text: referral, fontSize: 16),
          ],
        ).paddingSymmetric(horizontal: 16, vertical: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText(text: LocaleKeys.discount_platform_fee.tr, fontSize: 16, color: AppColor.neutral300),
            CustomText(text: reduction, fontSize: 16),
          ],
        ).paddingSymmetric(horizontal: 16, vertical: 4),
        const SizedBox(height: 16),
      ],
    );
  }
}
