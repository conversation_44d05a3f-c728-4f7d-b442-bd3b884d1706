import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/utils/app_utils.dart';
import 'package:u2u_dpn/widget/CustomContainerShadow.dart';
import 'package:u2u_dpn/widget/ultil.dart';
import 'package:u2u_dpn/widget/widget.dart';

class LeaderBoard extends GetView<ConnectionController> {
  const LeaderBoard({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: 0,
      length: 5,
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: CustomAppbarWithLeading(context, "Leader Board".tr),
        body: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(AppImages.bg_white),
              fit: BoxFit.cover,
            ),
          ),
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, mainAxisSize: MainAxisSize.min, children: [
            CustomContainerShadow(
                child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Image.asset(
                      AppImages.vip,
                      width: 24,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CustomText(text: "${CustomHideTex.hideMiddleText(SettingConfig.user_id)}"),
                            InkWell(
                              onTap: () async {
                                await Clipboard.setData(ClipboardData(text: "${SettingConfig.user_id}")).then((value) {
                                  // ignore: use_build_context_synchronously
                                  Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
                                });
                              },
                              child: const Icon(
                                Icons.copy,
                                color: AppColor.neutral400,
                                size: 16,
                              ).paddingLeft(6),
                            )
                          ],
                        ),
                        const CustomText(
                          text: "VIP",
                          color: AppColor.neutral400,
                          fontSize: 12,
                        )
                      ],
                    ).paddingLeft(8),
                  ],
                ),
                const CustomText(
                  text: "0",
                  fontFamily: 'Mona Sans Expanded',
                  fontWeight: FontWeight.w600,
                  color: AppColor.neutral400,
                )
              ],
            )),
            TabBar(
              indicatorWeight: 0,
              tabAlignment: TabAlignment.start,
              isScrollable: true,
              dividerColor: transparentColor,
              indicator: const UnderlineTabIndicator(
                borderSide: BorderSide(width: 2.0, color: AppColor.primaryGreen600),
              ),
              labelColor: Colors.white,
              unselectedLabelColor: AppColor.neutral400,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              onTap: (value) {
                print(value);
              },
              tabs: [
                const Tab(text: 'MEMBER'),
                const Tab(text: 'VIP'),
                const Tab(text: 'ELITE'),
                const Tab(text: 'PREMIER'),
                const Tab(text: 'EXCLUSIVE'),
              ],
            ).paddingSymmetric(vertical: 16),
            CustomText(
              text:
                  '${LocaleKeys.last_updated.tr.toUpperCase()}: ${Utils.convertTimestamptoDatetime((DateTime.now().millisecondsSinceEpoch) ~/ 1000)}',
              fontFamily: 'Mona Sans Expanded',
              fontSize: 12,
              color: AppColor.neutral400,
            ).paddingOnly(bottom: 16, top: 8),
            Flexible(
              child: CustomNodata(),
            )
            // Flexible(
            //   child: CustomContainerShadow(
            //     child: ConstrainedBox(
            //       constraints: BoxConstraints(maxHeight: Get.height),
            //       child: ListView.separated(
            //         shrinkWrap: true,
            //         itemCount: 10,
            //         itemBuilder: (context, index) {
            //           return listUser(context, index, address: '0x13ab...1af42');
            //         },
            //         separatorBuilder: (BuildContext context, int index) {
            //           return const Divider(
            //             color: AppColor.neutral700,
            //             height: 0,
            //           ).paddingSymmetric(vertical: 12);
            //         },
            //       ),
            //     ),
            //   ),
            // )
          ]),
        ),
      ),
    );
  }

  Row listUser(BuildContext context, int index, {String address = ''}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Image.asset(
          AppImages.member,
          width: 24,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomText(
                  text: address,
                  fontFamily: 'Mona Sans Expanded',
                ),
                InkWell(
                  onTap: () => Utils.copyFunction(context, address),
                  child: const Icon(
                    Icons.copy,
                    color: AppColor.neutral400,
                    size: 16,
                  ).paddingLeft(6),
                )
              ],
            ),
            const CustomText(
              text: "2,125 EXP",
              fontFamily: 'Mona Sans Expanded',
              color: AppColor.neutral400,
              fontSize: 12,
            ).paddingTop(4)
          ],
        ).paddingLeft(8),
        const Spacer(),
        index < 3
            ? Image.asset(
                index == 0 ? AppImages.no1 : (index == 1 ? AppImages.no2 : (index == 2 ? AppImages.no3 : '')),
                width: 32,
              )
            : CustomText(
                fontWeight: FontWeight.w600,
                color: AppColor.neutral400,
                fontFamily: 'Mona Sans Expanded',
                text: "${index + 1}",
              ).paddingRight(12)
      ],
    );
  }
}
