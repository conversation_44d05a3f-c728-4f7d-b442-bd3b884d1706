import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/model/connection_history_model.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/widget/cell_text.dart';
import 'package:u2u_dpn/widget/custom_appbar.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
import 'package:u2u_dpn/widget/loading.dart';
import 'package:u2u_dpn/widget/ultil.dart';

import '../../../../utils/app_images.dart';

class SessionDetailPage extends StatefulWidget {
  final String? hash;
  final ConnectionHistoryModel? connectionHistoryModel;

  const SessionDetailPage({
    super.key,
    this.hash,
    this.connectionHistoryModel,
  });

  @override
  State<SessionDetailPage> createState() => _SessionDetailPageState();
}

class _SessionDetailPageState extends State<SessionDetailPage> {
  final controller = Get.find<ConnectionController>();
  final RxInt elapsedSeconds = 0.obs;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
  
    if (widget.connectionHistoryModel == null)  {
        controller.detailConnection(widget.hash ?? '');
      final start = (controller.activeConnectionModel.value.handshakeAt ?? 0);
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      elapsedSeconds.value = now - start;

      _timer = Timer.periodic(const Duration(seconds: 1), (_) {
        elapsedSeconds.value++;
        controller.detailConnection(widget.hash ?? '');
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isActiveSession = widget.connectionHistoryModel == null;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppbarWithLeading(context, LocaleKeys.session_detail.tr),
      backgroundColor: const Color(0xFF120E21),
      body: Column(
        children: [
          Obx(() {
            if (controller.isLoadingDetail.value) {
              return const Expanded(child: LoadingScreen());
            }

            final model = isActiveSession
                ? controller.listActiveConnection
                    .firstWhereOrNull((e) => e.sessionHash == widget.hash)
                : widget.connectionHistoryModel;

            if (model == null) {
              return const Expanded(
                  child: Center(child: Text("Session not found")));
            }
            return buildDetail(context, model, isActiveSession);
          }),
        ],
      ).paddingTop(Platform.isIOS ? 40: 0),
    );
  }

  Widget buildDetail(BuildContext context, dynamic model, bool isActive) {
    return Expanded(
        child: Container(
            decoration: const BoxDecoration(
              color: AppColor.neutralBlack,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(15),
                topRight: Radius.circular(15),
              ),
            ),
            child: !controller.isLoadingDetail.value
                ? ListView(
                    padding: EdgeInsets.zero, // scroll từ đầu
                    children: [
                      buildHeader(model, isActive), // ✅ Cho vào ListView luôn
                      const SizedBox(height: 16),
                      buildRow(context, LocaleKeys.session_hash.tr,
                          model.sessionHash.toString(),
                          isCopyable: true, isHash: true),
                      buildRow(context, LocaleKeys.started_at.tr,
                          model.getStarted.toString()),
                      if (!isActive)
                        buildRow(context, LocaleKeys.ended_at.tr,
                            model.getEndTime.toString()),
                      buildRow(
                          context,
                          LocaleKeys.duration.tr,
                          isActive
                              ? Utils.formattedTimeDuration(
                                  elapsedSeconds.value)
                              : model.getDuration),
                      if (model.ratePerSecond != null &&
                          model.ratePerSecond != 0)
                        buildRateRow(
                            LocaleKeys.selling_price_per_hour.tr,
                            model.getRatePerHour,
                            AppImages.logo,
                            '/${LocaleKeys.hour.tr}'),
                      buildRateRow(LocaleKeys.selling_price_per_gb.tr,
                          model.getRatePerGb, AppImages.logo, '/ GB'),
                      buildRateRow(LocaleKeys.selling_price_per_gb.tr,
                          model.getRatePerGbV2, AppImages.logoU2U, '/ GB'),
                      // buildRateRow('${LocaleKeys.gross_amount.tr} U2U',
                      //   model.gettotalV2, AppImages.logoU2U, ''),
                      Obx(() => Column(
                            children: [
                              buildExpandableAmountRow(
                                  context,
                                  '${LocaleKeys.gross_amount.tr} U2U',
                                  model.gettotalV2.toString(),
                                  AppImages.logoU2U,
                                  isShowTotal: controller.showTotalV2.value,
                                  isV2: true),
                              if (controller.showTotalV2.value)
                                buildFeeBreakdown(model, isV2: true),
                            ],
                          )).paddingSymmetric(horizontal: 16),
                      Obx(() => Column(
                            children: [
                              buildExpandableAmountRow(
                                  context,
                                  '${LocaleKeys.gross_amount.tr} U2DPN',
                                  model.gettotal.toString(),
                                  AppImages.logo,
                                  isShowTotal: controller.showTotal.value),
                              if (controller.showTotal.value)
                                buildFeeBreakdown(model),
                            ],
                          )).paddingSymmetric(horizontal: 16),
                      const SizedBox(height: 16),
                    ],
                  )
                : const LoadingScreen()));
  }

  Widget buildHeader(dynamic model, bool isActive) {
    final usage = isActive
        ? Utils.kbToGb(model.bandwidthUsage ?? 0)
        : model.getsharedBandwidth ?? "0 GB";

    return SizedBox(
      height: Get.height * 0.4, // ✅ an toàn hơn thay vì phụ thuộc LayoutBuilder
      child: Stack(
        children: [
          Image.asset(
            AppImages.bg_session_detail,
            width: Get.width,
            fit: BoxFit.fitWidth,
          ),
          Positioned(
            bottom: 65,
            left: Get.width * 0.25,
            child: Center(
              child: Column(
                children: [
                  CustomText(
                    text: usage,
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                  ).paddingBottom(8),
                  Text(
                    '${LocaleKeys.last_updated.tr}: ${Utils.convertTimestamptoDatetime((DateTime.now().millisecondsSinceEpoch) ~/ 1000)}',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF65636F),
                    ),
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget buildRow(BuildContext context, String title, String value,
      {bool isCopyable = false, bool isHash = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16).copyWith(bottom: 8),
      child: cellText(
        title,
        Row(
          children: [
            Text(
              isHash
                  ? Utils.shortenUsername(value, 4, showlastString: true)
                  : value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFFDEDEDE),
              ),
            ),
            if (isCopyable)
              const Icon(Icons.copy, color: AppColor.neutral300, size: 15)
                  .onTap(() {
                Clipboard.setData(ClipboardData(text: value)).then((_) {
                  // ignore: use_build_context_synchronously
                  Utils.showSnackbar(
                      context, LocaleKeys.copied_successfully.tr);
                });
              }).paddingLeft(5),
          ],
        ),
      ),
    );
  }

  Widget buildRateRow(
      String label, String value, String imagePath, String suffix) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16).copyWith(bottom: 8),
      child: cellText(
        label,
        Row(
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: Get.width * 0.25),
              child: Text(
                value,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.right,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFFDEDEDE),
                ),
              ),
            ),
            const SizedBox(width: 4),
            Image.asset(imagePath, width: 20),
            if (suffix.isNotEmpty)
              Text(
                suffix,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColor.neutral400,
                ),
              )
          ],
        ),
      ),
    );
  }

  Widget buildExpandableAmountRow(
      BuildContext context, String label, String value, String imagePath,
      {bool isShowTotal = false, bool isV2 = false}) {
    return cellText(
      label,
      Row(
        children: [
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: Get.width * 0.25),
            child: Text(
              value,
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFFDEDEDE),
              ),
            ),
          ),
          const SizedBox(width: 4),
          Image.asset(imagePath, width: 20),
          Container(
            margin: const EdgeInsets.only(left: 6),
            decoration: BoxDecoration(
              color: AppColor.neutral600,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              isShowTotal ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
              color: AppColor.neutral300,
            ),
          ),
        ],
      ),
    ).onTap(() {
      isV2
          ? controller.showTotalV2.value = !controller.showTotalV2.value
          : controller.showTotal.value = !controller.showTotal.value;
    }).paddingBottom(5);
  }

  Widget buildFeeBreakdown(dynamic model, {bool isV2 = false}) {
    final tier =
        controller.userRank.value.tier ;

    String netAmount =
        isV2 ? model.getNetAmountV2(tier) : model.getNetAmount(tier);
    String platformFee =
        isV2 ? model.getPlatformFeeV2(tier) : model.getPlatformFee(tier);
    String referralFee =
        isV2 ? model.getReferralFeeV2(tier) : model.getReferralFee(tier);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.only(right: 12, left: 12, bottom: 12),
      decoration: const BoxDecoration(
        color: AppColor.neutral700,
        borderRadius: BorderRadius.all(Radius.circular(15)),
      ),
      child: Column(
        children: [
          buildRowFee(LocaleKeys.net_amount.tr, netAmount, isV2: isV2),
          buildRowFee(LocaleKeys.platform_fee.tr, platformFee, isV2: isV2),
          buildRowFee(LocaleKeys.referral_fee.tr, referralFee, isV2: isV2),
        ],
      ),
    );
  }

  Widget buildRowFee(String label, String value, {bool isV2 = false}) {
    return cellText(
      label,
      Row(
        children: [
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: Get.width * 0.25),
            child: Text(
              value,
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFFDEDEDE),
              ),
            ),
          ),
          const SizedBox(width: 4),
          Image.asset(!isV2 ? AppImages.logo : AppImages.logoU2U, width: 20),
        ],
      ),
    ).paddingOnly(top: 8);
  }
}
