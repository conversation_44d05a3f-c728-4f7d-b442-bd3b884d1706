import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_utils.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

class LocationBonusScreen extends GetView<ConnectionController> {

  const LocationBonusScreen({super.key});

  @override
  Widget build(BuildContext context) {
    controller.getLocationBonusAll();
    return Scaffold(
      backgroundColor: AppColor.neutral800,
      
      body: Container(
        decoration:  const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AppImages.bg_white),
            fit: BoxFit.cover,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
           Row(
            children: [
             const Icon(Icons.arrow_back, color: Colors.white).onTap(
               () => Navigator.pop(context)).paddingRight( 10),
              
                CustomText(text:LocaleKeys.location_bonus.tr, color:Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
            ],
          ).paddingOnly(top: 60, bottom: 30),
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: AppColor.neutralBlack,
                  borderRadius: BorderRadius.circular(12),
                ),
                child:   TextField(
        onChanged: (value) {
        controller.searchLocation(value);
        },
                        decoration: InputDecoration(
                     
                          hintText: LocaleKeys.search_location.tr,
                          border: InputBorder.none,
                          hintStyle: const TextStyle(color: Colors.white54),
                        ),
                        style: const TextStyle(color: Colors.white),
                      ),
                    
              ),
        
              // Table Headers
               Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(LocaleKeys.location.tr, style: const TextStyle(color: Colors.white60)),
                   Text(LocaleKeys.reward_bonus.tr, style: const TextStyle(color: Colors.white60)),
                ],
              ),
              const SizedBox(height: 8),
        
              // Location List
              Expanded(
                child:Obx(() =>  ListView.builder(
                  padding:const EdgeInsets.all(0),
                  itemCount: controller.filterListLocationBonusAll.length,
                  itemBuilder: (context, index) {
                    final loc = controller.filterListLocationBonusAll[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText(text:loc.countryName??'', color: Colors.white, fontSize: 16,),
                          CustomText(text:loc.getBonusAmount, color: Colors.white),
                         
                        ],
                      ),
                    );
                  },
                )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
