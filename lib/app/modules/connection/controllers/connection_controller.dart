import 'dart:async';
// import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_speed_test_plus/flutter_speed_test_plus.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:u2u_dpn/app/modules/dash_board/controllers/dash_board_controller.dart';
import 'package:u2u_dpn/app/modules/rewards/controllers/rewards_controller.dart';
import 'package:u2u_dpn/data/app_controller.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/model/active_connection_model.dart';
import 'package:u2u_dpn/model/bandwidth_price_model.dart';
import 'package:u2u_dpn/model/connection_history_model.dart';
import 'package:u2u_dpn/model/connection_overview_model.dart';
import 'package:u2u_dpn/model/ip_country_model.dart';
import 'package:u2u_dpn/model/ip_location_bobus.dart';
import 'package:u2u_dpn/model/user_detail_model.dart';
import 'package:u2u_dpn/repository/api_repository.dart';
import 'package:u2u_dpn/utils/app_color.dart';
// import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
// import 'package:u2u_dpn/widget/custom_text.dart';
import 'package:u2u_dpn/widget/dialog/process_dialog.dart';

enum StateButtonConnect {
  start(LocaleKeys.node_activated),
  stop(LocaleKeys.disconnected),
  connecting(LocaleKeys.connecting),
  loading(LocaleKeys.connecting_to_U2DPN_subnet);

  const StateButtonConnect(this.text);
  final String text;
}

class ConnectionController extends GetxController {
  final dashboardController = Get.put(DashBoardController());
  late RefreshController refreshController;
  late RefreshController refreshDetailController;
  late RefreshController refreshHistoryController;
  var isShutdown = false.obs;
  var controllerTextField = TextEditingController().obs;
  var isHintButton = true.obs;
  String referralCodeValue = '';
  var listActiveConnection = <ActiveConnectionModel>[].obs;
  var listActiveConnectionProvisional = <ActiveConnectionModel>[].obs;
  var ipLocationBonusModel = IPLocationBonusModel().obs;
  var bandwidthPriceData = BandwidthPriceModel().obs;
  var activeConnectionModel = ActiveConnectionModel().obs;
  var listConnectionHistoryData = <ConnectionHistoryModel>[].obs;
  // AudioPlayer player = AudioPlayer();
  ApiRepository apiRepository = ApiRepository();
  var suggestBandwidth = ''.obs;
  var connectionOverviewModel = ConnectionOverviewModel().obs;
  // ActiveConnectionModel activeConnectionModel= Get.arguments!=null?Get.arguments:ActiveConnectionModel();
  var isloading = false.obs;
  var isLoadingDetail = false.obs;
  var showCommunity = false.obs;
  var showTotal = false.obs;
  var showTotalV2 = false.obs;

  // var autoConnection = SettingConfig.autoConnection.obs;
  var statusConnection = StateButtonConnect.stop.obs;
  var isConnecting = false.obs;
  var userRank = UserTier().obs;
  AppController appController = Get.put(AppController());
  RewardsController rewardsController = Get.put(RewardsController());
  var height = 105.0.obs;
  var currentIp = IpCountryModel(ip: '0.0.0.0', country: '').obs;
  var totalTime = 0.obs;
  var listLocationBonusAll = <LocationBonusAllModel>[].obs;
  var filterListLocationBonusAll = <LocationBonusAllModel>[].obs;
  int attempt = 0;

  var downloadRate = 0.0.obs;
  var uploadRate = 0.0.obs;
  var isTesting = false.obs;
  var ip = ''.obs; // thông tin IP mạng
  var asn = ''.obs; // tổ chức mạng
  var isp = ''.obs; // nhà cung cấp mạng
  final FlutterInternetSpeedTest flutterInternetSpeedTest = FlutterInternetSpeedTest();

  @override
  void onInit() {
    refreshController = RefreshController(initialRefresh: false);
    refreshDetailController = RefreshController(initialRefresh: false);
    refreshHistoryController = RefreshController(initialRefresh: false);
    super.onInit();
  }

  @override
  void onClose() {
    controllerTextField.value.dispose();
    refreshController.dispose();
    refreshDetailController.dispose();
    refreshHistoryController.dispose();
    super.onClose();
  }

  void changeHintButton() {
    if (controllerTextField.value.text.isEmpty) {
      isHintButton.value = true;
    } else {
      isHintButton.value = false;
    }
  }

  void removeDuplicateSessions() {
    final historyHashes = listConnectionHistoryData.map((e) => e.sessionHash).toSet();

    listActiveConnection.removeWhere(
      (active) => historyHashes.contains(active.sessionHash),
    );
  }

  Future onRefresh() async {
    // monitor network fetch
    //  await Future.delayed(Duration(milliseconds: 1000));
    // if failed,use refreshFailed()
    // await connectionOverview();
    // await getConnectionHistory();

    await getTotalTime();
    await getloactionBonus();
    // await getActiveConnection();
    // refreshController.refreshCompleted();
  }

  void onRefreshDetail(String id) async {
    // monitor network fetch
    //  await Future.delayed(Duration(milliseconds: 1000));
    // if failed,use refreshFailed()
    if (id.isEmpty) {
      return;
    }
    await detailConnection(id);
    refreshDetailController.refreshCompleted();
  }

  void onRefreshHistory() async {
    // monitor network fetch
    //  await Future.delayed(Duration(milliseconds: 1000));
    // if failed,use refreshFailed()

    await getConnectionHistory();
    await connectionOverview();
    removeDuplicateSessions();
    refreshHistoryController.refreshCompleted();
  }

  void onLoading() async {
    // monitor network fetch
    await Future.delayed(const Duration(milliseconds: 1000));
    // if failed,use loadFailed(),if no data return,use LoadNodata()

    refreshController.loadComplete();
  }

  // Method to insert text at the current cursor position
  void insertText(String myText) {
    final text = controllerTextField.value.text;
    final textSelection = controllerTextField.value.selection;
    final newText = text.replaceRange(
      textSelection.start,
      textSelection.end,
      myText,
    );
    final myTextLength = myText.length;
    if (controllerTextField.value.text.length < 6) {
      controllerTextField.value.text = newText;
      controllerTextField.value.selection = textSelection.copyWith(
        baseOffset: textSelection.start + myTextLength,
        extentOffset: textSelection.start + myTextLength,
      );
    }
  }

  // Method to handle backspace action in the text input
  void backspace() {
    final text = controllerTextField.value.text;
    final textSelection = controllerTextField.value.selection;
    final selectionLength = textSelection.end - textSelection.start;

    // There is a selection.
    if (selectionLength > 0) {
      final newText = text.replaceRange(
        textSelection.start,
        textSelection.end,
        '',
      );
      if (controllerTextField.value.text.length < 6) {
        controllerTextField.value.text = newText;
        controllerTextField.value.selection = textSelection.copyWith(
          baseOffset: textSelection.start,
          extentOffset: textSelection.start,
        );
      }
      return;
    }

    // The cursor is at the beginning.
    if (textSelection.start == 0) {
      return;
    }

    // Delete the previous character
    final previousCodeUnit = text.codeUnitAt(textSelection.start - 1);
    final offset = isUtf16Surrogate(previousCodeUnit) ? 2 : 1;
    final newStart = textSelection.start - offset;
    final newEnd = textSelection.start;
    final newText = text.replaceRange(
      newStart,
      newEnd,
      '',
    );
    controllerTextField.value.text = newText;
    controllerTextField.value.selection = textSelection.copyWith(
      baseOffset: newStart,
      extentOffset: newStart,
    );
  }

  bool isUtf16Surrogate(int value) {
    return value & 0xF800 == 0xD800;
  }

  Future getloactionBonus() async {
    try {
      var res = await apiRepository.getLocationBonus();
      if (res != null && res.data != null) {
        ipLocationBonusModel.value = IPLocationBonusModel.fromJson(res.data);
      }
    } catch (e) {
      logger.d(e);
    }
  }

  Future getLocationBonusAll() async {
    try {
      var res = await apiRepository.getLocationBonusAll();
      if (res != null && res.data != null) {
        listLocationBonusAll.value = (res.data as List).map((e) => LocationBonusAllModel.fromJson(e)).toList();
        filterListLocationBonusAll.value = listLocationBonusAll;
      }
    } catch (e) {
      logger.d(e);
    }
  }

  void searchLocation(String keyword) {
    if (keyword.isEmpty) {
      filterListLocationBonusAll.value = listLocationBonusAll;
    } else {
      filterListLocationBonusAll.value =
          listLocationBonusAll.where((loc) => loc.countryName?.toLowerCase().contains(keyword.toLowerCase()) ?? false).toList();
    }
  }

  Future getBandwidthPrice() async {
    try {
      var res = await apiRepository.getBandwidthPrice();

      if (res != null && res.data != null) {
        bandwidthPriceData.value = BandwidthPriceModel.fromJson(res.data);
      }
    } catch (e) {
      logger.d(e);
    }
  }

  Future detailConnection(String id) async {
    try {
      final index = listActiveConnection.indexWhere((element) => element.sessionHash == id);

      if (index == -1) {
        // Không tìm thấy session có id khớp
        Get.back();
        getConnectionHistory();
        return;
      }

      activeConnectionModel.value = listActiveConnection[index];

      // Do something with activeConnectionModel.value...
    } catch (e) {
      logger.e("Error in detailConnection: $e");
    }
  }

  Future connectionOverview() async {
    try {
      var res = await apiRepository.connectionOverview();
      //  await getActiveConnection();
      //  await getBandwidthPrice();
      if (res != null && res.data != null) {
        connectionOverviewModel.value = ConnectionOverviewModel.fromJson(res.data);
      }
    } catch (e) {
      logger.d(e);
    }
  }

  Future getSuggestBandwidth() async {
    try {
      var res = await apiRepository.getSuggesttionbandwidth();
      if (res != null && res.data != null) {
        suggestBandwidth.value = (res.data['rate_per_kb'] ?? 0).toString();
      }
    } catch (e) {
      logger.d(e);
    }
  }

  Future setBandwidthPrice(BuildContext context, double price) async {
    try {
      var res = await apiRepository.setBandwidthPrice(price.toInt());

      if (res != null && res.data != null) {
        ProgressDialog.showDialogNotification(isShowCancel: false, content: dialogPriceWasUpdated());
        getBandwidthPrice();
      } else {
        ProgressDialog.showDialogNotification(
            isShowCancel: false,
            content: const Column(
              children: [
                SizedBox(
                  height: 16,
                ),
                Text(
                  'Error',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
              ],
            ));
      }
    } catch (e) {
      ProgressDialog.showDialogNotification(
          isShowCancel: false,
          content: Column(
            children: [
              const SizedBox(
                height: 16,
              ),
              Text(
                e.toString(),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFFFFFFFF),
                ),
              ),
            ],
          ));
      logger.d(e);
    }
  }

  Widget dialogPriceWasUpdated() {
    return Column(
      children: [
        Image.asset(
          AppImages.claim_rewards_done,
          width: 104,
        ),
        const SizedBox(
          height: 16,
        ),
        const Text(
          'Price was updated',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color(0xFFFFFFFF),
          ),
        ),
      ],
    );
  }

  // Method to retrieve connection history data from the API
  Future getConnectionHistory() async {
    // try {
    //   isloading.value = true;
    var res = await apiRepository.connectionHistory();

    if (res != null && res.data != null) {
      listConnectionHistoryData.value = (res.data as List).map((e) => ConnectionHistoryModel.fromJson(e)).toList();
    }

    if (listActiveConnectionProvisional.isNotEmpty) {
      checkDurationMismatch(
        historyList: listConnectionHistoryData,
        activeList: listActiveConnectionProvisional,
      );
    }

    //   isloading.value = false;
    // } catch (e) {
    //   isloading.value = false;
    //   logger.d(e);
    // }
  }

  Future<void> checkDurationMismatch({
    required List<ConnectionHistoryModel> historyList,
    required List<ActiveConnectionModel> activeList,
  }) async {
    // Tạo map sessionHash -> ActiveConnectionModel
    final Map<String, ActiveConnectionModel> activeMap = {
      for (var a in activeList)
        if (a.sessionHash != null) a.sessionHash!: a,
    };

    for (final history in historyList) {
      final sessionHash = history.sessionHash;

      if (sessionHash != null && activeMap.containsKey(sessionHash)) {
        final active = activeMap[sessionHash]!;

        final int historyDuration = history.duration ?? 0;
        final int activeDuration = active.duration ?? 0;

        final int diffInSeconds = (historyDuration - activeDuration).abs();

        if (diffInSeconds > 60) {
          await Sentry.captureMessage(
            'WRONG DURATION\n'
            'UserId: ${SettingConfig.user_id}\n'
            'SessionHash: $sessionHash\n'
            '• History duration = $historyDuration sec\n'
            '• Active  duration = $activeDuration sec\n'
            '• Difference       = $diffInSeconds sec',
          );
        }
      }
    }
  }

  // Future getActiveConnection() async {
  //   try {
  //     var res = await apiRepository.activeConnection();
  //     if (res != null && res.data != null) {
  //       listActiveConnection.value = (res.data as List).map((e) => ActiveConnectionModel.fromJson(e)).toList();
  //     }
  //   } catch (e) {
  //     logger.d(e);
  //   }
  // }
  // Method to handle adding a new active connection
  Future getActiveConnection(dynamic data) async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      listActiveConnection.value = ActiveConnectionModel.fromJsonList(data).where((e) {
        final start = e.handshakeAt ?? 0;
        final elapsed = now - start;
        return e.status?.toLowerCase() == 'active' && elapsed <= 3000;
      }).toList();

      logger.d("list: $listActiveConnection");
    } catch (e) {
      logger.d(e);
    }
  }

  // // Method to handle updating an active connection
  // Future updateActiveConnection(dynamic data) async {
  //   try {
  //     ActiveConnectionModel activeConnectionModel = ActiveConnectionModel.fromJson(data);
  //     if (listActiveConnection.isNotEmpty) {
  //       listActiveConnection[listActiveConnection.indexWhere((element) => element.sessionHash == activeConnectionModel.sessionHash)] =
  //           activeConnectionModel;
  //       if (Get.arguments != null) detailConnection(Get.arguments);
  //     } else if (!listActiveConnection.contains(activeConnectionModel)) {
  //       getActiveConnection(data);
  //     }
  //     if (activeConnectionModel.status == 'Finished') {
  //       terminatedActiveConnection(data);
  //     }
  //     logger.d("list: $listActiveConnection");
  //   } catch (e) {
  //     logger.d(e);
  //   }
  // }

  // // Method to handle terminating an active connection
  // Future terminatedActiveConnection(dynamic data) async {
  //   try {
  //     ActiveConnectionModel activeConnectionModel = ActiveConnectionModel.fromJson(data);
  //     if (listActiveConnection.any((element) => element.sessionHash == activeConnectionModel.sessionHash)) {
  //       listActiveConnection.removeWhere((element) => element.sessionHash == activeConnectionModel.sessionHash);
  //       listActiveConnection.removeAt(listActiveConnection.indexWhere((element) => element.sessionHash == activeConnectionModel.sessionHash));
  //       await Future.delayed(const Duration(milliseconds: 1000));
  //       // player.play(AssetSource("sound/stop.mp3"));
  //       logger.d("list: $listActiveConnection");
  //     }
  //   } catch (e) {
  //     logger.d(e);
  //   }
  // }

  Future startSesionLoyaltyPoint() async {
    try {
      await apiRepository.startSessionLoyaltyPoint();
    } catch (e) {
      logger.d(e);
    }
  }

  Future endSessionLoyaltyPoint() async {
    try {
      await apiRepository.endSessionLoyaltyPoint();
    } catch (e) {
      logger.d(e);
    }
  }

  Future heartBeat() async {
    try {
      await apiRepository.hearbeat();
    } catch (e) {
      logger.d(e);
    }
  }

  Future getTotalTime() async {
    try {
      var res = await apiRepository.getTotalTime();

      if (res != null && res.data != null) {
        totalTime.value = res.data['total_time'];
        userRank.value = UserTier(points: totalTime.value);
        attempt = 0;
        logger.d("Total time: ${totalTime.value}");
      }
    } catch (e) {
      attempt++;
      if (attempt < 3) {
        getTotalTime();
      }
    }
  }

  startTest({bool needDisable = false}) {
    flutterInternetSpeedTest.startTesting(
      onStarted: () {
        isTesting.value = true;
        print("⚠️ Test started.");
      },
      onCompleted: (download, upload) {
        downloadRate.value = download.transferRate;
        uploadRate.value = upload.transferRate;
        isTesting.value = false;

        print("✅ Test completed.");
        if (uploadRate.value < 10) {
          Get.bottomSheet(
            Container(
              decoration: const BoxDecoration(
                color: AppColor.neutral700,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min, // 🔑 Chìa khóa để co theo nội dung
                  children: [
                    Container(
                      width: 85,
                      height: 4,
                      margin: const EdgeInsets.only(top: 8),
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(16)),
                        color: Color(0xFF4C556B),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Align(
                      alignment: Alignment.topRight,
                      child: Icon(Icons.close, color: AppColor.neutral400),
                    ).onTap(() => Get.back()),
                    Image.asset(AppImages.no_internet, width: 80),
                    CustomText(
                      text: LocaleKeys.network_connection_warning.tr,
                      fontSize: 20,
                    ).paddingOnly(top: 24, bottom: 8),
                    CustomText(
                      text: LocaleKeys.network_connection_warning_description.tr,
                      color: AppColor.neutral300,
                      textAlign: TextAlign.center,
                      fontSize: 14,
                    ),
                    const SizedBox(height: 30),
                  ],
                ),
              ),
            ),
            barrierColor: Colors.black.withOpacity(0.5),
            isDismissible: true,
            enableDrag: true,
          );
        }
        // print("Download: ${download.transferRate} , Upload: ${upload.transferRate}");
      },
      onDefaultServerSelectionDone: (client) {
        ip.value = client?.ip ?? '';
        asn.value = client?.asn ?? '';
        isp.value = client?.isp ?? '';
      },
      onError: (msg, err) {
        isTesting.value = false;
        downloadRate.value = 0;
        uploadRate.value = 0;
        ip.value = '';
        asn.value = '';
        isp.value = '';
        print("❌ Error: $msg ($err)");
      },
      onCancel: () {
        isTesting.value = false;
        downloadRate.value = 0;
        uploadRate.value = 0;
        ip.value = '';
        asn.value = '';
        isp.value = '';
        print("⚠️ Test cancelled.");
      },
    );
  }
}
