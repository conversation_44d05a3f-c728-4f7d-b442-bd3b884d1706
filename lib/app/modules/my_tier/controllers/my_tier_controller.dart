// import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:u2u_dpn/app/modules/rewards/controllers/rewards_controller.dart';
import 'package:u2u_dpn/data/app_controller.dart';
// import 'package:u2u_dpn/utils/app_utils.dart';

class MyTierController extends GetxController {
  AppController appController = Get.find();
  late RefreshController refreshControllerMyTier;

  var currentSliderValue = 60.0.obs;
  RewardsController rewardsController = Get.put(RewardsController());
  // var ImageRank = AppImages.icon_rank_mini.obs;
  // RewardsController rewardsController = Get.put(RewardsController());

  @override
  void onInit() {
    refreshControllerMyTier = RefreshController(initialRefresh: false);
    super.onInit();
  }

  @override
  void onClose() {
    refreshControllerMyTier.dispose();
    super.onClose();
  }

  void onRefresh() async {
    // monitor network fetch
    //  await Future.delayed(Duration(milliseconds: 1000));
    // if failed,use refreshFailed()

    var resUser = await appController.getUserDetail();
    if (resUser != null) {
      rewardsController.userDetailModel.value = resUser;
    }

    refreshControllerMyTier.refreshCompleted();
  }

  // Color activeColor() {
  //   return currentSliderValue.value <= 50
  //       ? Color(0xffDEDEDE)
  //       : currentSliderValue.value <= 150
  //           ? Color(0xffFFDE72)
  //           : currentSliderValue.value <= 500
  //               ? Color(0xff6DB7FC)
  //               : Color(0xff13D4A3);
  // }
}
