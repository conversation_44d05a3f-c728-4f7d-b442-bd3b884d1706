import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:u2u_dpn/app/modules/my_tier/controllers/my_tier_controller.dart';
import 'package:u2u_dpn/app/modules/rewards/controllers/rewards_controller.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';

// ignore: must_be_immutable
class Benefits extends GetView<MyTierController> {
  Benefits({super.key});

  RewardsController rewardsController = Get.put(RewardsController());

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(LocaleKeys.benefits.tr, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: Color(0xFFFFFFFF))),
                InkWell(
                  onTap: () => Navigator.pop(context),
                  child: SvgPicture.asset(AppImages.icon_close),
                )
              ],
            ),
            benefitsCell(AppImages.icon_rank_bronze, LocaleKeys.bronze.tr, '0 GB', LocaleKeys.fee_reduction_15.tr, AppColor.brownColor,
                isYourRank: (rewardsController.userDetailModel.value.userTier?.tier) == 'Bronze'),
            benefitsCell(AppImages.icon_rank, LocaleKeys.silver.tr, '10 GB', LocaleKeys.fee_reduction_13.tr, const Color(0xffDEDEDE),
                isYourRank: (rewardsController.userDetailModel.value.userTier?.tier) == 'Silver'),
            benefitsCell(AppImages.icon_rank_gold, LocaleKeys.gold.tr, '100 GB', LocaleKeys.fee_reduction_11.tr, const Color(0xffFFDE72),
                isYourRank: (rewardsController.userDetailModel.value.userTier?.tier) == 'Gold'),
            benefitsCell(AppImages.icon_rank_platinum, LocaleKeys.platinum.tr, '1 TB', LocaleKeys.fee_reduction_9.tr, const Color(0xff6DB7FC),
                isYourRank: (rewardsController.userDetailModel.value.userTier?.tier) == 'Platinum'),
            benefitsCell(AppImages.icon_rank_diamond, LocaleKeys.diamond.tr, '10 TB', LocaleKeys.fee_reduction_7.tr, const Color(0xff1AF7A9),
                isYourRank: (rewardsController.userDetailModel.value.userTier?.tier) == 'Diamond'),
          ],
        ),
      ),
    );
  }

  Widget benefitsCell(String iconRank, String title, String point, String sub, Color colorTitle, {bool isYourRank = false}) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppColor.neutral600,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  SvgPicture.asset(
                    iconRank,
                    width: 36,
                  ),
                  const SizedBox(
                    width: 8,
                  ),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: Get.width * 0.3),
                    child: Text(
                      title,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: colorTitle,
                      ),
                    ),
                  ),
                  if (isYourRank)
                    const SizedBox(
                      width: 12,
                    ),
                  if (isYourRank)
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: const Color(0xFF383846),
                      ),
                      child: Text(
                        LocaleKeys.your_rank.tr,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Color(0xffDEDEDE),
                        ),
                      ),
                    )
                ],
              ),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: Get.width * 0.35),
                child: Text(
                  point,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xffDEDEDE),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 8,
          ),
          Text(
            LocaleKeys.benefits.tr,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xffDEDEDE),
            ),
          ),
          const SizedBox(
            height: 4,
          ),
          Text(
            sub,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Color(0xffA7A4A4),
            ),
          ),
        ],
      ),
    );
  }
}
