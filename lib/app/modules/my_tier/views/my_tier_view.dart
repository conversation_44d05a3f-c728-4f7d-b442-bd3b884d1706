// ignore_for_file: deprecated_member_use

import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:u2u_dpn/app/modules/my_tier/views/benefits.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/loading.dart';
import 'package:u2u_dpn/widget/widget.dart';

import '../controllers/my_tier_controller.dart';

class MyTierView extends GetView<MyTierController> {
  const MyTierView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    // logger.d(controller.rewardsController.userDetailModel.value.userTier?.tier);
    return SmartRefresher(
        controller: controller.refreshControllerMyTier,
        onRefresh: controller.onRefresh,
        child: Scaffold(
          backgroundColor: const Color(0xFF1B1A28),
          appBar: CustomAppBarTitle(context, title: ''),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  rankingCard(context),
                  const SizedBox(
                    height: 12,
                  ),
                  howToUpYourRank(),
                  const SizedBox(
                    height: 24,
                  ),
                  recentPoints(),
                ],
              ),
            ),
          ),
        ));
  }

  Widget rankingCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        color: const Color(0xFF120E21),
      ),
      child: Column(
        children: [
          Obx(() => Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                    border: Border.all(
                      width: 0.5,
                      color: AppColor.neutral600,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    gradient: const LinearGradient(
                        begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: [Color(0xff1D1C2B), AppColor.neutral600])
                    // color: const Color(0xFF2D2C3B),
                    ),
                child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset(
                            controller.rewardsController.userDetailModel.value.userTier?.getImageRank ?? AppImages.icon_rank_bronze,
                            width: 52,
                          ),
                          const SizedBox(
                            width: 6,
                          ),
                          ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: Get.width * 0.3),
                            child: Text(
                              (controller.rewardsController.userDetailModel.value.userTier?.tier ?? '').toLowerCase().tr,
                              overflow: TextOverflow.fade,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w500,
                                color: controller.rewardsController.userDetailModel.value.userTier?.getColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                      InkWell(
                        onTap: () {
                          CustomBottomSheet.showModalNotFullScreenWithHeight(
                            context,
                            Benefits(),
                            height: MediaQuery.of(context).size.height * 0.9,
                          );
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 5),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            color: AppColor.neutral600,
                          ),
                          child: Row(
                            children: [
                              Text(LocaleKeys.benefits.tr,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: AppColor.neutral200,
                                  )).paddingOnly(left: 16),
                              const Icon(
                                Icons.arrow_forward_ios_rounded,
                                color: AppColor.neutral300,
                                size: 16,
                              ).paddingOnly(left: 4, right: 6)
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: Get.width * 0.4),
                    child: Row(
                      children: [
                        Text(
                          '${controller.rewardsController.userDetailModel.value.userTier?.getPoint ?? 0}',
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: controller.rewardsController.userDetailModel.value.userTier?.getColor,
                          ),
                        ),
                        Text(
                          ' / ${controller.rewardsController.userDetailModel.value.userTier?.getPointRank ?? 0}',
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: white,
                          ),
                        ),
                      ],
                    ),
                  ).paddingOnly(top: 24),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: SliderTheme(
                      data: const SliderThemeData(
                        thumbShape: RoundSliderThumbShape(enabledThumbRadius: 0),
                        overlayShape: RoundSliderOverlayShape(overlayRadius: 0),
                      ),
                      child: Slider(
                          value: (controller.rewardsController.userDetailModel.value.userTier?.points ?? 0).toDouble(),
                          min: 0,
                          max: controller.rewardsController.userDetailModel.value.userTier?.getMaxPoints ?? 0,
                          label: controller.currentSliderValue.value.round().toString(),
                          onChanged: (value) {},
                          // onChanged: (double value) {
                          //   controller.currentSliderValue.value = value;
                          //   controller.activeColor();
                          // },
                          activeColor: controller.rewardsController.userDetailModel.value.userTier?.getColor,
                          inactiveColor: const Color(0xff1B1A28)),
                    ),
                  ),
                  // Padding(
                  //   padding: EdgeInsets.zero,
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //     children: [
                  //       SvgPicture.asset(
                  //         AppImages.icon_rank_mini,
                  //         color: AppColor.brownColor,
                  //         width: 24,
                  //       ),
                  //       SvgPicture.asset(
                  //         AppImages.icon_rank_mini,
                  //         color: const Color(0xffDEDEDE),
                  //         width: 24,
                  //       ),
                  //       SvgPicture.asset(
                  //         AppImages.icon_rank_mini,
                  //         color: const Color(0xffFFDE72),
                  //         width: 24,
                  //       ),
                  //       SvgPicture.asset(
                  //         AppImages.icon_rank_mini,
                  //         color: const Color(0xff6DB7FC),
                  //         width: 24,
                  //       ),
                  //       SvgPicture.asset(
                  //         AppImages.icon_rank_mini,
                  //         color: const Color(0xff13D4A3),
                  //         width: 24,
                  //       ),
                  //     ],
                  //   ),
                  // )

                  // Row(
                  //   children: [
                  //     SvgPicture.asset(AppImages.icon_clock),
                  //     CustomText(
                  //       text: LocaleKeys.total_online.tr,
                  //       color: AppColor.neutral300,
                  //     ).paddingLeft(7),
                  //     Spacer(),
                  //     CustomText(
                  //       text: controller.appController.userDetailModel.value.userXp?.getTime,
                  //     ),
                  //   ],
                  // ).paddingSymmetric(vertical: 15)
                ]),
              )),
          const SizedBox(
            height: 20,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    rankingInfor(AppImages.icon_rank_bronze, LocaleKeys.bronze.tr, '0 GB', AppColor.brownColor).paddingOnly(bottom: 16),
                    rankingInfor(AppImages.icon_rank_gold, LocaleKeys.gold.tr, '100 GB', const Color(0xffFFDE72)),
                    const SizedBox(
                      height: 16,
                    ),
                    rankingInfor(AppImages.icon_rank_diamond, LocaleKeys.diamond.tr, '10 TB', const Color(0xff1AF7A9)),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    rankingInfor(AppImages.icon_rank, LocaleKeys.silver.tr, '10 GB', const Color(0xffDEDEDE)).paddingOnly(bottom: 16),
                    rankingInfor(AppImages.icon_rank_platinum, LocaleKeys.platinum.tr, '1 TB', const Color(0xff6DB7FC)),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          // Padding(
          //   padding: const EdgeInsets.all(14.0),
          //   child: Row(
          //     children: [
          //       const Spacer(),
          //       InkWell(
          //         onTap: () {
          //           CustomBottomSheet.showModalNotFullScreenWithHeight(
          //             context,
          //             Benefits(),
          //             height: MediaQuery.of(context).size.height * 0.9,
          //           );
          //         },
          //         child: Container(
          //           padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 5),
          //           decoration: BoxDecoration(
          //             borderRadius: BorderRadius.circular(16),
          //             color: const Color(0xFF2D2C3B),
          //           ),
          //           child: const Text('Benefits',
          //               style: TextStyle(
          //                 fontSize: 14,
          //                 fontWeight: FontWeight.w500,
          //                 color: AppColor.neutral200,
          //               )),
          //         ),
          //       )
          //     ],
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget howToUpYourRank() {
    return Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: const Color(0xFF242332),
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              AppImages.icon_up_rank,
              width: 28,
              fit: BoxFit.contain,
            ),
            const SizedBox(
              width: 16,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: Get.width * 0.7),
                  child: Text(
                    LocaleKeys.how_to_up_your_rank.tr,
                    maxLines: 3,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xffDEDEDE),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                ConstrainedBox(
                  constraints: BoxConstraints(
                      maxWidth: kIsWeb
                          ? 470 * 0.6
                          : Platform.isWindows
                              ? 400 * 0.55
                              : Get.width * 0.6),
                  child: Text(
                    LocaleKeys.complete_more_missions.tr,
                    maxLines: 5,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Color(0xffA7A4A4),
                    ),
                  ),
                ),
              ],
            )
          ],
        ));
  }

  Widget recentPoints() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.recent_points.tr,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: AppColor.neutral200,
          ),
        ),
        Obx(() => controller.rewardsController.loadingListPoint.value
            ? const LoadingScreen(height: 135)
            : controller.rewardsController.listUserTierPoints.isNotEmpty
                ? ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: controller.rewardsController.listUserTierPoints.length,
                    itemBuilder: (context, indext) {
                      return RecentPointsCell(
                          LocaleKeys.shared_bandwidth.tr,
                          // controller.rewardsController.listUserTierPoints[indext].pointsType.toString(),
                          controller.rewardsController.listUserTierPoints[indext].getCreatAt,
                          controller.rewardsController.listUserTierPoints[indext].getPoint);
                    })
                : const NoDataWidget()),
        // RecentPointsCell(controller.rewardsController.listUserTierPoints.value, '21/01/2023 - 13:30', 12),
        // RecentPointsCell('Complete name of tasks', '21/01/2023 - 13:30', 12),
        // RecentPointsCell('Shared bandwidth 10.38 GB', '21/01/2023 - 13:30', 12),
        // RecentPointsCell('Complete name of tasks', '21/01/2023 - 13:30', 12),
      ],
    );
  }

  // ignore: non_constant_identifier_names
  Widget RecentPointsCell(String title, String sub, String points) {
    return Padding(
      padding: const EdgeInsets.only(top: 12, bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SvgPicture.asset(
                AppImages.icon_wifi_recent_points,
                width: 40,
              ),
              const SizedBox(
                width: 8,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xffFFFFFF),
                    ),
                  ),
                  const SizedBox(
                    height: 2,
                  ),
                  Text(
                    sub,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xff65636F),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: const Color(0xFF2D2C3B),
            ),
            child: GradientText(
              points,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
              gradientType: GradientType.linear,
              gradientDirection: GradientDirection.rtl,
              colors: points.toString().contains('-')
                  ? const [
                      Colors.red,
                      Colors.red,
                    ]
                  : const [
                      Color(0xFF1AF7A9),
                      AppColor.primaryBlue500,
                    ],
            ),
          ),
        ],
      ),
    );
  }

  Widget rankingInfor(String iconRank, String title, String sub, Color colorTitle) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SvgPicture.asset(
          iconRank,
          width: 36,
        ),
        const SizedBox(
          width: 8,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: Get.width * 0.25),
              child: Text(
                title,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: colorTitle,
                ),
              ),
            ),
            const SizedBox(
              height: 2,
            ),
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: Get.width * 0.25),
              child: Text(
                sub,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xffA7A4A4),
                ),
              ),
            ),
          ],
        )
      ],
    );
  }
}
