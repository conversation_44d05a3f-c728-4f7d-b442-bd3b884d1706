// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart' show RefreshController;
import 'package:u2u_dpn/app/modules/connection/controllers/connection_controller.dart';
import 'package:u2u_dpn/app/modules/referrals/controllers/referrals_controller.dart';
import 'package:u2u_dpn/app/modules/rewards/controllers/rewards_controller.dart';
import 'package:u2u_dpn/app_state.dart';
import 'package:u2u_dpn/data/app_controller.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:upgrader/upgrader.dart';

import '../../../../utils/app_utils.dart';
import '../controllers/dash_board_controller.dart';

class DashBoardView extends StatefulWidget {
  const DashBoardView({super.key});

  @override
  State<DashBoardView> createState() => _DashBoardViewState();
}

class _DashBoardViewState extends State<DashBoardView> {
  final controller = Get.find<DashBoardController>();
  final rewardsController = Get.find<RewardsController>();
  final appController = Get.find<AppController>();
  final connectionController = Get.find<ConnectionController>();
  final referralsController = Get.put<ReferralsController>(ReferralsController());
  @override
  void initState() {
    super.initState();
    if (AppState.instance.settingBox.read(SettingType.referral.toString()) != null &&
        AppState.instance.settingBox.read(SettingType.referral.toString()) != '') {
      if (!referralsController.checkNullReferredBy()) {
        referralsController.linkReferralcontroller.value.text = AppState.instance.settingBox.read(SettingType.referral.toString());
        referralsController.linkReferral(ishowDialog: false);
        AppState.instance.settingBox.remove(SettingType.referral.toString());
      }
    }

    // rewardsController.reloadOverview();

    connectionController.refreshController = RefreshController(initialRefresh: false);
  }

  @override
  void dispose() {
    super.dispose();
  }

  getOverview() async {
    await rewardsController.overview();
  }

  DateTime? currentBackPressTime;

  Future<bool> onWillPop(BuildContext context) {
    var now = DateTime.now();
    if (currentBackPressTime == null || now.difference(currentBackPressTime!) > const Duration(seconds: 2)) {
      currentBackPressTime = now;
      //Utils.showSnackbar(context, LocaleKeys.press_again_to_exit.tr);
      Get.snackbar(
        '',
        '',
        titleText: const SizedBox(height: 3),
        messageText: Text(
          LocaleKeys.press_again_to_exit.tr,
          textAlign: TextAlign.center,
          style: const TextStyle(color: Colors.white),
        ),
        padding: const EdgeInsets.only(right: 16, left: 16, bottom: 8),
        margin: const EdgeInsets.only(right: 16, left: 16, bottom: 32),
        //maxWidth: 200,
        backgroundColor: Colors.grey.withOpacity(0.6),
        snackPosition: SnackPosition.BOTTOM,
      );
      return Future.value(false);
    }
    SystemNavigator.pop();
    return Future.value(true);
  }

  @override
  Widget build(BuildContext context) {
    // getOverview();
    return Obx(() => WillPopScope(
          onWillPop: () => onWillPop(context),
          child: UpgradeAlert(
            showIgnore: false, // ❌ Ẩn nút "Bỏ qua"
            showLater: false, // ❌ Ẩn nút "Để sau"
            shouldPopScope: () => false, // 🚫 Không cho người dùng back
            barrierDismissible: false, // 🚫 Không thể dismiss bằng tap ngoài
            upgrader: Upgrader(
              durationUntilAlertAgain: const Duration(seconds: 3),
            ),
            child: Scaffold(
              // appBar: AppBar(
              //   title: const Text('DashBoardView'),
              //   centerTitle: true,
              // ),
              // backgroundColor: Colors.transparent,
              body: SettingConfig.showsTask
                  ? IndexedStack(
                      index: controller.selectIndex.value,
                      children: controller.widgetOptions,
                    )
                  : IndexedStack(
                      index: controller.selectIndex.value,
                      children: controller.widgetOptionsHideTask,
                    ),
              bottomNavigationBar: BottomNavigationBar(
                items: [
                  BottomNavigationBarItem(
                      icon: SvgPicture.asset(AppImages.connection),
                      activeIcon: SvgPicture.asset(
                        AppImages.connection,
                        color: Colors.white,
                      ),
                      label: "DPN Subnet"),
                  // if (SettingConfig.showsTask)
                  //   BottomNavigationBarItem(
                  //     icon: SvgPicture.asset(
                  //       AppImages.my_task,
                  //       color: AppColor.neutral400,
                  //     ),
                  //     activeIcon: SvgPicture.asset(
                  //       AppImages.my_task,
                  //       color: Colors.white,
                  //     ),
                  //     label: "My Tasks",
                  //   ),
                  // BottomNavigationBarItem(
                  //     icon: SvgPicture.asset(AppImages.news),
                  //     activeIcon: SvgPicture.asset(
                  //       AppImages.news,
                  //       color: Colors.white,
                  //     ),
                  //     label: "News"),

                  BottomNavigationBarItem(
                      icon: SvgPicture.asset(AppImages.wallet),
                      activeIcon: SvgPicture.asset(
                        AppImages.wallet,
                        color: Colors.white,
                      ),
                      label: LocaleKeys.wallet.tr),
                  BottomNavigationBarItem(
                      icon: SvgPicture.asset(AppImages.referrals),
                      activeIcon: SvgPicture.asset(
                        AppImages.referrals,
                        color: Colors.white,
                      ),
                      label: LocaleKeys.referrals.tr),
                  // BottomNavigationBarItem(
                  //     icon: SvgPicture.asset(
                  //       AppImages.icon_setting,
                  //       color: AppColor.neutral400,
                  //     ),
                  //     activeIcon: SvgPicture.asset(
                  //       AppImages.icon_setting,
                  //       color: Colors.white,
                  //     ),
                  //     label: LocaleKeys.setting.tr),
                ],
                backgroundColor: AppColor.neutral900,
                unselectedItemColor: AppColor.neutral400,
                selectedIconTheme: const IconThemeData(color: Colors.white),
                type: BottomNavigationBarType.fixed,
                selectedItemColor: Colors.white,
                selectedLabelStyle: const TextStyle(fontSize: 12.5, fontWeight: FontWeight.w500),
                unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 12),
                currentIndex: controller.selectIndex.value,
                onTap: (value) {
                  controller.selectIndex.value = value;
                  switch (value) {
                    case 0:
                      connectionController.onRefresh();
                      break;
                    case 1:
                      rewardsController.onRefresh();
                    case 2:
                      referralsController.onRefresh();
                      break;
                    default:
                  }
                },
              ),
            ),
          ),
        ));
  }
}
