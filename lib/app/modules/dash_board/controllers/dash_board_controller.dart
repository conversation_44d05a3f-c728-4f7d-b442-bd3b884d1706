import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:u2u_dpn/app/modules/connection/views/connection_view.dart';
// import 'package:u2u_dpn/app/modules/news/views/news_view.dart';
//import 'package:u2u_dpn/app/modules/home/<USER>/home_view.dart';
import 'package:u2u_dpn/app/modules/referrals/views/referrals_view.dart';
import 'package:u2u_dpn/app/modules/rewards/views/rewards_view.dart';

class DashBoardController extends GetxController {
  final selectIndex = 0.obs;



  List<Widget> widgetOptions = <Widget>[
    const ConnectionView(),
    //HomeView(),
    // NewsView(),

    const RewardsView(),
    const ReferralsView(),
    // const SettingView(),
  ].obs;
  List<Widget> widgetOptionsHideTask = <Widget>[
    const ConnectionView(),
    // NewsView(),

    const RewardsView(),
    const ReferralsView(),
    // const SettingView(),
  ].obs;
  final count = 0.obs;

  void increment() => count.value++;
}
