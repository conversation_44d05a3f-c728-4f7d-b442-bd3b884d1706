import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:u2u_dpn/app/modules/referrals/controllers/referrals_controller.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/widget/custom_raisebutton.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
import 'package:u2u_dpn/widget/ultil.dart';

import '../../../../utils/app_utils.dart';
import '../../../../widget/custom_hide_text.dart';
import '../../../../widget/dialog/process_dialog.dart';

class LinkReferral extends GetView<ReferralsController> {
  const LinkReferral({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Stack(
        children: [
          SingleChildScrollView(
            // ignore: prefer_const_constructors
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              top: 16,
              bottom: 100, // chừa chỗ cho button cố định bên dưới
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: Get.width * 0.8),
                      child: Text(LocaleKeys.link_referral_code.tr,
                          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: Color(0xFFFFFFFF))),
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.pop(context);
                        controller.linkReferralcontroller.value.text = '';
                      },
                      child: SvgPicture.asset(AppImages.icon_close),
                    )
                  ],
                ),
                const SizedBox(
                  height: 12,
                ),
                Stack(
                  children: [
                    Obx(() => TextFormField(
                          maxLength: 50,
                          controller: controller.linkReferralcontroller.value,
                          cursorColor: const Color(0xFF1AF7A9),
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Color(0xFFFFFFFF)),
                          maxLines: null,
                          onChanged: (value) {
                            controller.changeHintButton();
                          },
                          decoration: InputDecoration(
                            counterText: '',
                            hintText: LocaleKeys.enter_referral_code.tr,
                            hintStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Color(0xFF383846)),
                            enabledBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(Radius.circular(8)),
                              borderSide: BorderSide(color: Color(0xFF120E21), width: 0.6),
                            ),
                            focusedBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(Radius.circular(8)),
                              borderSide: BorderSide(color: Color(0xFF120E21)),
                            ),
                            contentPadding: const EdgeInsets.symmetric(vertical: 25, horizontal: 25.0),
                            filled: true,
                            fillColor: const Color(0xFF120E21),
                            suffixIcon: const SizedBox(
                              height: 24,
                              width: 100,
                            ),
                          ),
                        )),
                    Positioned(
                      right: 16,
                      top: 5,
                      child: InkWell(
                        child: IntrinsicHeight(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 16, bottom: 16),
                            child: Container(
                              padding: const EdgeInsets.only(left: 16, right: 16, top: 5, bottom: 5),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                color: const Color(0xFF2D2C3B),
                              ),
                              child: Text(
                                LocaleKeys.paste.tr,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                        onTap: () async {
                          var data = await Clipboard.getData("text/plain");
                          if (data?.text?.isNotEmpty ?? false) {
                            controller.linkReferralcontroller.value.text = data!.text!.trim();
                            controller.changeHintButton();
                            // ignore: use_build_context_synchronously
                            FocusScope.of(context).unfocus();
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 12,
                ),
                SizedBox(
                  width: Get.width - 32,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Icon(
                        Icons.brightness_1,
                        size: 4,
                        color: Color(0xFF65636F),
                      ),
                      const SizedBox(
                        width: 16,
                      ),
                      Expanded(
                        child: CustomText(
                          text: LocaleKeys.you_can_only.tr,
                          maxLine: 10,
                          color: AppColor.neutral400,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 24,
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 16, // né bàn phím
            left: 16,
            right: 16,
            child: Obx(() => CostomRaisedButtom(
                  isHintButton: controller.isHintButton.value,
                  name: LocaleKeys.save.tr,
                  function: () {
                    if (controller.referralCodeValue.value == controller.linkReferralcontroller.value.text) {
                      Utils.showSnackbar(context, LocaleKeys.you_cannot_enter_your_referral_code.tr);
                    } else {
                      FocusManager.instance.primaryFocus!.unfocus();

                      Get.back();
                      ProgressDialog.showDialogNotification(
                          onPressed: () {
                            controller.linkReferral();
                          },
                          onPressedCancell: () {
                            Get.back();
                            controller.linkReferralcontroller.value.text = '';
                          },
                          content: dialogRewardClaim());
                    }
                  },
                )),
          ),
        ],
      ),
    );
  }

  Widget dialogTransactionsIsCompleted() {
    return Column(
      children: [
        Image.asset(
          AppImages.icon_done,
          width: 104,
        ),
        const SizedBox(
          height: 8,
        ),
        Text(
          LocaleKeys.you_has_successfully.tr,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color(0xFFFFFFFF),
          ),
        ),
      ],
    );
  }

  Widget dialogRewardClaim() {
    return Column(
      children: [
        Text(
          LocaleKeys.are_you_sure_claim.tr,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFFDEDEDE),
          ),
        ),
        const SizedBox(
          height: 24,
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
          decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFF65636F),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(16),
              color: const Color(0xff383846)),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                AppImages.icon_infor_code,
                width: 24,
              ),
              const SizedBox(
                width: 4,
              ),
              SvgPicture.asset(
                AppImages.icon_connection,
                width: 18,
              ),
              const SizedBox(
                width: 4,
              ),
              Obx(() => CustomText(
                    text: CustomHideTex.hideMiddleText(controller.linkReferralcontroller.value.text.toString()),
                    textAlign: TextAlign.center,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  )),
            ],
          ),
        ),
      ],
    );
  }
}
