// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/referrals/views/create_referral.dart';
import 'package:u2u_dpn/app/modules/referrals/views/link_referral.dart';
import 'package:u2u_dpn/app/modules/referrals/views/referrals_history.dart';
import 'package:u2u_dpn/app/routes/app_pages.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/dialog/process_dialog.dart';
import 'package:u2u_dpn/widget/loading.dart';
import 'package:u2u_dpn/widget/ultil.dart';
import '../../../../widget/CustomContainerShadow.dart';
import '../../../../widget/widget.dart';
import '../controllers/referrals_controller.dart';

class ReferralsView extends StatefulWidget {
  const ReferralsView({Key? key}) : super(key: key);

  @override
  State<ReferralsView> createState() => _ReferralsState();
}

class _ReferralsState extends State<ReferralsView> {
  final controller = Get.find<ReferralsController>();

  @override
  void initState() {
    controller.onRefresh();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFF1B1A28),
        body: Container(
            width: double.maxFinite,
            height: double.maxFinite,
            decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AppImages.bg_white), fit: BoxFit.cover)),
            child: _buildContent(context)));
  }

  // Widget _buildBackground() {
  //   return Image.asset(
  //     AppImages.bg_white,
  //     width: Get.width,
  //     fit: BoxFit.contain,
  //   );
  // }

  Widget _buildContent(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        await controller.onRefresh();
      },
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          const SizedBox(height: 45),
          _buildHeader(),
          const SizedBox(height: 24),
          inforCard(context),
          referralCode(context),
          _buildInviteHistoryHeader(),
          historyReferral(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocaleKeys.referrals.tr,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w500,
                color: AppColor.neutral100,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInviteHistoryHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CustomText(
          text: LocaleKeys.invite_history.tr,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
        Row(
          children: [
            CustomText(
              text: LocaleKeys.view_all.tr,
              fontSize: 14,
              color: AppColor.primaryGreen700,
            ).onTap(() {
              Get.toNamed(Routes.REFERRALS_HISTORY);
              controller.historyReferral();
            }),
            const Icon(
              Icons.arrow_forward_ios,
              size: 12,
              color: AppColor.primaryGreen700,
            ).paddingOnly(left: 4),
          ],
        ),
      ],
    ).paddingOnly(top: 20, bottom: 30);
  }

  Widget inforCard(BuildContext context) {
    return CustomContainerShadow(
      child: Obx(
        () => Column(
          children: [
            linkCode(),
            const Divider(height: 40, color: AppColor.neutral600),
            inforTotal(),
            const Divider(height: 40, color: AppColor.neutral600),
            _buildCommissionClaimable(),
          ],
        ),
      ),
    );
  }

  Widget linkCode() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              text: LocaleKeys.link_code.tr,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppColor.neutral300,
            ).paddingOnly(bottom: 8),
            Row(
              children: [
                SvgPicture.asset(AppImages.linkAdress, width: 18),
                8.width,
                (!controller.isEeferredBy.value)
                    ? const Text(
                        '--------',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppColor.neutral400,
                          letterSpacing: 5,
                        ),
                      )
                    : Text(
                        Utils.shortenUsername(
                          controller.appController.userDetailModel.value.userReferral?.referredBy ?? '...',
                          7,
                          showlastString: true,
                        ),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppColor.neutral200,
                          letterSpacing: 3,
                        ),
                      ),
              ],
            ),
          ],
        ),
        Obx(() {
          if (!controller.isEeferredBy.value||controller.appController.userDetailModel.value.userReferral?.referredBy==null) {
            return InkWell(
              onTap: () => linkReferral(context),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  gradient: AppColor.customLineGradientBlack,
                ),
                child: Text(
                  LocaleKeys.link_with_your_friends.tr,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ).paddingSymmetric(vertical: 16);
          }
          return const SizedBox(height: 16);
        }),
      ],
    );
  }

  Widget referralCode(BuildContext context) {
    return Obx(() => (controller.referralCodeValue.value != '')
        ? CustomContainerShadow(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocaleKeys.your_code.tr,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: AppColor.neutral400,
                      ),
                    ).paddingOnly(bottom: 4),
                    Obx(() => Text(
                          controller.referralCodeValue.value == '' ? '--------' : controller.referralCodeValue.value,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppColor.neutral200,
                            letterSpacing: 3,
                          ),
                        )),
                  ],
                ),
                shareReferralsCode(),
              ],
            ),
          ).paddingOnly(top: 26, bottom: 16)
        : CostomRaisedButtom(
            name: LocaleKeys.create_referral_code.tr,
            function: () {
              CustomBottomSheet.showModalNotFullScreenWithHeight(
                context,
                CreateReferral(),
                height: MediaQuery.of(context).size.height * 0.6,
                isUnfocus: true,
              );
            },
          ).paddingOnly(top: 26, bottom: 16));
  }

  Widget inforTotal() {
    final model = controller.referralOverviewModel.value;

    return Row(
      children: [
        Expanded(
          child: _buildInfoColumn(
            title: LocaleKeys.total_invited.tr,
            value: (model.totalReferees ?? 0).toString(),
          ),
        ),
        Expanded(
          child: _buildInfoColumn(
            title:
                //  hasCommissionTxs
                //     ? LocaleKeys.total_commission_transaction.tr
                //     :
                LocaleKeys.total_referee_transactions.tr,
            value:
                //  hasCommissionTxs
                //     ? model.getTotalCommisionTxs
                //     :
                model.getTotalCommisionTxs,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoColumn({required String title, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppColor.neutral300,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.w600,
            color: AppColor.neutral100,
          ),
        ),
      ],
    );
  }

  Widget _buildCommissionClaimable() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ConstrainedBox(
          constraints: BoxConstraints(maxWidth: Get.width * 0.45),
          child: Text(
            LocaleKeys.referral_rewards.tr,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppColor.neutral300,
            ),
          ),
        ),
        Row(
          children: [
            Row(
              children: [
                Image.asset(AppImages.logoU2U, width: 20).paddingOnly(right: 10),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: Get.width * 0.25),
                  child: CustomText(
                    text: controller.referralOverviewModel.value.getUnclaimedCommissionV2.toString(),
                    fontSize: 16,
                    textAlign: TextAlign.right,
                    fontWeight: FontWeight.w500,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const Spacer(),
            const CustomText(text: 'U2U', fontSize: 16, fontWeight: FontWeight.w500, color: AppColor.neutral400),
          ],
        ).paddingTop(16),
        Row(
          children: [
            Row(
              children: [
                Image.asset(AppImages.logo, width: 20).paddingOnly(right: 10),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: Get.width * 0.25),
                  child: CustomText(
                    text: controller.referralOverviewModel.value.getUnclaimedCommission.toString(),
                    fontSize: 16,
                    textAlign: TextAlign.right,
                    fontWeight: FontWeight.w500,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const Spacer(),
            const CustomText(text: 'U2DPN', fontSize: 16, fontWeight: FontWeight.w500, color: AppColor.neutral400),
          ],
        ).paddingTop(16),
      ],
    );
  }

  InkWell shareReferralsCode() {
    return InkWell(
      onTap: () async {
        if (controller.linkReferralCodeValue.value == '') {
          controller.createDynamicLink;
        }

        if (controller.linkReferralCodeValue.value != '') {
          ProgressDialog.showDialogQRcode(
            context,
            controller.referralCodeValue.value,
            controller.linkReferralCodeValue.value,
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          gradient: AppColor.customLineGradientGreen,
        ),
        child: Text(
          LocaleKeys.share.tr,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColor.neutral800,
          ),
        ),
      ),
    );
  }

  historyReferral() {
    controller.historyReferral();
    return Obx(
      () => controller.isloading.value
          ? LoadingScreen(height: Get.height - 300)
          : controller.listReferralHistory.isNotEmpty
              ? Column(
                  children: controller.listReferralHistory
                      .take(5)
                      .map((item) => ReferralHistoryItem(referralHistory: item).paddingOnly(bottom: 28))
                      .toList(),
                )
              : CustomNodata(textLine2: ''),
    ).paddingSymmetric(horizontal: 16);
  }

  void linkReferral(BuildContext context) {
    CustomBottomSheet.showModalNotFullScreenWithHeight(
      context,
      const LinkReferral(),
      height: MediaQuery.of(context).size.height * 0.5,
    );
  }
}
