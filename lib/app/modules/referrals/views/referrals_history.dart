import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/referrals/controllers/referrals_controller.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/model/referral_history_model.dart';
// import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_utils.dart';
import 'package:u2u_dpn/widget/loading.dart';
// import 'package:u2u_dpn/widget/custom_nodata.dart';
import 'package:u2u_dpn/widget/ultil.dart';
import 'package:u2u_dpn/widget/widget.dart';

class ReferralsHistoryView extends GetView<ReferralsController> {
  const ReferralsHistoryView({super.key});

  @override
  Widget build(BuildContext context) {
    //controller.historyReferral();
    return Scaffold(
 
     
        body: Container(
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(AppImages.bg_white),
              fit: BoxFit.cover,
            ),
          ),
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                IconButton(onPressed: () => Get.back(), icon: const Icon(
            Icons.arrow_back_rounded,
            color: Colors.white,
            size: 24,
          ))     .paddingRight(10),
                      CustomText(
                        text: LocaleKeys.invite_history.tr,
                        fontSize: 24,
                      ),
                    ],
                  ).paddingOnly(bottom: 24, top: 60),
                  Obx(() => controller.isloading.value
                      ? LoadingScreen(height: Get.height - 300)
                      : Expanded(
                          child: controller.listReferralHistory.isNotEmpty
                              ? ListView.builder(
                                  itemCount: controller.listReferralHistory.length,
                                  padding: const EdgeInsets.only(top: 10),
                                  itemBuilder: (context, index) {
                                    return ReferralHistoryItem(referralHistory:  controller.listReferralHistory[index]).paddingOnly(bottom: 28);
                                  })
                              : Center(child: CustomNodata(textLine2: ''))))
                  // refferalsHistoryItems(),
                ],
              ).paddingSymmetric(horizontal: 16),
            )
          ]),
        ));
  }

}

class ReferralHistoryItem extends StatelessWidget {
  final ReferralHistoryModel referralHistory;

  const ReferralHistoryItem({
    Key? key,
    required this.referralHistory,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CircleAvatar(
          backgroundColor: AppColor.neutral700,
          radius: 20,
          child: SvgPicture.asset(
            AppImages.linkAdress,
            width: 18,
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomText(
              text: Utils.shortenUsername(
                referralHistory.userAddr,
                8,
                showlastString: true,
              ),
              fontSize: 18,
            ),
            CustomText(
              text: Utils.convertTimestamptoDatetime(
                referralHistory.referredAt ?? 0,
              ),
              fontWeight: FontWeight.w400,
              color: AppColor.neutral400,
            )
          ],
        ).paddingOnly(left: 16),
        const Spacer(),
      ],
    );
  }
}
