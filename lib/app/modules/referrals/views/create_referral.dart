import 'package:flutter/material.dart';

import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import '../../../../utils/app_utils.dart';
import '../../../../widget/widget.dart';
import '../controllers/referrals_controller.dart';

class CreateReferral extends GetView<ReferralsController> {
  // ignore: prefer_const_constructors_in_immutables
  CreateReferral({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Stack(
        children: [
          SingleChildScrollView(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
              top: 16,
              bottom: 100, // chừa chỗ cho button cố định bên d<PERSON>
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ConstrainedBox(
                        constraints: BoxConstraints(maxWidth: Get.width * 0.8),
                        child: Text(LocaleKeys.create_ref_code.tr,
                            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: Color(0xFFFFFFFF)))),
                    InkWell(
                      onTap: () {
                        Navigator.pop(context);
                        controller.createReferralController.value.text = '';
                      },
                      child: SvgPicture.asset(AppImages.icon_close),
                    )
                  ],
                ),
                const SizedBox(
                  height: 12,
                ),
                Stack(
                  children: [
                    Obx(() => TextFormField(
                          maxLength: 50,
                          controller: controller.createReferralController.value,
                          cursorColor: AppColor.primaryGreen500,
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Color(0xFFFFFFFF)),
                          maxLines: null,
                          onChanged: (value) {
                            controller.changeHintButtonCreateAdress();
                          },
                          decoration: InputDecoration(
                            counterText: '',
                            hintText: LocaleKeys.enter_referral_code.tr,
                            hintStyle: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF383846),
                            ),
                            enabledBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(Radius.circular(8)),
                              borderSide: BorderSide(color: Color(0xFF120E21), width: 0.6),
                            ),
                            focusedBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(Radius.circular(8)),
                              borderSide: BorderSide(color: Color(0xFF120E21)),
                            ),
                            contentPadding: const EdgeInsets.symmetric(vertical: 25, horizontal: 25.0),
                            filled: true,
                            fillColor: const Color(0xFF120E21),
                            suffixIcon: const SizedBox(
                              height: 24,
                              width: 100,
                            ),
                          ),
                        )),
                    // Positioned(
                    //   right: 16,
                    //   top: 5,
                    //   child: InkWell(
                    //     child: IntrinsicHeight(
                    //       child: Padding(
                    //         padding: EdgeInsets.only(top: 16, bottom: 16),
                    //         child: Container(
                    //           padding: EdgeInsets.only(left: 16, right: 16, top: 5, bottom: 5),
                    //           decoration: BoxDecoration(
                    //             borderRadius: BorderRadius.circular(16),
                    //             color: const Color(0xFF2D2C3B),
                    //           ),
                    //           child: const Text(
                    //             'Generate',
                    //             style: TextStyle(
                    //               color: AppColor.neutral200,
                    //               fontSize: 14,
                    //               fontWeight: FontWeight.w500,
                    //             ),
                    //           ),
                    //         ),
                    //       ),
                    //     ),
                    //     onTap: () async {
                    //       var data = await Clipboard.getData("text/plain");
                    //       if (data?.text?.isNotEmpty ?? false) {
                    //         controller.createReferralController.value.text = data!.text!.trim();
                    //         // ignore: use_build_context_synchronously
                    //         FocusScope.of(context).unfocus();
                    //       }
                    //     },
                    //   ),
                    // ),
                  ],
                ),
                SizedBox(
                    width: Get.width - 32,
                    child: Column(
                      children: [
                        textWithDots(
                          text: LocaleKeys.type_in_any_letters.tr,
                        ),
                        textWithDots(text: LocaleKeys.referral_code_must.tr).paddingSymmetric(vertical: 8),
                        textWithDots(text: LocaleKeys.make_sure_your_referral_code.tr),
                      ],
                    )).paddingOnly(top: 12, bottom: 24),
              ],
            ),
          ),
          Positioned(
            bottom: 16, // né bàn phím
            left: 16,
            right: 16,
            child: Obx(() => CostomRaisedButtom(
                  isHintButton: controller.isHintButtonCreateWallet.value,
                  name: LocaleKeys.create_and_save.tr,
                  function: () {
                    FocusManager.instance.primaryFocus!.unfocus();
                    controller.createReferralCode(context);
                  },
                )),
          ),
        ],
      ),
    );
  }

  Row textWithDots({
    required String text,
    // bool check = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Icon(
          Icons.brightness_1,
          size: 4,
          color: Color(0xFF65636F),
        ),
        const SizedBox(
          width: 16,
        ),
        Expanded(
          child: CustomText(
            text: text,
            maxLine: 2,
            color: AppColor.neutral400,
          ),
        ),
      ],
    );
  }
}
