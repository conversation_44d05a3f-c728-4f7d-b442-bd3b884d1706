// ignore_for_file: use_build_context_synchronously, deprecated_member_use

import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:u2u_dpn/app_state.dart';
import 'package:u2u_dpn/data/app_controller.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/model/referral_history_model.dart';
import 'package:u2u_dpn/model/referral_overview_model.dart';
import 'package:u2u_dpn/repository/api_repository.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/dialog/process_dialog.dart';
import 'package:u2u_dpn/widget/ultil.dart';

class ReferralsController extends GetxController {
  var createReferralController = TextEditingController().obs;
  var linkReferralcontroller = TextEditingController().obs;
  AppController appController = Get.find();
  // var createWalletAddress = TextEditingController().obs;
  var isHintButton = true.obs;
  var isHintButtonCreateWallet = true.obs;
  var referralCodeValue = SettingConfig.referralCode.obs;
  var linkReferralCodeValue = ''.obs;
  //  (appController.userDetailModel.value.userReferral != null ? appController.userDetailModel.value.userReferral!.referralCode : '').obs;
  var isCopied = false.obs;
  var referralOverviewModel = ReferralOverviewModel().obs;
  var listReferralHistory = <ReferralHistoryModel>[].obs;
  var isloading = false.obs;
  var isEeferredBy = false.obs;

  ApiRepository apiRepository = ApiRepository();



  void changeHintButton() {
    if (linkReferralcontroller.value.text.isEmpty) {
      isHintButton.value = true;
    } else {
      isHintButton.value = false;
    }
  }

  void changeHintButtonCreateAdress() {
    if (createReferralController.value.text.isEmpty) {
      isHintButtonCreateWallet.value = true;
      // lv1.value = false;
    } else {
      isHintButtonCreateWallet.value = false;
      // lv1.value = RegExp(r'^[a-zA-Z0-9]+$').hasMatch(createWalletAddress.value.text);
      // lv2.value = createWalletAddress.value.text.length < 16;
    }
  }

  void copyReferralCode(BuildContext context) {
    Clipboard.setData(ClipboardData(text: referralCodeValue.toString())).then((value) async {
      Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
      // isCopied.value = true;
      // await Future.delayed(const Duration(seconds: 5));
      // isCopied.value = false;
    });
  }

  void copyLinkReferralCode(BuildContext context) {
    Clipboard.setData(ClipboardData(text: linkReferralCodeValue.toString())).then((value) async {
      Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
      // isCopied.value = true;
      // await Future.delayed(const Duration(seconds: 5));
      // isCopied.value = false;
    });
  }

  Future<void> createDynamicLink() async {
    final dynamicLinkParams = DynamicLinkParameters(
      link: Uri.parse("https://u2dpn.xyz/referral=$referralCodeValue"),
      uriPrefix: "https://u2dpn.page.link",
      androidParameters: const AndroidParameters(packageName: "com.uniultra.u2udpn"),
      iosParameters: const IOSParameters(bundleId: "com.unicornultra.app.u2dpn", appStoreId: '6478145991'),
    );
    final dynamicLink = await FirebaseDynamicLinks.instance.buildShortLink(dynamicLinkParams);
    linkReferralCodeValue.value = dynamicLink.shortUrl.toString().trim();
    Get.log("dynamicLink: ${dynamicLink.shortUrl}");

    // Clipboard.setData(ClipboardData(text: dynamicLink.shortUrl.toString())).then((value) async {
    //   Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
    //   isCopied.value = true;
    //   await Future.delayed(const Duration(seconds: 5));
    //   isCopied.value = false;
    // });
  }

  Future<void> onRefresh() async {
    ProgressDialog.show(Get.context!);
    // monitor network fetch
    // await Future.delayed(const Duration(milliseconds: 1000));
    // if failed,use refreshFailed()
    referralOverview();
    historyReferral();
    await appController.getUserDetail();
    referralCodeValue.value = SettingConfig.referralCode;
    await createDynamicLink();
    ProgressDialog.hide(Get.context!);
  }

  void onLoading() async {
    // monitor network fetch
    await Future.delayed(const Duration(milliseconds: 500));
    // if failed,use loadFailed(),if no data return,use LoadNodata()
  }

  Future createReferralCode(BuildContext context) async {
    try {
      var res = await apiRepository.createCode(referralCode: createReferralController.value.text);
      if (res != null && res.statusCode == 200) {
        // create successfully
        SettingConfig.referralCode = createReferralController.value.text;

        ProgressDialog.show(context);
        referralCodeValue.value = createReferralController.value.text;
        Get.back();

        appController.getUserDetail();
        ProgressDialog.showDialogNotification(
            isShowCancel: false,
            onPressedCancell: () {
              Get.back();
            },
            content: dialogTransactionsIsCompleted());
        ProgressDialog.hide(context);
        createDynamicLink();
      } else {
        if (res != null) Utils.showSnackbar(context, res.data['err_msg'], isError: true);
      }

      createReferralController.value.text = '';
    } catch (e) {
      createReferralController.value.text = '';
    }
  }

  Future linkReferralDeepLink() async {
    try {
      var res = await apiRepository.linkCode(referralCode: linkReferralcontroller.value.text);
      if (res != null && (res.statusCode == 200 || res.statusCode == 201)) {
        // link

        isEeferredBy.value = true;
        Get.snackbar(
          '',
          '',
          titleText: const SizedBox(height: 3),
          messageText: const Text(
            'Your referral code was created succesfully!',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.white),
          ),
          padding: const EdgeInsets.only(right: 16, left: 16, bottom: 8),
          margin: const EdgeInsets.only(right: 16, left: 16, bottom: 32),
          //maxWidth: 200,
          backgroundColor: Colors.green.withOpacity(0.6),
          snackPosition: SnackPosition.BOTTOM,
        );
        // Future.delayed(const Duration(seconds: 1), () {
        //   onRefresh();
        // });
      } else {
        if (res != null) {
          isEeferredBy.value = false;
          //Utils.showSnackbar(context, res.data['err_msg'], isError: true);
          Get.snackbar(
            '',
            '',
            titleText: const SizedBox(height: 3),
            messageText: Text(
              res.data['err_msg'],
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.white),
            ),
            padding: const EdgeInsets.only(right: 16, left: 16, bottom: 8),
            margin: const EdgeInsets.only(right: 16, left: 16, bottom: 32),
            //maxWidth: 200,
            backgroundColor: Colors.red.withOpacity(0.6),
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      }
      linkReferralcontroller.value.text = '';
      AppState.instance.settingBox.remove(SettingType.referral.toString());
    } catch (e) {
      isEeferredBy.value = false;
      logger.d(e);
      Get.snackbar(
        '',
        '',
        titleText: const SizedBox(height: 3),
        messageText: Text(
          e.toString(),
          textAlign: TextAlign.center,
          style: const TextStyle(color: Colors.white),
        ),
        padding: const EdgeInsets.only(right: 16, left: 16, bottom: 8),
        margin: const EdgeInsets.only(right: 16, left: 16, bottom: 32),
        //maxWidth: 200,
        backgroundColor: Colors.red.withOpacity(0.6),
        snackPosition: SnackPosition.BOTTOM,
      );
      linkReferralcontroller.value.text = '';
      AppState.instance.settingBox.remove(SettingType.referral.toString());
    }
  }

  Future linkReferral({bool ishowDialog = true}) async {
    try {
      var res = await apiRepository.linkCode(referralCode: linkReferralcontroller.value.text);
      if (res != null && (res.statusCode == 200 || res.statusCode == 201)) {
        // link
        Get.back();
        isEeferredBy.value = true;
        if (ishowDialog) {
          ProgressDialog.showDialogNotification(
              isShowCancel: false,
              onPressedCancell: () {
                Get.back();
              },
              content: dialogTransactionsIsCompleted());
        }
        Future.delayed(const Duration(seconds: 1), () {
          onRefresh();
        });
      } else {
        if (res != null) {
          Get.back();
          isEeferredBy.value = false;
          //Utils.showSnackbar(context, res.data['err_msg'], isError: true);
          Get.snackbar(
            '',
            '',
            titleText: const SizedBox(height: 3),
            messageText: Text(
              res.data['err_msg'],
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.white),
            ),
            padding: const EdgeInsets.only(right: 16, left: 16, bottom: 8),
            margin: const EdgeInsets.only(right: 16, left: 16, bottom: 32),
            //maxWidth: 200,
            backgroundColor: Colors.red.withOpacity(0.6),
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      }
      linkReferralcontroller.value.text = '';
    } catch (e) {
      isEeferredBy.value = false;
      logger.d(e);
      linkReferralcontroller.value.text = '';
    }
  }

  Future referralOverview() async {
    try {
      var res = await apiRepository.referralOverview();
      checkNullReferredBy();
      if (res != null && res.statusCode == 200) {
        // link
        logger.d(res.data['ip']);
        logger.d(res.data['port']);
        referralOverviewModel.value = ReferralOverviewModel.fromJson(res.data);
      }
    } catch (e) {
      logger.d(e);
    }
  }

  Future historyReferral() async {
    try {
      isloading.value = true;
      var res = await apiRepository.history();
      if (res != null && res.statusCode == 200) {
        listReferralHistory.value = (res.data as List).map((e) => ReferralHistoryModel.fromJson(e)).toList();
      }
      isloading.value = false;
    } catch (e) {
      isloading.value = false;
      Get.log('error: $e');
    }
  }

  Widget dialogTransactionsIsCompleted() {
    return Column(
      children: [
        Image.asset(
          AppImages.icon_done,
          width: 104,
        ),
        const SizedBox(
          height: 8,
        ),
        Text(
          LocaleKeys.your_referral_code_was.tr,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color(0xFFFFFFFF),
          ),
        ),
      ],
    );
  }

  bool checkNullReferredBy() {
    if (isEeferredBy.value == false) {
      if (appController.userDetailModel.value.userReferral?.referredBy != null) {
        return isEeferredBy.value = true;
      } else {
        return isEeferredBy.value = false;
      }
    } else {
      return isEeferredBy.value = true;
    }
  }
}
