import 'dart:async';

import 'package:flutter/material.dart';
//

import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/maintenance/views/dropdown_language.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_utils.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
import 'package:u2u_dpn/widget/info_comunity.dart';

import '../controllers/maintenance_controller.dart';

class MaintenanceView extends GetView<MaintenanceController> {
  const MaintenanceView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    controller.timer = Timer.periodic(const Duration(seconds: 5), (timer) async {
    await  controller.getHealthAfterClose();
    });
    return Scaffold(
      backgroundColor: AppColor.neutral900,
      body: Column(
        children: [
          Stack(
            children: [
              Image.asset(
                AppImages.img_maintenance,
                fit: BoxFit.fitWidth,
              ).paddingTop(20),
              ConstrainedBox(
                  constraints: BoxConstraints(maxHeight: Get.height - 35),
                  child: Column(
                    children: [
                      Align(
                        alignment: Alignment.topRight,
                        child: const DropdownButtomLocale().withWidth(190),
                      ),
                      const Spacer(),
                      CustomText(
                        text: LocaleKeys.application_is_under_maintenance.tr,
                        fontSize: 32,
                      ),
                      CustomText(
                        text: LocaleKeys.u2dpn_will_automatically.tr,
                        fontSize: 14,
                        color: AppColor.neutral300,
                      ).paddingSymmetric(vertical: 26),
                      const InfoComunity()
                    ],
                  ).paddingAll(16)),
            ],
          ).paddingTop(30),
          //  DropdownButtomLocale(),
        ],
      ),
    );
  }
}
