// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app_state.dart';
import 'package:u2u_dpn/utils/app_color.dart';

class DropdownButtomLocale extends StatefulWidget {
  const DropdownButtomLocale({Key? key}) : super(key: key);

  @override
  State<DropdownButtomLocale> createState() => _DropdownButtomLocaleState();
}

class _DropdownButtomLocaleState extends State<DropdownButtomLocale> {
  final List<LanguageItem> _listLanguageItems = [
    LanguageItem('Vietnamese', 'Tiếng Việt', 'assets/images/icon_flags_vn.png', const Locale('vi', 'VN')),
    LanguageItem('English', 'English', 'assets/images/icon_flags_en.png', const Locale('en', 'US')),
  ];

  LanguageItem _currentLanguage = LanguageItem('English', 'English', 'assets/images/icon_flags_en.png', const Locale('en', 'US'));

  updateLanguage(Locale locale) {
    // Get.back();
    Get.updateLocale(locale);
  }

  @override
  void initState() {
    switch (AppState.instance.settingBox.read(SettingType.locale.toString())) {
      case 'Vietnamese':
        _currentLanguage = LanguageItem('Vietnamese', 'Tiếng Việt', 'assets/images/icon_flags_vn.png', const Locale('vi', 'VN'));
        break;
      case 'English':
        _currentLanguage = LanguageItem('English', 'English', 'assets/images/icon_flags_en.png', const Locale('en', 'US'));
        break;
        case 'Japanese':
        _currentLanguage = LanguageItem('Japanese', '日本語', 'assets/images/icon_flags_jp.png', const Locale('ja', 'JP'));
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        PopupMenuButton(
            color: AppColor.neutral100,
            elevation: 3.0,
            child: Container(
              padding: const EdgeInsets.only(left: 16, top: 5, bottom: 5, right: 10),
              decoration: BoxDecoration(color: AppColor.neutral600.withOpacity(0.5), borderRadius: const BorderRadius.all(Radius.circular(20))),
              child: Row(
                children: [
                  // Image.asset(_currentLanguage.iconPath, height: 25, width: 40),
                  // const SizedBox(width: 8),
                  Text(
                    _currentLanguage.name,
                    style: const TextStyle(color: AppColor.neutral100),
                  ).paddingRight(5),
                  const Icon(
                    Icons.keyboard_arrow_down,
                    color: AppColor.neutral400,
                    size: 15,
                  ),
                ],
              ),
            ),
            itemBuilder: (context) => _listLanguage()),
      ],
    );
  }

  List<PopupMenuItem<dynamic>> _listLanguage() {
    return _listLanguageItems
        .map((e) => PopupMenuItem(
              onTap: () {
                updateLanguage(e.locale);
                _currentLanguage = e;
                AppState.instance.settingBox.write(SettingType.locale.toString(), e.id);
              },
              child: Text(
                e.name,
              ),
            ))
        .toList();
  }
}

class LanguageItem {
  final String id;
  final String name;
  final String iconPath;
  final Locale locale;

  LanguageItem(this.id, this.name, this.iconPath, this.locale);
}
