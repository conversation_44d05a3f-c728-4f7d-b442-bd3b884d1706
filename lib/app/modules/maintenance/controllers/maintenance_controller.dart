import 'dart:async';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:u2u_dpn/app/routes/app_pages.dart';
import 'package:u2u_dpn/main.dart';

import '../../../../data/api_docs.dart';

class MaintenanceController extends GetxController {
 

  final count = 0.obs;
  Timer? timer;
  @override
  void onInit() {
    super.onInit();
    getHealth();
    timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      getHealth();
    });
  }



  void increment() => count.value++;
  Future getHealth() async {
    try {
      var response = await Dio().get(ApiDocs.convertURL(ApiDocs.getHealth)).timeout(const Duration(seconds: 40));
      logger.d(ApiDocs.convertURL(ApiDocs.getHealth));
      logger.d(await InternetConnectionChecker().hasConnection);
      if (response.statusCode != 200 && await InternetConnectionChecker().hasConnection) {
        Future.delayed(const Duration(milliseconds: 500));
        timer?.cancel();
        Future.delayed(const Duration(milliseconds: 500));
        if (Get.currentRoute != Routes.MAINTENANCE) Get.offAllNamed(Routes.MAINTENANCE);
      } else {}
    } on DioException catch (e) {
      if (e.response?.statusCode != 200 && await InternetConnectionChecker().hasConnection) {
        Future.delayed(const Duration(milliseconds: 500));
        timer?.cancel();
        Future.delayed(const Duration(milliseconds: 500));
        if (Get.currentRoute != Routes.MAINTENANCE) Get.offAllNamed(Routes.MAINTENANCE);
      }
    }
  }

  Future getHealthAfterClose() async {
    try {
      var response = await Dio().get(ApiDocs.convertURL(ApiDocs.getHealth)).timeout(const Duration(seconds: 40));
      logger.d(ApiDocs.convertURL(ApiDocs.getHealth));
      if (response.statusCode == 200) {
        Future.delayed(const Duration(milliseconds: 500));
        timer?.cancel();
        Future.delayed(const Duration(milliseconds: 500));
        timer = Timer.periodic(const Duration(seconds: 5), (timer) {
          getHealth();
        });
        Get.offAllNamed(Routes.DASH_BOARD);
      } else {}
    } on DioException catch (e) {
      if (e.response?.statusCode == 200) {
        Future.delayed(const Duration(milliseconds: 500));
        timer?.cancel();
        Future.delayed(const Duration(milliseconds: 500));
        timer = Timer.periodic(const Duration(seconds: 5), (timer) {
          getHealth();
        });
        Get.offAllNamed(Routes.DASH_BOARD);
      }
    }
  }
}
