import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:u2u_dpn/app/modules/notification/controllers/notification_controller.dart';
import 'package:u2u_dpn/model/notification_model.dart';
import 'package:u2u_dpn/utils/app_utils.dart';
import 'package:u2u_dpn/widget/custom_appbar.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

class DetailNotification extends GetView<NotificationController> {
  final NotificationModel notificationModel;
  const DetailNotification({super.key, required this.notificationModel});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.neutral800,
      appBar: CustomAppBarTitle(context),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            text: notificationModel.header,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
          CustomText(
            text: notificationModel.getDateTime,
            color: AppColor.neutral400,
          ).paddingSymmetric(vertical: 16),
          CustomText(
            text: notificationModel.content.toString(),
            color: AppColor.neutral300,
          )
        ],
      ).paddingAll(16),
    );
  }
}
