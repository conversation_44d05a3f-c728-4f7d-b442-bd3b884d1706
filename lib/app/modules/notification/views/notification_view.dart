// ignore_for_file: prefer_interpolation_to_compose_strings, deprecated_member_use

import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/notification/controllers/notification_controller.dart';
import 'package:u2u_dpn/app/modules/notification/views/detail_notification.dart';
import 'package:u2u_dpn/app/routes/app_pages.dart';
import 'package:u2u_dpn/app_state.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/custom_nodata.dart';

import '../../../../widget/custom_appbar.dart';
import '../../../../widget/custom_text.dart';

class NotificationView extends GetView<NotificationController> {
  const NotificationView({super.key});

  @override
  Widget build(BuildContext context) {
    controller.getNotificaion();
    return WillPopScope(
      onWillPop: () async {
        Get.offAllNamed(Routes.DASH_BOARD);
        return true;
      },
      child: Scaffold(
        backgroundColor: AppColor.neutral800,
        appBar: CustomAppBarTitle(
          context,
          function: () => Get.offAllNamed(Routes.DASH_BOARD),
          // actions: [
          //   Container(
          //     padding: const EdgeInsets.only(left: 12, right: 16, top: 8, bottom: 8),
          //     margin: const EdgeInsets.only(right: 16),
          //     decoration: BoxDecoration(color: AppColor.neutral600, borderRadius: BorderRadius.circular(16)),
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //       children: [
          //         const Icon(
          //           Icons.check,
          //           size: 18,
          //           color: AppColor.primaryGreen600,
          //         ).paddingOnly(right: 8),
          //         CustomText(
          //           text: LocaleKeys.mark_as_all_read.tr,
          //           fontSize: 14,
          //           fontWeight: FontWeight.w500,
          //         )
          //       ],
          //     ),
          //   ).onTap(() {
          //     ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Đang trong giai đoạn hoàn thành!')));
          //   })
          // ]
        ),
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: LocaleKeys.notification.tr,
                    fontWeight: FontWeight.w500,
                    fontSize: 32,
                  ),
                  Obx(() => Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(color: AppColor.neutral600, borderRadius: BorderRadius.circular(30)),
                          child: SvgPicture.asset(
                            AppImages.check_all_noti,
                            color: controller.isAll.value ? AppColor.neutral400 : AppColor.primaryGreen500,
                          )).onTap(() {
                        AppState.instance.settingBox.write(SettingType.rememberNotification.toString(), '');
                        for (var element in controller.listNotification) {
                          AppState.instance.settingBox.write(SettingType.rememberNotification.toString(),
                              AppState.instance.settingBox.read(SettingType.rememberNotification.toString()) + element.id + ',');
                        }
                        controller.isAll.value = true;
                        controller.getNotificaion();
                        //ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Đang trong giai đoạn hoàn thành!')));
                      }))
                ],
              ).paddingOnly(bottom: 24),
              Obx(() => controller.listNotification.isNotEmpty
                  ? ListView.builder(
                      shrinkWrap: true,
                      physics: const ClampingScrollPhysics(),
                      itemCount: controller.listNotification.length,
                      itemBuilder: (context, index) {
                        return notifyItem(
                                title: controller.listNotification[index].header ?? '',
                                content: controller.listNotification[index].content ?? '',
                                time: controller.listNotification[index].getDateTime,
                                isReaded: AppState.instance.settingBox
                                    .read(SettingType.rememberNotification.toString())
                                    .contains(controller.listNotification[index].id))
                            .onTap(() {
                          if (!AppState.instance.settingBox
                              .read(SettingType.rememberNotification.toString())
                              .contains(controller.listNotification[index].id)) {
                            AppState.instance.settingBox.write(
                                SettingType.rememberNotification.toString(),
                                AppState.instance.settingBox.read(SettingType.rememberNotification.toString()) +
                                    controller.listNotification[index].id +
                                    ',');
                          }
                          Get.to(() => DetailNotification(
                                notificationModel: controller.listNotification[index],
                              ))?.then((value) {
                            controller.getNotificaion();
                          });
                        });
                      })
                  : CustomNodata(textLine2: ''))
            ],
          ).paddingSymmetric(horizontal: 16),
        ),
      ),
    );
  }

  Container notifyItem({required String title, required String content, required String time, bool isReaded = false}) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(color: AppColor.neutral700, borderRadius: BorderRadius.circular(16)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
              padding: const EdgeInsets.all(11),
              margin: const EdgeInsets.only(right: 16),
              width: 40,
              height: 40,
              decoration: BoxDecoration(color: AppColor.neutral600, borderRadius: BorderRadius.circular(99)),
              child: SvgPicture.asset(
                AppImages.icon_bell,
                color: isReaded ? AppColor.neutral400 : Colors.white,
              )),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  overflow: TextOverflow.ellipsis,
                  text: title,
                  fontSize: 16,
                  maxLine: 1,
                  color: isReaded ? AppColor.neutral400 : AppColor.primaryGreen600,
                ).paddingBottom(6),
                // CustomText(
                //   text: content,
                //   fontSize: 14,
                //   maxLine: 2,
                //   overflow: TextOverflow.ellipsis,
                //   color: isReaded ? AppColor.neutral400 : AppColor.neutral200,
                // ).paddingSymmetric(vertical: 6),
                CustomText(
                  text: time,
                  fontSize: 12,
                  color: AppColor.neutral400,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
