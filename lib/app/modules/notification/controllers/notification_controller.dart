import 'package:get/get.dart';
import 'package:u2u_dpn/app_state.dart';
import 'package:u2u_dpn/main.dart';
import 'package:u2u_dpn/model/notification_model.dart';
import 'package:u2u_dpn/repository/api_repository.dart';
import 'package:u2u_dpn/utils/notification_service.dart';

class NotificationController extends GetxController {

  var isNotification = false.obs;
  RxList<NotificationModel> listNotification = <NotificationModel>[].obs;
  final count = 0.obs;
  ApiRepository apiRepository = ApiRepository();

  var isAll = true.obs;

  void increment() => count.value++;

  Future getNotificaion() async {
    try {
      // var res = await apiRepository.getListNotification();
      // logger.d(res!.data);
      // if (res.data != null) {
      //   listNotification.value = (res.data as List).map((e) => NotificationModel.fromJson(e)).toList();
      //   logger.d(listNotification);
      //   if (listNotification.isEmpty) {
      //     isAll.value = true;
      //   } else {
      //     for (var element in listNotification) {
      //       if (!AppState.instance.settingBox.read(SettingType.rememberNotification.toString()).contains(element.id)) {
      //         isAll.value = false;
      //         break;
      //       } else {
      //         isAll.value = true;
      //       }
      //     }
      //   }
      // }
    } catch (e) {
      logger.d(e);
    }
  }

  checkPushNotificaion() {
    if (listNotification.isEmpty) {
      for (var element in listNotification) {
        // ignore: unrelated_type_equality_checks
        if (element.level == 2 && !AppState.instance.settingBox.read(SettingType.pushNotification.toString()).contains(element.id)) {
          AppState.instance.settingBox.write(
              // ignore: prefer_interpolation_to_compose_strings
              SettingType.pushNotification.toString(), AppState.instance.settingBox.read(SettingType.pushNotification.toString()) + element.id + ',');
          NotificationService().newNotification(element.content ?? '...', false, id: element.createdAt, ongoing: false, autoCancel: true);
        }
      }
    }
  }
}
