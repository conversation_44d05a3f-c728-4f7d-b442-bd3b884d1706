import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:u2u_dpn/app/modules/dash_board/controllers/dash_board_controller.dart';
import 'package:u2u_dpn/app/modules/referrals/controllers/referrals_controller.dart';
import 'package:u2u_dpn/app/modules/rewards/controllers/rewards_controller.dart';
import 'package:u2u_dpn/app/modules/sign_in/controllers/sign_in_controller.dart';
import 'package:u2u_dpn/app_state.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/repository/api_repository.dart';
import 'package:u2u_dpn/widget/ultil.dart';

class SettingController extends GetxController {
  RewardsController rewardsController = Get.find();

  DashBoardController dashboardController = Get.find();
  var language = 'English'.obs;
  var isCopy = false.obs;
  var isSwitched = SettingConfig.autoConnection.obs;

  @override
  void onInit() {
    if (AppState.instance.settingBox.read(SettingType.locale.toString()) == 'Vietnamese') {
      language.value = 'Vietnamese';
    } else if (AppState.instance.settingBox.read(SettingType.locale.toString()) == 'Japanese') {
      language.value = 'Japanese';
    } else {
      language.value = 'English';
    }

    super.onInit();
  }

  @override
  void onReady() {}

  void logout() {
    SettingConfig.referralCode = '';
    SettingConfig.token = '';
    SettingConfig.refreshToken = '';
    SettingConfig.user_email = '...';
    SettingConfig.user_id = '0x0000000000000000000000000000000000000000';
    rewardsController.isLogout.value = true;
    AppState.instance.settingBox.remove('loyalty_point');
    AppState.instance.settingBox.remove(SettingType.referral.toString());
    Get.find<ReferralsController>().isEeferredBy.value = false;
    dashboardController.selectIndex.value = 0;
    Get.find<ReferralsController>().referralCodeValue.value = '';

    SignInController.signOutGoogle();
  }

  Future updateLanguageNotification(String language, BuildContext context) async {
    try {
      var res = await ApiRepository().updateLanguageNotification(language);
      if (res != null && res.data['statusCode'] == 200) {
        // ignore: use_build_context_synchronously
        await Utils.showSnackbar(context, LocaleKeys.update_language_success.tr);
        Get.back();
      }
    } catch (e) {
      // ignore: use_build_context_synchronously
      //  await Utils.showSnackbar(context, LocaleKeys.update_language_failed.tr);
      Get.back();
    }
  }
}
