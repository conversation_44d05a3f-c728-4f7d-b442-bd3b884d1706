import 'package:flutter/material.dart';

import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/setting/controllers/setting_controller.dart';
import 'package:u2u_dpn/app_state.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

class LaguageView extends GetView<SettingController> {
  const LaguageView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                        text: LocaleKeys.language.tr,
                        fontSize: 24,
                      ),
                      const Icon(
                        Icons.close,
                        size: 30,
                        color: AppColor.neutral400,
                      ).onTap(() {
                        Get.back();
                      })
                    ],
                  ).paddingOnly(bottom: 16),
                  Obx(() => languageCell(
                        'English',
                        () {
                          controller.language.value = 'English';
                          AppState.instance.settingBox
                              .write(SettingType.locale.toString(), 'English');
                          Get.updateLocale(const Locale('en', 'US'));
                          controller.updateLanguageNotification('en', context);
                        },
                        isCheck: controller.language.value == 'English',
                      )),
                  const SizedBox(
                    height: 16,
                  ),
                  Obx(() => languageCell(
                        'Tiếng Việt',
                        () {
                          controller.language.value = 'Vietnamese';
                          AppState.instance.settingBox
                              .write(SettingType.locale.toString(), 'Vietnamese');
                          Get.updateLocale(const Locale('vi', 'VN'));
                          controller.updateLanguageNotification('vi', context);
                        },
                        isCheck: controller.language.value == 'Vietnamese',
                      )),  const SizedBox(
                    height: 16,
                  ),
                  Obx(() => languageCell(
                        '日本語',
                        () {
                          controller.language.value = 'Japanese';
                          AppState.instance.settingBox
                              .write(SettingType.locale.toString(), 'Japanese');
                          Get.updateLocale(const Locale('ja', 'JP'));
                        //  controller.updateLanguageNotification('ja', context);
                        },
                      
                        isCheck: controller.language.value == 'Japanese',
                      ))
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget languageCell(String title, Function() function, {bool? isCheck = false}) {
    return InkWell(
      onTap: function,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          color: isCheck == true ? AppColor.neutral500 : AppColor.neutral700,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText(
              text: title,
              fontSize: 14,
            ),
            isCheck == true
                ? SvgPicture.asset(
                    AppImages.icon_check_language,
                    height: 24,
                  )
                : const SizedBox(
                    height: 24,
                  ),
          ],
        ),
      ),
    );
  }
}
