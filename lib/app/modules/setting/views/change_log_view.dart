import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_utils.dart';
import 'package:u2u_dpn/widget/custom_appbar.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

class ChangeLogView extends StatefulWidget {
  const ChangeLogView({super.key});

  @override
  State<ChangeLogView> createState() => _ChangeLogViewState();
}

class _ChangeLogViewState extends State<ChangeLogView> {
  final List<Map<String, dynamic>> changelogs = [
    {
      'date': '30/05/2025',
      'features': [
        'Change UI',
        'Bug fixes and improvement',
      ],
    },
    {
      'date': '17/03/2025',
      'features': [
        'Bug fixes and improvement',
      ],
    },
    {
      'date': '14/04/2025',
      'features': [
        'Bug fixes and improvement',
      ],
    },
    {
      'date': '15/02/2025',
      'features': [
        'Bug fixes and improvement',
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbarWithLeading(context, LocaleKeys.change_log.tr),
      backgroundColor: AppColor.neutral900,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AppImages.bg_white),
            fit: BoxFit.cover,
          ),
        ),
        child: ListView.builder(
          itemCount: changelogs.length,
          itemBuilder: (context, index) {
            final log = changelogs[index];
            return Padding(
              padding: index == 0
                  ? const EdgeInsets.only(left: 20, right: 20, top: 20)
                  : index != changelogs.length - 1
                      ? const EdgeInsets.only(left: 20, right: 20)
                      : const EdgeInsets.only(left: 20, right: 20, bottom: 20),
              child: IntrinsicHeight(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      children: [
                        const Icon(Icons.circle, color: AppColor.primaryGreen600, size: 10),
                        // if (index != changelogs.length - 1)
                        Expanded(
                          child: Container(
                            width: 2,
                            color: AppColor.neutral700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomText(
                            text: log['date'],
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                          const SizedBox(height: 8),
                          CustomText(
                            text: '${LocaleKeys.update_new_features.tr}:',
                            color: Colors.white,
                          ),
                          ...log['features'].map<Widget>((feature) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const CustomText(
                                    text: "• ",
                                    color: Colors.white,
                                  ),
                                  Expanded(
                                    child: CustomText(
                                      text: feature,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        ],
                      ).paddingBottom(16),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
