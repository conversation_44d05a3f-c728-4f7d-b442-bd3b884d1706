// ignore_for_file: prefer_const_constructors

import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/dash_board/controllers/dash_board_controller.dart';
import 'package:u2u_dpn/app/modules/rewards/views/install_u2u.dart';
import 'package:u2u_dpn/app/modules/setting/controllers/setting_controller.dart';
import 'package:u2u_dpn/app_state.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/custom_cell_setting.dart';
import 'package:u2u_dpn/widget/custom_text.dart';

class UserGuideView extends GetView<SettingController> {
  final controllerDashBoard = Get.find<DashBoardController>();
  UserGuideView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                        text: LocaleKeys.user_guide.tr,
                        fontSize: 24,
                      ),
                      const Icon(
                        Icons.close,
                        size: 30,
                        color: AppColor.neutral400,
                      ).onTap(() {
                        Get.back();
                      })
                    ],
                  ).paddingOnly(bottom: 16),
                  if (!kIsWeb)
                    if (!Platform.isWindows)
                      CustomCellSetting(
                          icon: AppImages.icon_about,
                          title: LocaleKeys.how_to_use_U2DPN_interface.tr,
                          onTap: () async {
                            AppState.instance.settingBox.write(SettingType.isStartConnection.toString(), true);
                            controllerDashBoard.selectIndex.value = 0;
                            Get.back();
                            Get.back();
                          }),
                  CustomCellSetting(
                      icon: AppImages.icon_install_wallet,
                      title: LocaleKeys.how_to_install_U2U_wallet.tr,
                      onTap: () {
                        Get.to(InstallU2U());
                      }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
