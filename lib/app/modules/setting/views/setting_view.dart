// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/app/modules/setting/views/change_log_view.dart';
import 'package:u2u_dpn/app/modules/setting/views/language_view.dart';
import 'package:u2u_dpn/app/modules/setting/views/legal_view.dart';
import 'package:u2u_dpn/app/modules/setting/views/user_guide_view.dart';
import 'package:u2u_dpn/app/routes/app_pages.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/socket_app.dart';
import 'package:u2u_dpn/socket_app_session.dart';
import 'package:u2u_dpn/utils/app_color.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/CustomContainerShadow.dart';
import 'package:u2u_dpn/widget/custom_appbar.dart';
import 'package:u2u_dpn/widget/custom_bottom_sheet.dart';
import 'package:u2u_dpn/widget/custom_cell_setting.dart';
import 'package:u2u_dpn/widget/custom_hide_text.dart';
import 'package:u2u_dpn/widget/custom_raisebutton.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
import 'package:u2u_dpn/widget/ultil.dart';
import 'package:url_launcher/url_launcher.dart';
import '../controllers/setting_controller.dart';
import 'about_view.dart';

class SettingView extends GetView<SettingController> {
  const SettingView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: AppColor.neutralBlack,
      appBar: CustomAppbarWithLeading(context, LocaleKeys.setting.tr, actions: [
        const CustomText(
          text: 'Version',
          fontSize: 12,
          color: AppColor.neutral300,
        ),
        Container(
          margin: const EdgeInsets.only(right: 16, left: 4),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(color: AppColor.neutralBlack, borderRadius: BorderRadius.circular(8)),
          child: const CustomText(
            text: '1.3.5',
            fontSize: 12,
            fontWeight: FontWeight.w600,
            fontStyle: FontStyle.italic,
          ),
        )
      ]),
      body: Container(
        width: Get.width,
        height: Get.height,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AppImages.background), fit: BoxFit.fill)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: kToolbarHeight + (Platform.isIOS ? 40 : 32)), //24 + 16 padding
            CustomContainerShadow(
              child: Row(
                children: [
                  Image.asset(
                    AppImages.icon_circle_gmail,
                    width: 48,
                    height: 48,
                  ).paddingRight(16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          text: SettingConfig.user_email,
                          fontSize: 16,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Row(
                          children: [
                            CustomText(
                              text:
                                  // "ID: ${CustomHideTex.hideMiddleText(SettingConfig.user_id)}",
                                  "ID: ${CustomHideTex.hideMiddleText(SettingConfig.user_id)}",
                              color: AppColor.neutral300,
                              fontSize: 16,
                            ).paddingRight(8),
                            // Obx(() {
                            //   if (!Get.isRegistered<SettingController>()) {
                            //     Get.put(SettingController());
                            //   }
                            //   return CustomText(
                            //     text:
                            //         // "ID: ${CustomHideTex.hideMiddleText(SettingConfig.user_id)}",
                            //         "ID: ${CustomHideTex.hideMiddleText(controller.rewardsController.userDetailModel.value.userReferral?.userAddr ?? "...")}",
                            //     color: AppColor.neutral300,
                            //     fontSize: 16,
                            //   ).paddingRight(8);
                            // }),
                            Obx(() => controller.isCopy.value
                                ? const Icon(
                                    Icons.check,
                                    color: Colors.green,
                                    size: 15,
                                  )
                                : const Icon(
                                    Icons.copy,
                                    color: AppColor.neutral300,
                                    size: 15,
                                  ).onTap(() async {
                                    Clipboard.setData(ClipboardData(text: SettingConfig.user_id)).then((value) {
                                      Utils.showSnackbar(context, LocaleKeys.copied_successfully.tr);
                                    });
                                    controller.isCopy.value = true;
                                    await Future.delayed(const Duration(seconds: 5));
                                    controller.isCopy.value = false;
                                  }))
                          ],
                        )
                      ],
                    ),
                  )
                ],
              ),
            ).paddingSymmetric(vertical: 20),
            Expanded(child: LayoutBuilder(builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                  child: IntrinsicHeight(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Row(
                              children: [
                                Image.asset(
                                  AppImages.shield,
                                  width: 24,
                                ).paddingOnly(right: 8),
                                CustomText(
                                  text: LocaleKeys.reconnect_dpn.tr,
                                  fontSize: 14,
                                ),
                              ],
                            ),
                            Obx(() => Switch(
                                  value: controller.isSwitched.value,
                                  onChanged: (Value) {
                                    // controller.isSwitched.value = Value;
                                    if (!SettingConfig.autoConnection && !controller.isSwitched.value) {
                                      reconnectDpnOn(context);
                                    } else {
                                      reconnectDpnTurnOff(context);
                                    }
                                  },
                                  activeColor: white,
                                  inactiveTrackColor: AppColor.neutral500,
                                  inactiveThumbColor: white,
                                  activeTrackColor: AppColor.primaryGreen600,
                                ))
                          ],
                        ).paddingOnly(top: 12).onTap(() {
                          if (!controller.isSwitched.value) {
                            reconnectDpnOn(context);
                          } else {
                            reconnectDpnTurnOff(context);
                          }
                        }),
                        CustomCellSetting(
                            icon: AppImages.icon_language,
                            title: LocaleKeys.language.tr,
                            onTap: () {
                              laguage(context);
                            }),
                        CustomCellSetting(
                            icon: AppImages.icon_user_guide,
                            title: LocaleKeys.user_guide.tr,
                            onTap: () {
                              userGuide(context);
                            }),
                        CustomCellSetting(
                            icon: AppImages.icon_change_log,
                            title: LocaleKeys.change_log.tr,
                            onTap: () {
                              Get.to(() => const ChangeLogView());
                            }),
                        CustomCellSetting(
                            icon: AppImages.icon_send_feedback,
                            title: LocaleKeys.send_feedback.tr,
                            onTap: () {
                              launchUrl(
                                Uri.parse("https://forms.gle/LXUHGxQURqmxbxGR9"),
                                mode: LaunchMode.externalApplication,
                              );
                            }),
                        CustomCellSetting(
                            icon: AppImages.icon_about,
                            title: LocaleKeys.about.tr,
                            onTap: () {
                              Get.to(() => const AboutView());
                            }),
                        CustomCellSetting(
                            icon: AppImages.icon_legal,
                            title: LocaleKeys.legal.tr,
                            onTap: () {
                              legal(context);
                            }),
                        // CustomContainerShadow(const InfoComunity()).paddingOnly(top: 16, bottom: 24),
                        const Divider(height: 0, color: AppColor.neutral700).paddingSymmetric(vertical: 16),
                        CustomText(
                          text: LocaleKeys.join_U2DPN.tr.toUpperCase(),
                          fontSize: 12,
                          color: AppColor.neutral300,
                        ),
                        Column(
                          children: [
                            CustomCellSetting(
                                isShowIcon: true,
                                icon: AppImages.icon_twitter,
                                imgColor: AppColor.neutral400,
                                title: 'X (Twitter)',
                                onTap: () {
                                  launchUrl(
                                    Uri.parse("https://x.com/u2dpn_network"),
                                    mode: LaunchMode.externalApplication,
                                  );
                                }),
                            CustomCellSetting(
                                isShowIcon: true,
                                icon: AppImages.icon_telegram,
                                imgColor: AppColor.neutral400,
                                title: 'Telegram',
                                onTap: () {
                                  launchUrl(
                                    Uri.parse("https://t.me/u2u_xyzchat"),
                                    mode: LaunchMode.externalApplication,
                                  );
                                }),
                            CustomCellSetting(
                                isShowIcon: true,
                                icon: AppImages.icon_youtube,
                                imgColor: AppColor.neutral400,
                                title: 'Youtube',
                                onTap: () {
                                  launchUrl(
                                    Uri.parse("https://www.youtube.com/playlist?list=PLiZq-QQcRRnffubgxPGJCj0UA_D0Y5p0R"),
                                    mode: LaunchMode.externalApplication,
                                  );
                                })
                          ],
                        ).paddingTop(16).paddingBottom(12),

                        const Spacer(),
                        CustomCellSetting(
                          icon: AppImages.icon_logout,
                          title: LocaleKeys.logout.tr,
                          onTap: () {
                            logout(context);
                          },
                          // onTap: () {
                          //   ProgressDialog.showDialogNotification(
                          //       content: Column(
                          //         children: [
                          //           Image.asset(
                          //             AppImages.ic_warning,
                          //             width: 40,
                          //             height: 40,
                          //           ),
                          //           CustomText(
                          //             text: LocaleKeys.logout.tr,
                          //             fontSize: 20,
                          //             fontWeight: FontWeight.w500,
                          //           ).paddingSymmetric(vertical: 10),
                          //           CustomText(text: LocaleKeys.confirm_logout.tr).paddingSymmetric(vertical: 10),
                          //         ],
                          //       ),
                          //       onPressed: () {
                          //         controller.logout();
                          //         Get.offAllNamed(Routes.SIGN_IN);
                          //         SocketApp.stopConnection();
                          //         SocketApp.logout();
                          //         SocketAppSession.logout();

                          //         Utils.showSnackbar(context, LocaleKeys.logout_successfully.tr);
                          //       },
                          //       onPressedCancell: () {
                          //         Get.back();
                          //       });
                          // },
                          // textColor: Colors.red.shade800,
                        ),
                        const SizedBox(
                          height: 24,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            })),
          ],
        ),
      ),
    );
  }

  void laguage(BuildContext context) {
    return CustomBottomSheet.showModalNotFullScreenWithHeight(
      context,
      const LaguageView(),
      height: MediaQuery.of(context).size.height * 0.5,
    );
  }

  void userGuide(BuildContext context) {
    return CustomBottomSheet.showModalNotFullScreenWithHeight(
      context,
      UserGuideView(),
      height: MediaQuery.of(context).size.height * 0.5,
    );
  }

  void legal(BuildContext context) {
    return CustomBottomSheet.showModalNotFullScreenWithHeight(
      context,
      const LegalView(),
      height: MediaQuery.of(context).size.height * 0.5,
    );
  }

  void logout(BuildContext context) {
    return CustomBottomSheet.showModalNotFullScreenWithHeight(
      context,
      SingleChildScrollView(
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.45 > 350 ? MediaQuery.of(context).size.height * 0.45 : 350,
          child: Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Spacer(),
                        const Icon(
                          Icons.close,
                          size: 30,
                          color: AppColor.neutral400,
                        ).onTap(() {
                          Get.back();
                        })
                      ],
                    ).paddingOnly(bottom: 16),
                    Column(
                      children: [
                        Image.asset(
                          AppImages.icon_logout,
                          width: 100,
                          height: 100,
                        ),
                        CustomText(
                          text: LocaleKeys.logout.tr,
                          fontSize: 18,
                        ).paddingOnly(top: 8, bottom: 12),
                        SizedBox(
                          width: 250,
                          child: CustomText(
                            text: LocaleKeys.confirm_logout.tr,
                            textAlign: TextAlign.center,
                            color: AppColor.neutral300,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    CostomRaisedButtom(
                      name: LocaleKeys.logout.tr,
                      function: () {
                        controller.logout();
                        Get.offAllNamed(Routes.SIGN_IN);
                        SocketApp.stopConnection();
                        SocketApp.logout();
                        SocketAppSession.logout();

                        Utils.showSnackbar(context, LocaleKeys.logout_successfully.tr);
                      },
                      colorsGradient: const [AppColor.ErrorColor300, AppColor.ErrorColor300],
                      colorText: white,
                    ),
                  ],
                ),
              ),
            ],
          ).paddingOnly(left: 16, right: 16, bottom: 24),
        ),
      ),
      height: MediaQuery.of(context).size.height * 0.5,
    );
  }

  void reconnectDpnOn(BuildContext context) {
    return CustomBottomSheet.showModalNotFullScreenWithHeight(
      context,
      SingleChildScrollView(
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.45 > 420 ? MediaQuery.of(context).size.height * 0.45 : 420,
          child: Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Spacer(),
                        const Icon(
                          Icons.close,
                          size: 30,
                          color: AppColor.neutral400,
                        ).onTap(() {
                          Get.back();
                        })
                      ],
                    ).paddingOnly(bottom: 16),
                    Column(
                      children: [
                        Image.asset(
                          AppImages.reconnectDPN,
                          width: 100,
                          height: 100,
                        ),
                        CustomText(
                          text: LocaleKeys.turn_on_reconnect_dpn.tr,
                          fontSize: 18,
                        ).paddingOnly(top: 8, bottom: 12),
                        SizedBox(
                          width: 350,
                          child: CustomText(
                            text: LocaleKeys.application_will_automatically.tr,
                            textAlign: TextAlign.center,
                            color: AppColor.neutral300,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    CostomRaisedButtom(
                      name: LocaleKeys.turn_on_now.tr,
                      function: () {
                        controller.isSwitched.value = true;
                        SettingConfig.autoConnection = true;
                        Get.back();
                      },
                    ),
                    CostomRaisedButtom(
                      name: LocaleKeys.cancel.tr,
                      function: () {
                        Get.back();
                      },
                      colorsGradient: const [AppColor.neutral600, AppColor.neutral600],
                      colorText: white,
                    ).paddingTop(16)
                  ],
                ),
              ),
            ],
          ).paddingOnly(left: 16, right: 16, bottom: 24),
        ),
      ),
      height: MediaQuery.of(context).size.height * 0.5,
    );
  }

  void reconnectDpnTurnOff(BuildContext context) {
    return CustomBottomSheet.showModalNotFullScreenWithHeight(
      context,
      SingleChildScrollView(
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.45 > 420 ? MediaQuery.of(context).size.height * 0.45 : 420,
          child: Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Spacer(),
                        const Icon(
                          Icons.close,
                          size: 30,
                          color: AppColor.neutral400,
                        ).onTap(() {
                          Get.back();
                        })
                      ],
                    ).paddingOnly(bottom: 16),
                    Column(
                      children: [
                        Image.asset(
                          AppImages.reconnectDPN,
                          width: 100,
                          height: 100,
                          color: AppColor.neutral400,
                        ),
                        CustomText(
                          text: LocaleKeys.turn_off_reconnect_dpn.tr,
                          fontSize: 18,
                        ).paddingOnly(top: 8, bottom: 12),
                        SizedBox(
                          width: 350,
                          child: CustomText(
                            text: LocaleKeys.the_system_will_not_automatically.tr,
                            textAlign: TextAlign.center,
                            color: AppColor.neutral300,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    CostomRaisedButtom(
                      name: LocaleKeys.turn_off.tr,
                      function: () {
                        controller.isSwitched.value = false;
                        SettingConfig.autoConnection = false;
                        Get.back();
                      },
                      colorsGradient: const [AppColor.ErrorColor300, AppColor.ErrorColor300],
                      colorText: white,
                    ),
                    CostomRaisedButtom(
                      name: LocaleKeys.cancel.tr,
                      function: () {
                        Get.back();
                      },
                      colorsGradient: const [AppColor.neutral600, AppColor.neutral600],
                      colorText: white,
                    ).paddingTop(16)
                  ],
                ),
              ),
            ],
          ).paddingOnly(left: 16, right: 16, bottom: 24),
        ),
      ),
      height: MediaQuery.of(context).size.height * 0.5,
    );
  }
}
