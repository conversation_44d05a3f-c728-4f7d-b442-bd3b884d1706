import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/utils/app_images.dart';
import 'package:u2u_dpn/widget/custom_cell_setting.dart';
import 'package:u2u_dpn/widget/custom_text.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../utils/app_color.dart';
import '../../../../widget/custom_appbar.dart';

class AboutView extends StatelessWidget {
  const AboutView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: AppColor.neutral800,
      appBar: CustomAppbarWithLeading(context, LocaleKeys.about.tr),
      body: SingleChildScrollView(
        child: Container(
          width: Get.width,
          height: MediaQuery.of(context).size.height > 650 ? MediaQuery.of(context).size.height : 650,
          decoration: const BoxDecoration(image: DecorationImage(image: AssetImage(AppImages.background), fit: BoxFit.fill)),
          padding: const EdgeInsets.only(top: kToolbarHeight + 60),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: LocaleKeys.introduction.tr.toUpperCase(),
                fontSize: 12,
                color: AppColor.neutral300,
              ),
              CustomText(
                text: LocaleKeys.u2_DPN_is_a_decentralized.tr,
                fontSize: 16,
              ).paddingTop(24),
              CustomText(
                text: LocaleKeys.links.tr.toUpperCase(),
                fontSize: 12,
                color: AppColor.neutral300,
              ).paddingTop(32),
              //links
              Column(
                children: [
                  CustomCellSetting(
                      icon: AppImages.icon_telegram,
                      imgColor: AppColor.neutral400,
                      title: 'Telegram',
                      onTap: () {
                        launchUrl(
                          Uri.parse("https://t.me/u2u_xyzchat"),
                          mode: LaunchMode.externalApplication,
                        );
                      }),
                  CustomCellSetting(
                      icon: AppImages.icon_twitter,
                      imgColor: AppColor.neutral400,
                      title: 'X (Twitter)',
                      onTap: () {
                        launchUrl(
                          Uri.parse("https://x.com/u2dpn_network"),
                          mode: LaunchMode.externalApplication,
                        );
                      }),
                  CustomCellSetting(
                      icon: AppImages.icon_docs,
                      title: 'Docs',
                      onTap: () {
                        launchUrl(
                          Uri.parse("https://docs.u2dpn.xyz/"),
                          mode: LaunchMode.externalApplication,
                        );
                      })
                ],
              ).paddingTop(16),
              //make a review

              CustomText(
                text: LocaleKeys.make_a_review.tr.toUpperCase(),
                fontSize: 12,
                color: AppColor.neutral300,
              ).paddingOnly(top: 32, bottom: 16),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () => launchUrl(
                      Uri.parse("https://apps.apple.com/vn/app/u2u-super-app/id6471394866"),
                      mode: LaunchMode.externalApplication,
                    ),
                    child: Image.asset(
                      AppImages.download_ios,
                      width: Get.width * 0.44,
                      fit: BoxFit.contain,
                    ),
                  ),
                  InkWell(
                    onTap: () => launchUrl(
                      Uri.parse("https://play.google.com/store/apps/details?id=com.u2umobilewallet&pcampaignid=web_share"),
                      mode: LaunchMode.externalApplication,
                    ),
                    child: Image.asset(
                      AppImages.download_chplay,
                      width: Get.width * 0.44,
                      fit: BoxFit.contain,
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 16,
              )
            ],
          ).paddingSymmetric(horizontal: 16),
        ),
      ),
    );
  }
}
