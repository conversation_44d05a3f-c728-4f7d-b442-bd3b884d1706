import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:u2u_dpn/locales/locales.g.dart';
import 'package:u2u_dpn/widget/custom_cell_setting.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../utils/app_color.dart';
import '../../../../utils/app_images.dart';
import '../../../../widget/custom_text.dart';

class LegalView extends StatelessWidget {
  const LegalView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                        text: LocaleKeys.legal.tr,
                        fontSize: 24,
                      ),
                      const Icon(
                        Icons.close,
                        size: 30,
                        color: AppColor.neutral400,
                      ).onTap(() {
                        Get.back();
                      })
                    ],
                  ).paddingOnly(bottom: 16),
                  CustomCellSetting(
                      icon: AppImages.icon_term,
                      title: LocaleKeys.privacy_policy.tr,
                      onTap: () {
                        launchUrl(
                          Uri.parse("https://docs.u2dpn.xyz/u2dpn-privacy-policy"),
                          mode: LaunchMode.externalApplication,
                        );
                      }),
                  CustomCellSetting(
                      icon: AppImages.icon_term,
                      title: LocaleKeys.term_of_service.tr,
                      onTap: () {
                        launchUrl(
                          Uri.parse("https://docs.u2dpn.xyz/u2dpn-privacy-policy"),
                          mode: LaunchMode.externalApplication,
                        );
                      }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
