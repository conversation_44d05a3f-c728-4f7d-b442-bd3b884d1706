import 'package:get/get.dart';
import 'package:u2u_dpn/main.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../widget/widget.dart';

class HomeController extends GetxController {

  final selectDropdown = Items.listItems[0].obs;

  //Raward
  final selectRaward = false.obs;
  final rewardUp = false.obs;

  //my task list items
  final listIndex = [].obs;

  //pop menu value
  final selectValue = ''.obs;
  final checkValueChoose = ''.obs;
  final checkClick = false.obs;

  final List<String> items = [
    'All status',
    'Available',
    'Completed',
    'Expired'
  ];

  List<bool> listmenuCard = [true, false, true, true, false];
  final Uri uri = Uri.parse(
      'https://www.youtube.com/watch?v=wI1IroOdVvE&ab_channel=HeyFlutter%E2%80%A4com');
  final listmenuCardString =
      ['Completed', 'Expired', 'Expired', 'Completed', 'Available'].obs;

  final count = 0.obs;
  @override
  void onInit() {
    super.onInit();
    selectValue.value=items[0];
    checkValueChoose.value = items[0];
  }



  changeIndexList(int index) {
    if (listIndex.contains(index)) {
      listIndex.remove(index);
    } else {
      listIndex.add(index);
    }
    //
    // logger.d(listIndex);
  }

  Future tapLink(var url) async {
    try {
      if (await canLaunchUrl(url)) {
        await launchUrl(url);
      }
    } catch (e) {
      logger.d('tapLink Error: $e');
    }
  }

  void increment() => count.value++;
}
