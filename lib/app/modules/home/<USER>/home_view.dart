// ignore_for_file: deprecated_member_use, non_constant_identifier_names

import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:url_launcher/link.dart';

import '../../../../utils/app_utils.dart';
//import '../../../../widget/custom_popup_menu.dart';
import '../../../../widget/widget.dart';
import '../controllers/home_controller.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColor.neutral800,
        body: Stack(children: [
          Image.asset(
            AppImages.bg_my_task,
            width: Get.width,
            fit: BoxFit.contain,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const CustomText(
                text: 'My Tasks',
                fontSize: 32,
              ).paddingOnly(top: 16),
              menu().paddingSymmetric(vertical: 24),
              // noData(),
              //body, show card items
              Expanded(child: CustomNodata(textLine2: '')
                  //  ListView.builder(
                  //     itemCount: controller.listmenuCardString.length,
                  //     padding: EdgeInsets.all(0),
                  //     itemBuilder: (context, index) {
                  //       return Obx(
                  //         () => MyTaskItems(
                  //                 context: context,
                  //                 isType: controller.listmenuCardString[index],
                  //                 title: controller.listmenuCardString[index],
                  //                 onTap: () {
                  //                   controller.changeIndexList(index);
                  //                   // controller.selectIndex.value = index;
                  //                 },
                  //                 index: index)
                  //             .paddingOnly(bottom: 15 ),
                  //       );
                  //     }),
                  ),
            ],
          ).paddingSymmetric(horizontal: 16).paddingTop(45),
        ]));
  }

  // Center noData() {
  //   return Center(
  //     child: Column(
  //       children: [
  //         Image.asset(
  //           AppImages.message,
  //           width: 128,
  //         ),
  //         CustomText(
  //           text: 'There is no data available.',
  //           color: AppColor.neutral200,
  //           fontSize: 16,
  //           textAlign: TextAlign.center,
  //         ),
  //         CustomText(
  //           text: ' You can perform the My Tasks here.',
  //           color: AppColor.neutral200,
  //           fontSize: 16,
  //         ).paddingOnly(bottom: 32 ),
  //         CostomRaisedButtom(
  //           name: 'Show me all',
  //           function: () {},
  //           colorsGradient: const [AppColor.neutral600, AppColor.neutral600],
  //         ).paddingSymmetric(
  //           horizontal: 127,
  //         )
  //       ],
  //     ).paddingOnly(top: 48 ),
  //   );
  // }

  Widget menu() {
    return Obx(() => SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  controller.selectRaward.value = true;
                  controller.rewardUp.value = !controller.rewardUp.value;
                },
                child: Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 7),
                  decoration: BoxDecoration(
                      color: AppColor.neutral700,
                      border: Border.all(color: (controller.selectRaward.value) ? AppColor.neutral400 : Colors.transparent),
                      borderRadius: BorderRadius.circular(8)),
                  child: Row(
                    children: [
                      const CustomText(
                        text: 'Sort by: Reward',
                        color: AppColor.neutral200,
                      ),
                      controller.selectRaward.value
                          ? Icon(
                              controller.rewardUp.value ? Icons.arrow_upward_outlined : Icons.arrow_downward_outlined,
                              color: AppColor.neutral300,
                              size: 17,
                            ).paddingOnly(left: 4)
                          : const SizedBox()
                    ],
                  ),
                ),
              ),
              PopupMenuCustomer(
                appBarHeight: 40,
                data: controller.items,
                onSelected: (int index) {
                  controller.selectValue.value = controller.items[index];
                  controller.checkValueChoose.value = controller.items[index];
                  controller.checkClick.value = false;
                },
                getTitle: (int index) {
                  return controller.items[index];
                },
                checkValueChoosed: controller.checkValueChoose.value,
                onOpened: () {
                  controller.checkClick.value = true;
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 7.5, horizontal: 16),
                  decoration: BoxDecoration(
                      border: Border.all(
                        color: controller.checkClick.value ? AppColor.neutral400 : Colors.transparent,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      color: AppColor.neutral700),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      CustomText(text: controller.selectValue.value, color: AppColor.neutral200),
                      SvgPicture.asset(
                        controller.checkClick.value ? AppImages.arrow_up : AppImages.arrow_down,
                        width: 16,
                        color: AppColor.neutral400,
                      ).paddingOnly(left: 6),
                    ],
                  ),
                ),
              )
            ],
          ),
        ));
  }

  Container MyTaskItems(
      {required BuildContext context,
      // required bool isFalse,
      required String isType,
      required String title,
      required int index,
      required Function() onTap}) {
    return Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        decoration: BoxDecoration(
            border: Border.all(color: AppColor.borderContainerDarkmMode), color: AppColor.neutral700, borderRadius: BorderRadius.circular(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // isFalse?
            // Container(
            //   padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            //   decoration: BoxDecoration(
            //       borderRadius: BorderRadius.circular(6),
            //       color: AppColor.neutral600),
            //   child: ShaderMask(
            //     shaderCallback: (Rect rect){
            //       return AppColor.customLineGradientGreen.createShader(rect);
            //     },
            //     child: CustomText(text: 'Completed', fontSize: 12,),
            //   ),
            // )
            //state
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  gradient: (isType == 'Available')
                      ? AppColor.customLineGradientGreen
                      : const LinearGradient(colors: [AppColor.neutral600, AppColor.neutral600])),
              child: (isType == 'Available')
                  ? CustomText(
                      text: title,
                      fontSize: 12,
                      color: Colors.black,
                    )
                  : ShaderMask(
                      shaderCallback: (Rect rect) {
                        if (isType == "Completed") {
                          return AppColor.customLineGradientGreen.createShader(rect);
                        }
                        return const LinearGradient(colors: [AppColor.neutral400, AppColor.neutral400]).createShader(rect);
                        //return AppColor.customLineGradientGreen.createShader(rect);
                      },
                      // },
                      child: CustomText(
                        text: title,
                        fontSize: 12,
                      ),
                    ),
            ),
            //name
            Padding(
              padding: const EdgeInsets.only(top: 8, bottom: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(bottom: 3),
                        child: CustomText(
                          text: 'Visit the NFT Course',
                          fontSize: 18,
                        ),
                      ),
                      CustomText(
                        text: '23/01/2023 - 12:20',
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppColor.neutral300,
                      ),
                    ],
                  ),
                  GestureDetector(
                    onTap: onTap,
                    child: CircleAvatar(
                      backgroundColor: AppColor.neutral600,
                      child: SvgPicture.asset(controller.listIndex.contains(index) ? AppImages.arrow_up : AppImages.arrow_down),
                    ),
                  )
                ],
              ),
            ),
            Row(
              children: [
                const CustomText(
                  text: '12.84',
                  fontSize: 16,
                  maxLine: 2,
                ),
                Image.asset(
                  AppImages.logo,
                  width: 18,
                ).paddingOnly(left: 4)
              ],
            ),
            controller.listIndex.contains(index)
                // controller.selectIndex.value == index
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset(AppImages.linkAdress).paddingOnly(right: 8),
                          Expanded(
                              child: Link(
                            uri: controller.uri,
                            target: LinkTarget.defaultTarget,
                            builder: (context, openLink) => GestureDetector(
                              onTap: openLink,
                              child: CustomText(
                                maxLine: 1,
                                overflow: TextOverflow.ellipsis,
                                text: controller.uri,
                                textDecoration: TextDecoration.underline,
                                color: AppColor.primaryBlue500,
                              ),
                            ),
                          ))
                          // Expanded(child: GestureDetector(
                          //   child: CustomText(text: uri,
                          //   textDecoration: TextDecoration.underline,
                          //   color: AppColor.primaryBlue500,),
                          // ))
                        ],
                      ).paddingOnly(bottom: 16),
                      Row(
                        children: [
                          GestureDetector(
                            onTap: () => controller.tapLink(controller.uri),
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Image.network(
                                    'https://static.tildacdn.com/tild3634-3739-4065-b536-643934383262/Cat.jpg',
                                    width: 128,
                                    height: 72,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                Positioned.fill(
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(12),
                                    child: Container(
                                      color: Colors.black.withOpacity(0.15),
                                    ),
                                  ),
                                ),
                                Icon(
                                  Icons.play_arrow_rounded,
                                  color: Colors.white.withOpacity(0.64),
                                  size: 50,
                                )
                              ],
                            ),
                          ),
                          Expanded(
                              child: const CustomText(
                            text: 'Lorem ipsum dolar sit amet',
                            fontSize: 16,
                            overflow: TextOverflow.ellipsis,
                            maxLine: 3,
                          ).paddingOnly(left: 12))
                        ],
                      ),
                      const CustomText(
                        text:
                            'It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged.',
                        fontWeight: FontWeight.w400,
                        color: AppColor.neutral300,
                      ).paddingOnly(top: 16, bottom: 24),
                      isType == 'Expired' ? const SizedBox() : ((isType == 'Available') ? avaiableButton(() {}) : notAvaiableButton(() {})),
                      //   : CostomRaisedButtom(
                      //       name: (isType == 'Available')
                      //           ? "Complete Task"
                      //           : 'View your Rewards',
                      //       function: () {},
                      //       colorText: isType == 'Available'? AppColor.neutral800 : Colors.white,
                      //       colorsGradient: (isType == 'Available')
                      //           ? [
                      //               Color(0xff1AF7A9),
                      //             ]
                      //           : [
                      //               AppColor.neutral600,
                      //               AppColor.neutral600
                      //             ],
                      // iconButton: isType == 'Available'? Icon(Icons.play_arrow_rounded, color: AppColor.neutral800, size: 20,) : SizedBox(),
                      //     )
                    ],
                  ).paddingOnly(top: 4)
                : const SizedBox()
          ],
        ));
  }
}

//Button
CostomRaisedButtom notAvaiableButton(Function() function) {
  return CostomRaisedButtom(
    name: 'View your Rewards',
    function: function,
    colorText: Colors.white,
    colorsGradient: const [AppColor.neutral600, AppColor.neutral600],
  );
}

CostomRaisedButtom avaiableButton(Function() function) {
  return CostomRaisedButtom(
    name: "Complete Task",
    function: function,
    colorText: AppColor.neutral800,
    colorsGradient: const [
      AppColor.primaryBlue500,
      Color(0xff1AF7A9),
    ],
    iconButton: const Icon(
      Icons.play_arrow_rounded,
      color: AppColor.neutral800,
      size: 20,
    ),
  );
}
