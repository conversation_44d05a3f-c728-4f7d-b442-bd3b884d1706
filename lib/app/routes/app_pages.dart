// ignore_for_file: constant_identifier_names

import 'package:get/get.dart';
import 'package:u2u_dpn/app/modules/connection/views/leader_board.dart';
import 'package:u2u_dpn/app/modules/connection/views/location_bonus.dart';
import 'package:u2u_dpn/app/modules/connection/views/rank_benefit.dart';
import 'package:u2u_dpn/data/setting_config.dart';
import '../modules/claim_history/bindings/claim_history_binding.dart';
import '../modules/claim_history/views/claim_history_view.dart';
import '../modules/connection/bindings/connection_binding.dart';
import '../modules/connection/views/connection_history.dart';
import '../modules/connection/views/connection_view.dart';
import '../modules/dash_board/bindings/dash_board_binding.dart';
import '../modules/dash_board/views/dash_board_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/maintenance/bindings/maintenance_binding.dart';
import '../modules/maintenance/views/maintenance_view.dart';
import '../modules/my_tier/bindings/my_tier_binding.dart';
import '../modules/my_tier/views/my_tier_view.dart';
import '../modules/notification/bindings/notification_binding.dart';
import '../modules/notification/views/notification_view.dart';
import '../modules/otp/bindings/otp_binding.dart';
import '../modules/otp/views/otp_view.dart';
import '../modules/referrals/bindings/referrals_binding.dart';
import '../modules/referrals/views/referrals_history.dart';
import '../modules/referrals/views/referrals_view.dart';
import '../modules/rewards/bindings/rewards_binding.dart';
import '../modules/rewards/views/rewards_view.dart';
import '../modules/setting/bindings/setting_binding.dart';
import '../modules/setting/views/setting_view.dart';
import '../modules/sign_in/bindings/sign_in_binding.dart';
import '../modules/sign_in/views/sign_in_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.DASH_BOARD;
  static const INITIAL_LOGIN = Routes.SIGN_IN;
  static const INITIAL_SPLASH = Routes.SPLASH_SCREEN;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.DASH_BOARD,
      page: () => const DashBoardView(),
      binding: DashBoardBinding(),
      children: [
        GetPage(
          name: _Paths.DASH_BOARD,
          page: () => const DashBoardView(),
          binding: DashBoardBinding(),
        ),
      ],
    ),
    // GetPage(
    //   name: _Paths.SPLASH_SCREEN,
    //   page: () => const SplashScreenView(),
    //   binding: SplashScreenBinding(),
    // ),
    GetPage(
      name: _Paths.CLAIM_HISTORY,
      page: () => ClaimHistoryView(),
      binding: ClaimHistoryBinding(),
    ),
    GetPage(
      name: _Paths.CONNECTION,
      page: () => const ConnectionView(),
      binding: ConnectionBinding(),
    ),
    GetPage(
      name: _Paths.MY_TIER,
      page: () => const MyTierView(),
      binding: MyTierBinding(),
    ),
    GetPage(
      name: _Paths.REWARDS,
      page: () => const RewardsView(),
      binding: RewardsBinding(),
    ),
    GetPage(
      name: _Paths.REFERRALS,
      page: () => const ReferralsView(),
      binding: ReferralsBinding(),
    ),
    GetPage(
      name: _Paths.REFERRALS_HISTORY,
      page: () => const ReferralsHistoryView(),
    ),
    GetPage(
      name: _Paths.CONNECTION_HISTORY,
      page: () => const ConnectionHistory(),
    ),
    GetPage(
      name: _Paths.SIGN_IN,
      page: () => SignInView(),
      binding: SignInBinding(),
    ),
    GetPage(
      name: _Paths.OTP,
      page: () => const OtpView(),
      binding: OtpBinding(),
    ),
    // GetPage(
    //   name: _Paths.SHARED_BANDWIDTH,
    //   page: () => const SharedBandwidth(),
    //   // binding: ConnectionBinding(),
    // ),
    GetPage(
      name: _Paths.SETTING,
      page: () => const SettingView(),
      binding: SettingBinding(),
    ),
    GetPage(
      name: _Paths.MAINTENANCE,
      page: () => const MaintenanceView(),
      binding: MaintenanceBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFICATION,
      page: () => const NotificationView(),
      binding: NotificationBinding(),
    ),
    GetPage(
      name: _Paths.LOCATION_BONUS,
      page: () => const LocationBonusScreen(),
    ),
    GetPage(
      name: _Paths.RANK_BENEFITS,
      page: () => const RankBenefits(),
    ),
    GetPage(
      name: _Paths.unknown,
      page: () => SettingConfig.token != '' ? const DashBoardView() : SignInView(),
    ),
    GetPage(
      name: _Paths.LEADER_BOARD,
      page: () => const LeaderBoard(),
    ),
  ];
}
