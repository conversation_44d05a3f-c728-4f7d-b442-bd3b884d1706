<svg width="283" height="104" viewBox="0 0 283 104" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_2601_1257)">
<rect x="52.75" y="11.25" width="179.5" height="79.5" rx="39.75" stroke="url(#paint0_linear_2601_1257)" stroke-width="0.5" shape-rendering="crispEdges"/>
</g>
<g opacity="0.64" filter="url(#filter1_d_2601_1257)">
<rect x="48.75" y="7.25" width="187.5" height="87.5" rx="43.75" stroke="url(#paint1_linear_2601_1257)" stroke-width="0.5" shape-rendering="crispEdges"/>
</g>
<g opacity="0.32" filter="url(#filter2_d_2601_1257)">
<rect x="44.75" y="3.25" width="195.5" height="95.5" rx="47.75" stroke="url(#paint2_linear_2601_1257)" stroke-width="0.5" shape-rendering="crispEdges"/>
</g>
<rect x="110.75" y="19.25" width="63.5" height="63.5" rx="15.75" fill="#171423" stroke="url(#paint3_linear_2601_1257)" stroke-width="0.5"/>
<g filter="url(#filter3_d_2601_1257)">
<path d="M143 83V91" stroke="#13E3C1" stroke-width="0.5"/>
</g>
<g filter="url(#filter4_d_2601_1257)">
<path d="M143 11.5V19" stroke="#13E3C0" stroke-width="0.5"/>
</g>
<rect x="0.375" y="-0.375" width="58.6786" height="58.6786" rx="13.3393" transform="matrix(1 0 0 -1 112.786 79.9643)" fill="url(#paint4_linear_2601_1257)" stroke="#2D2C3B" stroke-width="0.75"/>
<circle cx="124.214" cy="31.5714" r="2.57143" fill="#171423" stroke="url(#paint5_linear_2601_1257)" stroke-width="0.571429"/>
<circle cx="124.214" cy="70.4286" r="2.57143" fill="#171423" stroke="url(#paint6_linear_2601_1257)" stroke-width="0.571429"/>
<circle cx="160.786" cy="31.5714" r="2.57143" fill="#171423" stroke="url(#paint7_linear_2601_1257)" stroke-width="0.571429"/>
<circle cx="160.786" cy="70.4286" r="2.57143" fill="#171423" stroke="url(#paint8_linear_2601_1257)" stroke-width="0.571429"/>
<g opacity="0.48" filter="url(#filter5_f_2601_1257)">
<path d="M157.415 44.5799L157.413 44.5783C155.463 42.6481 153.211 41.1394 150.755 40.1331C148.3 39.1076 145.624 38.5655 142.869 38.5655C137.378 38.5655 132.227 40.7197 128.344 44.5826L128.344 44.5831L126.932 45.9949L126.932 45.9946L126.923 46.0042C126.662 46.2935 126.65 46.7623 126.932 47.0445L129.756 49.8681C129.906 50.0189 130.094 50.0821 130.288 50.0821C130.463 50.0821 130.652 50.0228 130.803 49.8663L132.213 48.4485C132.213 48.4484 132.213 48.4484 132.213 48.4483C135.049 45.6128 138.841 44.0282 142.869 44.0282C146.916 44.0282 150.689 45.6125 153.545 48.4488C153.545 48.4489 153.545 48.449 153.545 48.4491L154.957 49.8603C155.239 50.1425 155.708 50.1301 155.997 49.8691L155.997 49.8694L156.007 49.8597L158.81 47.0367C158.81 47.0366 158.81 47.0366 158.81 47.0365C159.107 46.7395 159.107 46.2844 158.811 45.9874C158.81 45.9873 158.81 45.9872 158.81 45.987L157.415 44.5799Z" fill="url(#paint9_linear_2601_1257)" stroke="#171423" stroke-width="0.5"/>
<path d="M151.791 50.2026L151.79 50.2023C146.863 45.2951 138.876 45.2946 133.968 50.2026L132.556 51.6144C132.259 51.9115 132.259 52.367 132.556 52.6641L135.379 55.4877L135.389 55.4974L135.4 55.5061C135.517 55.5999 135.693 55.7017 135.892 55.7017C136.108 55.7017 136.281 55.6102 136.409 55.5023L136.417 55.4953L136.425 55.4877L137.837 54.0759C139.183 52.7299 140.965 51.9825 142.865 51.9825C144.785 51.9825 146.572 52.7302 147.917 54.0759L149.329 55.4877C149.474 55.6324 149.671 55.6958 149.854 55.6958C150.037 55.6958 150.234 55.6324 150.379 55.4877L153.202 52.6641L153.203 52.6643L153.211 52.6547C153.472 52.3654 153.485 51.8967 153.202 51.6144L151.791 50.2026Z" fill="url(#paint10_linear_2601_1257)" stroke="#171423" stroke-width="0.5"/>
<path d="M138.571 58.7035L138.571 58.7036L138.574 58.7065L142.333 62.4647L142.342 62.4745L142.353 62.4831C142.476 62.581 142.651 62.6787 142.865 62.6787C143.081 62.6787 143.253 62.5872 143.382 62.4794L143.39 62.4724L143.398 62.4647L147.176 58.6869C147.364 58.499 147.419 58.2264 147.36 57.9761C147.177 57.1973 146.776 56.44 146.168 55.8274C144.419 54.0589 141.339 54.0588 139.591 55.8271C138.987 56.4307 138.542 57.2073 138.389 58.0202C138.335 58.2535 138.39 58.5165 138.571 58.7035Z" fill="url(#paint11_linear_2601_1257)" stroke="#171423" stroke-width="0.5"/>
</g>
<g filter="url(#filter6_d_2601_1257)">
<path d="M157.237 44.756C155.309 42.8476 153.084 41.3576 150.659 40.3642C148.235 39.3514 145.591 38.8156 142.869 38.8156C137.445 38.8156 132.357 40.943 128.521 44.7599L127.109 46.1717C126.929 46.3712 126.929 46.6879 127.109 46.8678L129.932 49.6914C130.03 49.7892 130.151 49.8322 130.288 49.8322C130.406 49.8322 130.527 49.7931 130.625 49.6914L132.036 48.2718C134.919 45.3896 138.775 43.7783 142.869 43.7783C146.983 43.7783 150.82 45.3896 153.722 48.2718L155.133 49.6836C155.313 49.8635 155.63 49.8635 155.829 49.6836L158.633 46.86C158.833 46.6606 158.833 46.3634 158.633 46.1639L157.237 44.756Z" fill="url(#paint12_linear_2601_1257)"/>
</g>
<g filter="url(#filter7_d_2601_1257)">
<path d="M151.614 50.3795C146.784 45.5693 138.955 45.5693 134.144 50.3795L132.733 51.7913C132.533 51.9907 132.533 52.288 132.733 52.4874L135.556 55.311C135.654 55.3892 135.775 55.4518 135.892 55.4518C136.033 55.4518 136.151 55.3931 136.248 55.311L137.66 53.8992C139.052 52.507 140.898 51.7326 142.865 51.7326C144.852 51.7326 146.702 52.507 148.094 53.8992L149.506 55.311C149.686 55.4909 150.022 55.4909 150.202 55.311L153.026 52.4874C153.205 52.288 153.205 51.9712 153.026 51.7913L151.614 50.3795Z" fill="url(#paint13_linear_2601_1257)"/>
</g>
<g filter="url(#filter8_d_2601_1257)">
<path d="M145.99 56.0035C144.34 54.3336 141.418 54.3336 139.768 56.0035C139.193 56.5784 138.775 57.3136 138.634 58.0723C138.595 58.2326 138.634 58.4086 138.751 58.5298L142.509 62.2881C142.607 62.3663 142.728 62.4289 142.865 62.4289C143.006 62.4289 143.123 62.3702 143.221 62.2881L146.999 58.5103C147.116 58.393 147.159 58.2131 147.116 58.0332C146.944 57.298 146.565 56.5823 145.99 56.0035Z" fill="url(#paint14_linear_2601_1257)"/>
</g>
<defs>
<filter id="filter0_d_2601_1257" x="48.5" y="8" width="188" height="88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.101961 0 0 0 0 0.968627 0 0 0 0 0.662745 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2601_1257"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2601_1257" result="shape"/>
</filter>
<filter id="filter1_d_2601_1257" x="44.5" y="4" width="196" height="96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.101961 0 0 0 0 0.968627 0 0 0 0 0.662745 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2601_1257"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2601_1257" result="shape"/>
</filter>
<filter id="filter2_d_2601_1257" x="40.5" y="0" width="204" height="104" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.101961 0 0 0 0 0.968627 0 0 0 0 0.662745 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2601_1257"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2601_1257" result="shape"/>
</filter>
<filter id="filter3_d_2601_1257" x="138.75" y="80" width="8.5" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.101961 0 0 0 0 0.968627 0 0 0 0 0.662745 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2601_1257"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2601_1257" result="shape"/>
</filter>
<filter id="filter4_d_2601_1257" x="138.75" y="8.5" width="8.5" height="15.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.101961 0 0 0 0 0.968627 0 0 0 0 0.662745 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2601_1257"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2601_1257" result="shape"/>
</filter>
<filter id="filter5_f_2601_1257" x="112.76" y="24.6012" width="60.2377" height="52.0418" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6.85714" result="effect1_foregroundBlur_2601_1257"/>
</filter>
<filter id="filter6_d_2601_1257" x="124.688" y="37.6727" width="36.3805" height="15.5881" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.14286"/>
<feGaussianBlur stdDeviation="1.14286"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2601_1257"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2601_1257" result="shape"/>
</filter>
<filter id="filter7_d_2601_1257" x="130.297" y="45.629" width="25.1488" height="13.2514" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.14286"/>
<feGaussianBlur stdDeviation="1.14286"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2601_1257"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2601_1257" result="shape"/>
</filter>
<filter id="filter8_d_2601_1257" x="136.333" y="53.6082" width="13.0849" height="12.2492" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.14286"/>
<feGaussianBlur stdDeviation="1.14286"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2601_1257"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2601_1257" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2601_1257" x1="232.5" y1="10.9999" x2="50.8483" y2="14.9449" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AF7A9"/>
<stop offset="1" stop-color="#08CDDA"/>
</linearGradient>
<linearGradient id="paint1_linear_2601_1257" x1="236.5" y1="6.99991" x2="46.7661" y2="10.9123" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AF7A9"/>
<stop offset="1" stop-color="#08CDDA"/>
</linearGradient>
<linearGradient id="paint2_linear_2601_1257" x1="240.5" y1="2.9999" x2="42.685" y2="6.89816" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AF7A9"/>
<stop offset="1" stop-color="#08CDDA"/>
</linearGradient>
<linearGradient id="paint3_linear_2601_1257" x1="174.5" y1="18.9999" x2="109.888" y2="19.6236" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AF7A9"/>
<stop offset="1" stop-color="#08CDDA"/>
</linearGradient>
<linearGradient id="paint4_linear_2601_1257" x1="43.8961" y1="15.5325" x2="64.1558" y2="70.9091" gradientUnits="userSpaceOnUse">
<stop stop-color="#1D1C2B"/>
<stop offset="1" stop-color="#2C2B3D"/>
</linearGradient>
<linearGradient id="paint5_linear_2601_1257" x1="124.214" y1="29.2857" x2="124.214" y2="33.8571" gradientUnits="userSpaceOnUse">
<stop stop-color="#2C2B3C"/>
<stop offset="1" stop-color="#383747"/>
</linearGradient>
<linearGradient id="paint6_linear_2601_1257" x1="124.214" y1="68.1429" x2="124.214" y2="72.7143" gradientUnits="userSpaceOnUse">
<stop stop-color="#2C2B3C"/>
<stop offset="1" stop-color="#383747"/>
</linearGradient>
<linearGradient id="paint7_linear_2601_1257" x1="160.786" y1="29.2857" x2="160.786" y2="33.8571" gradientUnits="userSpaceOnUse">
<stop stop-color="#2C2B3C"/>
<stop offset="1" stop-color="#383747"/>
</linearGradient>
<linearGradient id="paint8_linear_2601_1257" x1="160.786" y1="68.1429" x2="160.786" y2="72.7143" gradientUnits="userSpaceOnUse">
<stop stop-color="#2C2B3C"/>
<stop offset="1" stop-color="#383747"/>
</linearGradient>
<linearGradient id="paint9_linear_2601_1257" x1="158.783" y1="38.8155" x2="126.692" y2="39.7099" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AF7A9"/>
<stop offset="1" stop-color="#08CDDA"/>
</linearGradient>
<linearGradient id="paint10_linear_2601_1257" x1="153.16" y1="46.7717" x2="132.395" y2="47.2469" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AF7A9"/>
<stop offset="1" stop-color="#08CDDA"/>
</linearGradient>
<linearGradient id="paint11_linear_2601_1257" x1="147.133" y1="54.7509" x2="138.538" y2="54.8429" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AF7A9"/>
<stop offset="1" stop-color="#08CDDA"/>
</linearGradient>
<linearGradient id="paint12_linear_2601_1257" x1="158.783" y1="38.8156" x2="126.692" y2="39.7099" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AF7A9"/>
<stop offset="1" stop-color="#08CDDA"/>
</linearGradient>
<linearGradient id="paint13_linear_2601_1257" x1="153.16" y1="46.7718" x2="132.395" y2="47.247" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AF7A9"/>
<stop offset="1" stop-color="#08CDDA"/>
</linearGradient>
<linearGradient id="paint14_linear_2601_1257" x1="147.133" y1="54.7511" x2="138.538" y2="54.843" gradientUnits="userSpaceOnUse">
<stop stop-color="#1AF7A9"/>
<stop offset="1" stop-color="#08CDDA"/>
</linearGradient>
</defs>
</svg>
